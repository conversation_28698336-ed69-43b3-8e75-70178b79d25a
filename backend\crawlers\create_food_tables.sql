-- 美食数据爬取系统 - 数据库表结构
-- 支持广西、广东、海南各市美食数据存储

-- 地区信息表
CREATE TABLE IF NOT EXISTS regions (
    region_id INT AUTO_INCREMENT PRIMARY KEY,
    province VARCHAR(50) NOT NULL COMMENT '省份',
    city VARCHAR(100) NOT NULL COMMENT '城市',
    district VARCHAR(100) COMMENT '区县',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_region (province, city, district)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='地区信息表';

-- 餐厅信息表
CREATE TABLE IF NOT EXISTS restaurants (
    restaurant_id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(200) NOT NULL COMMENT '餐厅名称',
    region_id INT COMMENT '地区ID',
    address TEXT COMMENT '详细地址',
    phone VARCHAR(50) COMMENT '联系电话',
    latitude DECIMAL(10,7) COMMENT '纬度',
    longitude DECIMAL(11,7) COMMENT '经度',
    business_hours VARCHAR(200) COMMENT '营业时间',
    avg_price DECIMAL(8,2) COMMENT '人均消费',
    rating DECIMAL(3,2) COMMENT '评分',
    review_count INT DEFAULT 0 COMMENT '评价数量',
    restaurant_type VARCHAR(100) COMMENT '餐厅类型',
    environment_score DECIMAL(3,2) COMMENT '环境评分',
    service_score DECIMAL(3,2) COMMENT '服务评分',
    taste_score DECIMAL(3,2) COMMENT '口味评分',
    source_platform VARCHAR(50) COMMENT '数据来源平台',
    source_url TEXT COMMENT '来源URL',
    image_urls JSON COMMENT '餐厅图片URLs',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (region_id) REFERENCES regions(region_id),
    INDEX idx_region (region_id),
    INDEX idx_rating (rating),
    INDEX idx_source (source_platform)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='餐厅信息表';

-- 美食菜品表（扩展版）
CREATE TABLE IF NOT EXISTS dishes_extended (
    dish_id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(200) NOT NULL COMMENT '菜品名称',
    restaurant_id INT COMMENT '餐厅ID',
    region_id INT COMMENT '地区ID',
    cuisine_type VARCHAR(100) COMMENT '菜系类型',
    category VARCHAR(100) COMMENT '菜品分类',
    price DECIMAL(8,2) COMMENT '价格',
    rating DECIMAL(3,2) COMMENT '评分',
    review_count INT DEFAULT 0 COMMENT '评价数量',
    description TEXT COMMENT '菜品描述',
    ingredients TEXT COMMENT '主要食材',
    cooking_method VARCHAR(100) COMMENT '烹饪方法',
    taste_tags JSON COMMENT '口味标签',
    nutritional_info JSON COMMENT '营养信息',
    allergen_info VARCHAR(200) COMMENT '过敏原信息',
    spice_level TINYINT COMMENT '辣度等级(1-5)',
    difficulty_level TINYINT COMMENT '制作难度(1-5)',
    cooking_time INT COMMENT '制作时间(分钟)',
    calories_per_serving INT COMMENT '每份卡路里',
    image_url TEXT COMMENT '主图片URL',
    image_urls JSON COMMENT '多张图片URLs',
    video_url TEXT COMMENT '制作视频URL',
    recipe TEXT COMMENT '制作方法',
    cultural_background TEXT COMMENT '文化背景',
    seasonal_availability VARCHAR(100) COMMENT '时令性',
    popularity_score DECIMAL(5,2) COMMENT '受欢迎程度',
    source_platform VARCHAR(50) COMMENT '数据来源平台',
    source_url TEXT COMMENT '来源URL',
    crawl_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '爬取时间',
    is_verified BOOLEAN DEFAULT FALSE COMMENT '是否已验证',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (restaurant_id) REFERENCES restaurants(restaurant_id),
    FOREIGN KEY (region_id) REFERENCES regions(region_id),
    INDEX idx_name (name),
    INDEX idx_cuisine (cuisine_type),
    INDEX idx_category (category),
    INDEX idx_region (region_id),
    INDEX idx_rating (rating),
    INDEX idx_price (price),
    INDEX idx_source (source_platform),
    FULLTEXT idx_search (name, description, ingredients)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='美食菜品扩展表';

-- 用户评价表
CREATE TABLE IF NOT EXISTS dish_reviews (
    review_id INT AUTO_INCREMENT PRIMARY KEY,
    dish_id INT NOT NULL,
    user_name VARCHAR(100) COMMENT '用户名',
    rating DECIMAL(3,2) COMMENT '评分',
    review_text TEXT COMMENT '评价内容',
    taste_rating DECIMAL(3,2) COMMENT '口味评分',
    presentation_rating DECIMAL(3,2) COMMENT '摆盘评分',
    value_rating DECIMAL(3,2) COMMENT '性价比评分',
    helpful_count INT DEFAULT 0 COMMENT '有用数',
    review_date TIMESTAMP COMMENT '评价时间',
    source_platform VARCHAR(50) COMMENT '来源平台',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (dish_id) REFERENCES dishes_extended(dish_id),
    INDEX idx_dish (dish_id),
    INDEX idx_rating (rating),
    INDEX idx_date (review_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='菜品评价表';

-- 数据源管理表
CREATE TABLE IF NOT EXISTS data_sources (
    source_id INT AUTO_INCREMENT PRIMARY KEY,
    platform_name VARCHAR(100) NOT NULL COMMENT '平台名称',
    platform_url VARCHAR(500) COMMENT '平台URL',
    api_endpoint VARCHAR(500) COMMENT 'API端点',
    last_crawl_time TIMESTAMP COMMENT '最后爬取时间',
    crawl_status ENUM('active', 'inactive', 'error') DEFAULT 'active',
    success_count INT DEFAULT 0 COMMENT '成功爬取数',
    error_count INT DEFAULT 0 COMMENT '错误次数',
    rate_limit_per_minute INT DEFAULT 60 COMMENT '每分钟请求限制',
    headers_config JSON COMMENT '请求头配置',
    proxy_config JSON COMMENT '代理配置',
    notes TEXT COMMENT '备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_platform (platform_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据源管理表';

-- 爬取任务表
CREATE TABLE IF NOT EXISTS crawl_tasks (
    task_id INT AUTO_INCREMENT PRIMARY KEY,
    task_name VARCHAR(200) NOT NULL COMMENT '任务名称',
    source_id INT COMMENT '数据源ID',
    region_id INT COMMENT '目标地区ID',
    task_type ENUM('dishes', 'restaurants', 'reviews') NOT NULL COMMENT '任务类型',
    status ENUM('pending', 'running', 'completed', 'failed', 'paused') DEFAULT 'pending',
    priority TINYINT DEFAULT 1 COMMENT '优先级(1-5)',
    start_time TIMESTAMP COMMENT '开始时间',
    end_time TIMESTAMP COMMENT '结束时间',
    items_found INT DEFAULT 0 COMMENT '发现项目数',
    items_processed INT DEFAULT 0 COMMENT '已处理项目数',
    items_success INT DEFAULT 0 COMMENT '成功处理数',
    items_failed INT DEFAULT 0 COMMENT '失败处理数',
    error_message TEXT COMMENT '错误信息',
    config JSON COMMENT '任务配置',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (source_id) REFERENCES data_sources(source_id),
    FOREIGN KEY (region_id) REFERENCES regions(region_id),
    INDEX idx_status (status),
    INDEX idx_type (task_type),
    INDEX idx_priority (priority)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='爬取任务表';

-- 初始化基础地区数据
INSERT IGNORE INTO regions (province, city, district) VALUES
-- 广东省主要城市
('广东', '广州', '天河区'),
('广东', '广州', '越秀区'),
('广东', '广州', '荔湾区'),
('广东', '广州', '海珠区'),
('广东', '深圳', '南山区'),
('广东', '深圳', '福田区'),
('广东', '深圳', '罗湖区'),
('广东', '深圳', '宝安区'),
('广东', '珠海', '香洲区'),
('广东', '佛山', '禅城区'),
('广东', '佛山', '南海区'),
('广东', '东莞', '莞城区'),
('广东', '中山', '石岐区'),
('广东', '江门', '蓬江区'),
('广东', '湛江', '霞山区'),
('广东', '茂名', '茂南区'),
('广东', '肇庆', '端州区'),
('广东', '惠州', '惠城区'),
('广东', '梅州', '梅江区'),
('广东', '汕头', '龙湖区'),
('广东', '河源', '源城区'),
('广东', '阳江', '江城区'),
('广东', '清远', '清城区'),
('广东', '韶关', '武江区'),
('广东', '揭阳', '榕城区'),
('广东', '潮州', '湘桥区'),

-- 广西壮族自治区主要城市  
('广西', '南宁', '青秀区'),
('广西', '南宁', '兴宁区'),
('广西', '南宁', '江南区'),
('广西', '柳州', '城中区'),
('广西', '柳州', '鱼峰区'),
('广西', '桂林', '秀峰区'),
('广西', '桂林', '叠彩区'),
('广西', '梧州', '万秀区'),
('广西', '北海', '海城区'),
('广西', '防城港', '港口区'),
('广西', '钦州', '钦南区'),
('广西', '贵港', '港北区'),
('广西', '玉林', '玉州区'),
('广西', '百色', '右江区'),
('广西', '贺州', '八步区'),
('广西', '河池', '金城江区'),
('广西', '来宾', '兴宾区'),
('广西', '崇左', '江州区'),

-- 海南省主要城市
('海南', '海口', '美兰区'),
('海南', '海口', '龙华区'),
('海南', '海口', '琼山区'),
('海南', '海口', '秀英区'),
('海南', '三亚', '天涯区'),
('海南', '三亚', '吉阳区'),
('海南', '三亚', '海棠区'),
('海南', '三亚', '崖州区'),
('海南', '儋州', '那大镇'),
('海南', '琼海', '嘉积镇'),
('海南', '文昌', '文城镇'),
('海南', '万宁', '万城镇'),
('海南', '五指山', '通什镇'),
('海南', '东方', '八所镇'),
('海南', '临高', '临城镇'),
('海南', '澄迈', '金江镇'),
('海南', '定安', '定城镇'),
('海南', '屯昌', '屯城镇'),
('海南', '昌江', '石碌镇'),
('海南', '乐东', '抱由镇'),
('海南', '陵水', '椰林镇'),
('海南', '保亭', '保城镇'),
('海南', '琼中', '营根镇');

-- 初始化数据源配置
INSERT IGNORE INTO data_sources (platform_name, platform_url, rate_limit_per_minute, notes) VALUES
('美团', 'https://www.meituan.com', 30, '美团外卖和到店餐饮数据'),
('大众点评', 'https://www.dianping.com', 30, '大众点评餐厅和菜品数据'),
('饿了么', 'https://www.ele.me', 30, '饿了么外卖数据'),
('下厨房', 'https://www.xiachufang.com', 60, '菜谱和制作方法数据'),
('豆果美食', 'https://www.douguo.com', 60, '菜谱和美食制作数据'),
('美食杰', 'https://www.meishij.net', 60, '菜谱制作和营养信息'),
('香哈菜谱', 'https://www.xiangha.com', 60, '地方特色菜谱数据'),
('好豆菜谱', 'https://www.haodou.com', 60, '家常菜谱和制作技巧');