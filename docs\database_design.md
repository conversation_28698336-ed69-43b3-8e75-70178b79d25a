# 数据库设计文档

## 概述

美食推荐系统数据库采用MySQL 8.0，支持utf8mb4字符集，包含用户管理、菜品信息、评价系统、订单管理等核心功能模块。

## 数据库架构

### 核心表结构

#### 1. 用户表 (users)
存储用户基本信息和偏好设置

| 字段名 | 类型 | 长度 | 约束 | 说明 |
|--------|------|------|------|------|
| id | INT | - | PRIMARY KEY, AUTO_INCREMENT | 用户ID |
| username | VARCHAR | 80 | NOT NULL, UNIQUE | 用户名 |
| email | VARCHAR | 120 | NOT NULL, UNIQUE | 邮箱地址 |
| password_hash | VARCHAR | 255 | NOT NULL | 密码哈希值 |
| phone | VARCHAR | 20 | NULL | 手机号码 |
| avatar | VARCHAR | 255 | NULL | 头像URL |
| preferred_cuisine | VARCHAR | 100 | NULL | 偏好菜系 |
| price_range | VARCHAR | 20 | NULL | 价格偏好范围 |
| taste_preference | TEXT | - | NULL | 口味偏好JSON |
| created_at | DATETIME | - | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | - | ON UPDATE CURRENT_TIMESTAMP | 更新时间 |
| last_login | DATETIME | - | NULL | 最后登录时间 |

**索引设计:**
- PRIMARY KEY (id)
- UNIQUE KEY (username)
- UNIQUE KEY (email)
- INDEX (created_at)

#### 2. 菜品表 (dishes)
存储菜品详细信息和统计数据

| 字段名 | 类型 | 长度 | 约束 | 说明 |
|--------|------|------|------|------|
| id | INT | - | PRIMARY KEY, AUTO_INCREMENT | 菜品ID |
| name | VARCHAR | 100 | NOT NULL | 菜品名称 |
| description | TEXT | - | NULL | 菜品描述 |
| cuisine_type | VARCHAR | 50 | NULL | 菜系类型 |
| region | VARCHAR | 50 | NULL | 地区 |
| price | DECIMAL | 10,2 | NULL | 价格 |
| price_range | VARCHAR | 20 | NULL | 价格区间 |
| image_url | VARCHAR | 255 | NULL | 主图片URL |
| images | TEXT | - | NULL | 多张图片JSON |
| calories | INT | - | NULL | 卡路里 |
| ingredients | TEXT | - | NULL | 主要食材 |
| cooking_method | VARCHAR | 50 | NULL | 烹饪方法 |
| taste_tags | TEXT | - | NULL | 口味标签JSON |
| spicy_level | INT | - | NULL | 辣度等级(0-5) |
| rating | FLOAT | - | DEFAULT 0.0 | 平均评分 |
| review_count | INT | - | DEFAULT 0 | 评价数量 |
| order_count | INT | - | DEFAULT 0 | 订购次数 |
| source_platform | VARCHAR | 50 | NULL | 数据来源平台 |
| source_url | VARCHAR | 255 | NULL | 原始链接 |
| is_active | BOOLEAN | - | DEFAULT TRUE | 是否有效 |
| created_at | DATETIME | - | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | - | ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**索引设计:**
- PRIMARY KEY (id)
- INDEX (name)
- INDEX (cuisine_type)
- INDEX (region)
- INDEX (rating)
- INDEX (price)
- INDEX (source_platform)

#### 3. 评价表 (reviews)
存储用户对菜品的评价信息

| 字段名 | 类型 | 长度 | 约束 | 说明 |
|--------|------|------|------|------|
| id | INT | - | PRIMARY KEY, AUTO_INCREMENT | 评价ID |
| user_id | INT | - | NOT NULL, FOREIGN KEY | 用户ID |
| dish_id | INT | - | NOT NULL, FOREIGN KEY | 菜品ID |
| rating | INT | - | NOT NULL | 评分(1-5) |
| title | VARCHAR | 100 | NULL | 评价标题 |
| content | TEXT | - | NULL | 评价内容 |
| taste_rating | INT | - | NULL | 口味评分(1-5) |
| price_rating | INT | - | NULL | 性价比评分(1-5) |
| service_rating | INT | - | NULL | 服务评分(1-5) |
| tags | TEXT | - | NULL | 评价标签JSON |
| is_recommended | BOOLEAN | - | DEFAULT TRUE | 是否推荐 |
| is_verified | BOOLEAN | - | DEFAULT FALSE | 是否已验证 |
| created_at | DATETIME | - | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | - | ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**索引设计:**
- PRIMARY KEY (id)
- FOREIGN KEY (user_id) REFERENCES users(id)
- FOREIGN KEY (dish_id) REFERENCES dishes(id)
- INDEX (user_id)
- INDEX (dish_id)
- INDEX (rating)
- INDEX (created_at)

#### 4. 订单表 (orders)
存储用户订单信息

| 字段名 | 类型 | 长度 | 约束 | 说明 |
|--------|------|------|------|------|
| id | INT | - | PRIMARY KEY, AUTO_INCREMENT | 订单ID |
| user_id | INT | - | NOT NULL, FOREIGN KEY | 用户ID |
| order_no | VARCHAR | 50 | NOT NULL, UNIQUE | 订单号 |
| total_amount | DECIMAL | 10,2 | NULL | 订单总金额 |
| status | VARCHAR | 20 | DEFAULT 'pending' | 订单状态 |
| order_time | DATETIME | - | DEFAULT CURRENT_TIMESTAMP | 下单时间 |
| created_at | DATETIME | - | DEFAULT CURRENT_TIMESTAMP | 创建时间 |

**索引设计:**
- PRIMARY KEY (id)
- FOREIGN KEY (user_id) REFERENCES users(id)
- UNIQUE KEY (order_no)
- INDEX (user_id)
- INDEX (status)
- INDEX (order_time)

#### 5. 订单项表 (order_items)
存储订单中的具体菜品信息

| 字段名 | 类型 | 长度 | 约束 | 说明 |
|--------|------|------|------|------|
| id | INT | - | PRIMARY KEY, AUTO_INCREMENT | 订单项ID |
| order_id | INT | - | NOT NULL, FOREIGN KEY | 订单ID |
| dish_id | INT | - | NOT NULL, FOREIGN KEY | 菜品ID |
| quantity | INT | - | DEFAULT 1 | 数量 |
| price | DECIMAL | 10,2 | NULL | 单价 |

**索引设计:**
- PRIMARY KEY (id)
- FOREIGN KEY (order_id) REFERENCES orders(id)
- FOREIGN KEY (dish_id) REFERENCES dishes(id)
- INDEX (order_id)
- INDEX (dish_id)

## 数据关系图

```
users (1) -----> (N) reviews (N) <----- (1) dishes
  |                                        ^
  |                                        |
  v                                        |
orders (1) -----> (N) order_items (N) ----+
```

## 性能优化策略

### 1. 索引优化
- 为高频查询字段创建索引
- 复合索引优化多条件查询
- 定期分析索引使用情况

### 2. 分区策略
- 按时间分区存储历史数据
- 按地区分区存储菜品数据

### 3. 缓存策略
- Redis缓存热门菜品数据
- 缓存用户推荐结果
- 缓存统计数据

### 4. 查询优化
- 避免全表扫描
- 使用LIMIT限制结果集
- 优化JOIN查询

## 数据安全

### 1. 数据加密
- 用户密码使用bcrypt加密
- 敏感信息字段加密存储

### 2. 访问控制
- 数据库用户权限最小化
- API接口权限验证

### 3. 备份策略
- 每日自动备份
- 异地备份存储
- 定期恢复测试

## 扩展性考虑

### 1. 水平扩展
- 读写分离
- 分库分表策略

### 2. 垂直扩展
- 硬件资源升级
- 数据库参数优化

### 3. 监控告警
- 性能监控
- 异常告警
- 容量预警