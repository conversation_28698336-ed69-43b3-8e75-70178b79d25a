#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接插入广西、广东、海南地区美食数据
使用现有数据库结构
"""

import pymysql
import json
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 广西、广东、海南特色美食数据
REGIONAL_FOOD_DATA = [
    # 广东美食
    {
        'name': '广式早茶点心',
        'cuisine_type': '粤菜',
        'price': 25.0,
        'rating': 4.6,
        'review_count': 342,
        'description': '传统广式早茶，包含虾饺、烧卖、叉烧包等经典点心，体现粤菜精致文化',
        'image_url': 'https://images.unsplash.com/photo-1496116218417-1a781b1c416c?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
        'restaurant_name': '广州酒家',
        'region': '广东',
        'tags': ['精致', '传统', '早茶'],
        'data_source': 'enhanced'
    },
    {
        'name': '深圳海鲜大餐',
        'cuisine_type': '粤菜',
        'price': 188.0,
        'rating': 4.5,
        'review_count': 256,
        'description': '新鲜海鲜配以粤式烹饪，清蒸石斑鱼、白灼虾、蒜蓉扇贝等',
        'image_url': 'https://images.unsplash.com/photo-1571026073021-9e3e30d63641?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
        'restaurant_name': '深圳海鲜城',
        'region': '广东',
        'tags': ['海鲜', '新鲜', '粤式'],
        'data_source': 'enhanced'
    },
    {
        'name': '潮汕牛肉火锅',
        'cuisine_type': '潮菜',
        'price': 98.0,
        'rating': 4.7,
        'review_count': 445,
        'description': '潮汕特色牛肉火锅，手切鲜牛肉片，配以特制沙茶酱',
        'image_url': 'https://images.unsplash.com/photo-1582878826629-29b7ad1cdc43?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
        'restaurant_name': '潮汕牛肉店',
        'region': '广东',
        'tags': ['火锅', '牛肉', '潮汕'],
        'data_source': 'enhanced'
    },
    {
        'name': '佛跳墙',
        'cuisine_type': '粤菜',
        'price': 288.0,
        'rating': 4.8,
        'review_count': 167,
        'description': '广东版佛跳墙，选用鲍鱼、海参、花胶等珍贵食材，营养丰富',
        'image_url': 'https://images.unsplash.com/photo-1547592166-23ac45744acd?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
        'restaurant_name': '广东大酒楼',
        'region': '广东',
        'tags': ['名菜', '滋补', '高档'],
        'data_source': 'enhanced'
    },
    {
        'name': '广式腊肠煲仔饭',
        'cuisine_type': '粤菜',
        'price': 32.0,
        'rating': 4.4,
        'review_count': 289,
        'description': '传统煲仔饭，广式腊肠配香米，底部有香脆锅巴',
        'image_url': 'https://images.unsplash.com/photo-1512058564366-18510be2db19?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
        'restaurant_name': '老广茶餐厅',
        'region': '广东',
        'tags': ['煲仔饭', '腊肠', '香脆'],
        'data_source': 'enhanced'
    },

    # 广西美食
    {
        'name': '正宗桂林米粉',
        'cuisine_type': '桂菜',
        'price': 15.0,
        'rating': 4.5,
        'review_count': 523,
        'description': '桂林特色米粉，汤底鲜美，配菜丰富，是广西最具代表性的小吃',
        'image_url': 'https://images.unsplash.com/photo-1569718212165-3a8278d5f624?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
        'restaurant_name': '桂林米粉店',
        'region': '广西',
        'tags': ['特色', '实惠', '传统'],
        'data_source': 'enhanced'
    },
    {
        'name': '柳州螺蛳粉',
        'cuisine_type': '桂菜',
        'price': 18.0,
        'rating': 4.3,
        'review_count': 678,
        'description': '柳州特色螺蛳粉，酸辣鲜香，独特的螺蛳汤底配红油辣椒',
        'image_url': 'https://images.unsplash.com/photo-1569718319928-1de4ba1d69f2?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
        'restaurant_name': '柳州螺蛳粉馆',
        'region': '广西',
        'tags': ['酸辣', '特色', '网红'],
        'data_source': 'enhanced'
    },
    {
        'name': '南宁老友粉',
        'cuisine_type': '桂菜',
        'price': 16.0,
        'rating': 4.4,
        'review_count': 334,
        'description': '南宁传统老友粉，酸笋、豆豉配河粉，酸辣开胃',
        'image_url': 'https://images.unsplash.com/photo-1569718212165-3a8278d5f624?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
        'restaurant_name': '南宁老友粉店',
        'region': '广西',
        'tags': ['酸辣', '开胃', '传统'],
        'data_source': 'enhanced'
    },
    {
        'name': '广西柠檬鸭',
        'cuisine_type': '桂菜',
        'price': 68.0,
        'rating': 4.6,
        'review_count': 245,
        'description': '广西特色柠檬鸭，鸭肉配新鲜柠檬，酸甜解腥，口感独特',
        'image_url': 'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
        'restaurant_name': '广西风味馆',
        'region': '广西',
        'tags': ['柠檬', '特色', '酸甜'],
        'data_source': 'enhanced'
    },
    {
        'name': '桂林啤酒鱼',
        'cuisine_type': '桂菜',
        'price': 58.0,
        'rating': 4.5,
        'review_count': 389,
        'description': '桂林阳朔特色啤酒鱼，鲜鱼配啤酒烹制，鱼肉鲜嫩，汤汁浓郁',
        'image_url': 'https://images.unsplash.com/photo-1563379091339-03246963d51a?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
        'restaurant_name': '阳朔啤酒鱼',
        'region': '广西',
        'tags': ['啤酒', '鲜鱼', '桂林'],
        'data_source': 'enhanced'
    },
    {
        'name': '广西酸野',
        'cuisine_type': '桂菜',
        'price': 12.0,
        'rating': 4.2,
        'review_count': 456,
        'description': '广西传统小食酸野，各种腌制蔬果，酸甜开胃，夏日必备',
        'image_url': 'https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
        'restaurant_name': '街边酸野摊',
        'region': '广西',
        'tags': ['酸甜', '开胃', '小食'],
        'data_source': 'enhanced'
    },
    {
        'name': '桂林马蹄糕',
        'cuisine_type': '桂菜',
        'price': 8.0,
        'rating': 4.1,
        'review_count': 234,
        'description': '桂林传统甜品马蹄糕，口感Q弹，清甜不腻',
        'image_url': 'https://images.unsplash.com/photo-1551024506-0bccd828d307?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
        'restaurant_name': '桂林甜品店',
        'region': '广西',
        'tags': ['甜品', 'Q弹', '传统'],
        'data_source': 'enhanced'
    },

    # 海南美食
    {
        'name': '正宗海南鸡饭',
        'cuisine_type': '琼菜',
        'price': 28.0,
        'rating': 4.7,
        'review_count': 567,
        'description': '海南招牌菜，文昌鸡配香米饭，蘸特制蒜蓉辣椒酱',
        'image_url': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
        'restaurant_name': '海南鸡饭店',
        'region': '海南',
        'tags': ['招牌', '文昌鸡', '香米'],
        'data_source': 'enhanced'
    },
    {
        'name': '文昌鸡',
        'cuisine_type': '琼菜',
        'price': 88.0,
        'rating': 4.8,
        'review_count': 342,
        'description': '海南四大名菜之首，文昌鸡肉质细嫩，营养丰富',
        'image_url': 'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
        'restaurant_name': '文昌人家',
        'region': '海南',
        'tags': ['名菜', '细嫩', '营养'],
        'data_source': 'enhanced'
    },
    {
        'name': '加积鸭',
        'cuisine_type': '琼菜',
        'price': 98.0,
        'rating': 4.6,
        'review_count': 278,
        'description': '海南四大名菜之一，加积鸭皮薄肉厚，香味浓郁',
        'image_url': 'https://images.unsplash.com/photo-1574484284002-952d92456975?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
        'restaurant_name': '琼海美食',
        'region': '海南',
        'tags': ['名菜', '香味', '皮薄'],
        'data_source': 'enhanced'
    },
    {
        'name': '东山羊',
        'cuisine_type': '琼菜',
        'price': 128.0,
        'rating': 4.7,
        'review_count': 234,
        'description': '海南四大名菜之一，东山羊肉质鲜美，营养价值高',
        'image_url': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
        'restaurant_name': '东山羊庄',
        'region': '海南',
        'tags': ['名菜', '鲜美', '营养'],
        'data_source': 'enhanced'
    },
    {
        'name': '和乐蟹',
        'cuisine_type': '琼菜',
        'price': 168.0,
        'rating': 4.9,
        'review_count': 189,
        'description': '海南四大名菜之一，和乐蟹膏满肉肥，是海南海鲜的代表',
        'image_url': 'https://images.unsplash.com/photo-1571026073021-9e3e30d63641?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
        'restaurant_name': '海南海鲜楼',
        'region': '海南',
        'tags': ['名菜', '海鲜', '膏肥'],
        'data_source': 'enhanced'
    },
    {
        'name': '椰子饭',
        'cuisine_type': '琼菜',
        'price': 25.0,
        'rating': 4.4,
        'review_count': 456,
        'description': '海南特色椰子饭，椰香浓郁，香甜可口，极具热带风情',
        'image_url': 'https://images.unsplash.com/photo-1512058564366-18510be2db19?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
        'restaurant_name': '椰风海韵',
        'region': '海南',
        'tags': ['椰香', '热带', '香甜'],
        'data_source': 'enhanced'
    },
    {
        'name': '清补凉',
        'cuisine_type': '琼菜',
        'price': 15.0,
        'rating': 4.3,
        'review_count': 567,
        'description': '海南传统甜品清补凉，椰奶配各种配料，清热解暑',
        'image_url': 'https://images.unsplash.com/photo-1551024506-0bccd828d307?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
        'restaurant_name': '海南甜品铺',
        'region': '海南',
        'tags': ['甜品', '清热', '椰奶'],
        'data_source': 'enhanced'
    },
    {
        'name': '海南抱罗粉',
        'cuisine_type': '琼菜',
        'price': 18.0,
        'rating': 4.2,
        'review_count': 345,
        'description': '海南特色米粉，汤清味浓，配菜丰富，是文昌的传统小吃',
        'image_url': 'https://images.unsplash.com/photo-1569718212165-3a8278d5f624?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
        'restaurant_name': '文昌小吃店',
        'region': '海南',
        'tags': ['米粉', '清汤', '传统'],
        'data_source': 'enhanced'
    },

    # 更多广东城市特色
    {
        'name': '顺德双皮奶',
        'cuisine_type': '粤菜',
        'price': 12.0,
        'rating': 4.5,
        'review_count': 678,
        'description': '顺德经典甜品双皮奶，奶香浓郁，口感顺滑',
        'image_url': 'https://images.unsplash.com/photo-1551024506-0bccd828d307?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
        'restaurant_name': '顺德甜品店',
        'region': '广东',
        'tags': ['甜品', '奶香', '顺滑'],
        'data_source': 'enhanced'
    },
    {
        'name': '东莞腊肠',
        'cuisine_type': '粤菜',
        'price': 35.0,
        'rating': 4.4,
        'review_count': 234,
        'description': '东莞特产腊肠，肥瘦相间，香味浓郁，是广东人餐桌必备',
        'image_url': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
        'restaurant_name': '东莞特产店',
        'region': '广东',
        'tags': ['腊肠', '特产', '香味'],
        'data_source': 'enhanced'
    }
]

def get_db_connection():
    """获取数据库连接"""
    return pymysql.connect(
        host='localhost',
        user='root',
        password='123456789',  # 使用正确的密码
        database='food_recommendation',
        charset='utf8mb4',
        autocommit=True
    )

def insert_food_data():
    """插入美食数据到数据库"""
    try:
        connection = get_db_connection()
        cursor = connection.cursor()
        
        logger.info("开始插入地区美食数据...")
        
        success_count = 0
        for food_item in REGIONAL_FOOD_DATA:
            try:
                # 检查是否已存在
                cursor.execute(
                    "SELECT dish_id FROM dishes WHERE name = %s AND data_source = %s",
                    (food_item['name'], food_item['data_source'])
                )
                
                if cursor.fetchone():
                    logger.info(f"菜品 {food_item['name']} 已存在，跳过")
                    continue
                
                # 插入新菜品
                sql = """
                INSERT INTO dishes (
                    name, cuisine_type, price, rating, review_count, description,
                    image_url, restaurant_name, region, tags, data_source, created_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                
                cursor.execute(sql, (
                    food_item['name'],
                    food_item['cuisine_type'],
                    food_item['price'],
                    food_item['rating'],
                    food_item['review_count'],
                    food_item['description'],
                    food_item['image_url'],
                    food_item['restaurant_name'],
                    food_item['region'],
                    json.dumps(food_item['tags'], ensure_ascii=False),
                    food_item['data_source'],
                    datetime.now()
                ))
                
                success_count += 1
                logger.info(f"✓ 成功插入菜品: {food_item['name']}")
                
            except Exception as e:
                logger.error(f"✗ 插入菜品 {food_item['name']} 失败: {e}")
        
        cursor.close()
        connection.close()
        
        logger.info(f"数据插入完成！成功插入 {success_count} 道菜品")
        return success_count
        
    except Exception as e:
        logger.error(f"数据库操作失败: {e}")
        return 0

def update_existing_mock_data():
    """更新现有的mock数据，添加地区标识"""
    try:
        connection = get_db_connection()
        cursor = connection.cursor()
        
        # 更新一些现有数据的地区信息
        region_updates = [
            ("白切鸡", "广东"),
            ("海南鸡饭", "海南"),
            ("口水鸡", "四川"),
            ("宫保鸡丁", "四川"),
            ("辣子鸡", "重庆"),
            ("兰州拉面", "甘肃"),
            ("担担面", "四川"),
            ("麻辣火锅", "四川"),
            ("水煮鱼", "四川"),
            ("清蒸鲈鱼", "广东")
        ]
        
        update_count = 0
        for dish_name, region in region_updates:
            try:
                cursor.execute(
                    "UPDATE dishes SET region = %s WHERE name = %s AND region IS NULL",
                    (region, dish_name)
                )
                if cursor.rowcount > 0:
                    update_count += 1
                    logger.info(f"✓ 更新 {dish_name} 的地区为 {region}")
            except Exception as e:
                logger.error(f"✗ 更新 {dish_name} 失败: {e}")
        
        cursor.close()
        connection.close()
        
        logger.info(f"更新完成！共更新 {update_count} 道菜品的地区信息")
        return update_count
        
    except Exception as e:
        logger.error(f"更新数据失败: {e}")
        return 0

def check_data_status():
    """检查数据状态"""
    try:
        connection = get_db_connection()
        cursor = connection.cursor()
        
        # 总菜品数
        cursor.execute("SELECT COUNT(*) FROM dishes")
        total_dishes = cursor.fetchone()[0]
        
        # 按地区统计
        cursor.execute("""
            SELECT region, COUNT(*) as count 
            FROM dishes 
            WHERE region IS NOT NULL 
            GROUP BY region 
            ORDER BY count DESC
        """)
        region_stats = cursor.fetchall()
        
        # 按菜系统计
        cursor.execute("""
            SELECT cuisine_type, COUNT(*) as count 
            FROM dishes 
            WHERE cuisine_type IS NOT NULL 
            GROUP BY cuisine_type 
            ORDER BY count DESC
            LIMIT 10
        """)
        cuisine_stats = cursor.fetchall()
        
        cursor.close()
        connection.close()
        
        logger.info("=" * 60)
        logger.info("数据库状态报告")
        logger.info("=" * 60)
        logger.info(f"菜品总数: {total_dishes}")
        logger.info("\n地区分布:")
        for region, count in region_stats:
            logger.info(f"  {region}: {count} 道菜品")
        logger.info("\n菜系分布:")
        for cuisine, count in cuisine_stats:
            logger.info(f"  {cuisine}: {count} 道菜品")
        logger.info("=" * 60)
        
        return total_dishes
        
    except Exception as e:
        logger.error(f"检查数据状态失败: {e}")
        return 0

def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("广西、广东、海南美食数据插入系统")
    logger.info("=" * 60)
    
    try:
        # 1. 插入新的地区美食数据
        success_count = insert_food_data()
        
        # 2. 更新现有数据的地区信息
        update_count = update_existing_mock_data()
        
        # 3. 检查数据状态
        total_dishes = check_data_status()
        
        logger.info("=" * 60)
        logger.info("任务完成总结:")
        logger.info(f"新增菜品: {success_count} 道")
        logger.info(f"更新菜品: {update_count} 道")
        logger.info(f"数据库总菜品: {total_dishes} 道")
        logger.info("=" * 60)
        
        if success_count > 0 or update_count > 0:
            logger.info("✓ 数据插入成功！前端现在可以搜索到更多地区美食了")
        else:
            logger.warning("⚠ 没有新数据插入，可能数据已存在")
            
    except Exception as e:
        logger.error(f"系统运行失败: {e}")

if __name__ == "__main__":
    main()