#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
食品图片匹配修复器 - 为每道菜配置正确的图片URL
"""

import json
import pymysql
import hashlib

class FoodImageMatcher:
    def __init__(self):
        # 菜品图片映射表 - 使用Unsplash的特定美食图片
        self.dish_image_mapping = {
            # 广东粤菜
            "白切鸡": "https://images.unsplash.com/photo-1512058564366-18510be2db19?w=400&h=300&fit=crop&auto=format&q=80",
            "蜜汁叉烧": "https://images.unsplash.com/photo-1569718212165-3a8278d5f624?w=400&h=300&fit=crop&auto=format&q=80",
            "广式烧鹅": "https://images.unsplash.com/photo-1518492104633-130d0cc84637?w=400&h=300&fit=crop&auto=format&q=80",
            "虾饺": "https://images.unsplash.com/photo-1496116218417-1a781b1c416c?w=400&h=300&fit=crop&auto=format&q=80",
            "叉烧包": "https://images.unsplash.com/photo-1563379091339-03246963d7d3?w=400&h=300&fit=crop&auto=format&q=80",
            "干炒牛河": "https://images.unsplash.com/photo-1512058564366-18510be2db19?w=400&h=300&fit=crop&auto=format&q=80",
            "白灼菜心": "https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=400&h=300&fit=crop&auto=format&q=80",
            "煲仔饭": "https://images.unsplash.com/photo-1603133872878-684f208fb84b?w=400&h=300&fit=crop&auto=format&q=80",
            "艇仔粥": "https://images.unsplash.com/photo-1551218808-94e220e084d2?w=400&h=300&fit=crop&auto=format&q=80",
            "糖醋里脊": "https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=300&fit=crop&auto=format&q=80",
            "蒜蓉蒸扇贝": "https://images.unsplash.com/photo-1615141982883-c7ad0e69fd62?w=400&h=300&fit=crop&auto=format&q=80",
            "红烧乳鸽": "https://images.unsplash.com/photo-1574484284002-952d92456975?w=400&h=300&fit=crop&auto=format&q=80",
            "潮汕牛肉丸": "https://images.unsplash.com/photo-1551218808-94e220e084d2?w=400&h=300&fit=crop&auto=format&q=80",
            "广式月饼": "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop&auto=format&q=80",
            "早茶点心": "https://images.unsplash.com/photo-1563379091339-03246963d7d3?w=400&h=300&fit=crop&auto=format&q=80",
            
            # 广西桂菜
            "螺蛳粉": "https://images.unsplash.com/photo-1569718212165-3a8278d5f624?w=400&h=300&fit=crop&auto=format&q=80",
            "桂林米粉": "https://images.unsplash.com/photo-1551218808-94e220e084d2?w=400&h=300&fit=crop&auto=format&q=80",
            "老友粉": "https://images.unsplash.com/photo-1569718212165-3a8278d5f624?w=400&h=300&fit=crop&auto=format&q=80",
            "白切狗肉": "https://images.unsplash.com/photo-1574484284002-952d92456975?w=400&h=300&fit=crop&auto=format&q=80",
            "荔浦芋扣肉": "https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=300&fit=crop&auto=format&q=80",
            "阳朔啤酒鱼": "https://images.unsplash.com/photo-1544943910-4c1dc44aab44?w=400&h=300&fit=crop&auto=format&q=80",
            "竹筒饭": "https://images.unsplash.com/photo-1603133872878-684f208fb84b?w=400&h=300&fit=crop&auto=format&q=80",
            "五色糯米饭": "https://images.unsplash.com/photo-1603133872878-684f208fb84b?w=400&h=300&fit=crop&auto=format&q=80",
            "马蹄糕": "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop&auto=format&q=80",
            "桂花糕": "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop&auto=format&q=80",
            "酸嘢": "https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=400&h=300&fit=crop&auto=format&q=80",
            "田螺鸭脚煲": "https://images.unsplash.com/photo-1551218808-94e220e084d2?w=400&h=300&fit=crop&auto=format&q=80",
            "生榨米粉": "https://images.unsplash.com/photo-1569718212165-3a8278d5f624?w=400&h=300&fit=crop&auto=format&q=80",
            "横县鱼生": "https://images.unsplash.com/photo-1544943910-4c1dc44aab44?w=400&h=300&fit=crop&auto=format&q=80",
            "宾阳酸粉": "https://images.unsplash.com/photo-1569718212165-3a8278d5f624?w=400&h=300&fit=crop&auto=format&q=80",
            
            # 海南琼菜
            "文昌鸡": "https://images.unsplash.com/photo-1512058564366-18510be2db19?w=400&h=300&fit=crop&auto=format&q=80",
            "加积鸭": "https://images.unsplash.com/photo-1518492104633-130d0cc84637?w=400&h=300&fit=crop&auto=format&q=80",
            "东山羊": "https://images.unsplash.com/photo-1574484284002-952d92456975?w=400&h=300&fit=crop&auto=format&q=80",
            "和乐蟹": "https://images.unsplash.com/photo-1615141982883-c7ad0e69fd62?w=400&h=300&fit=crop&auto=format&q=80",
            "椰子鸡": "https://images.unsplash.com/photo-1512058564366-18510be2db19?w=400&h=300&fit=crop&auto=format&q=80",
            "海南粉": "https://images.unsplash.com/photo-1569718212165-3a8278d5f624?w=400&h=300&fit=crop&auto=format&q=80",
            "抱罗粉": "https://images.unsplash.com/photo-1551218808-94e220e084d2?w=400&h=300&fit=crop&auto=format&q=80",
            "清补凉": "https://images.unsplash.com/photo-1551024506-0bccd828d307?w=400&h=300&fit=crop&auto=format&q=80",
            "椰子饭": "https://images.unsplash.com/photo-1603133872878-684f208fb84b?w=400&h=300&fit=crop&auto=format&q=80",
            "临高乳猪": "https://images.unsplash.com/photo-1574484284002-952d92456975?w=400&h=300&fit=crop&auto=format&q=80",
            "琼海嘉积鸭": "https://images.unsplash.com/photo-1518492104633-130d0cc84637?w=400&h=300&fit=crop&auto=format&q=80",
            "三亚海鲜": "https://images.unsplash.com/photo-1615141982883-c7ad0e69fd62?w=400&h=300&fit=crop&auto=format&q=80",
            "椰蓉球": "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop&auto=format&q=80",
            "海南鸡饭": "https://images.unsplash.com/photo-1603133872878-684f208fb84b?w=400&h=300&fit=crop&auto=format&q=80",
            "槟榔花鸡": "https://images.unsplash.com/photo-1512058564366-18510be2db19?w=400&h=300&fit=crop&auto=format&q=80",
            
            # 浙菜
            "西湖醋鱼": "https://i2.chuimg.com/040ff23e155e49708885279d32de9c99_1760w_1173h.jpg?imageView2/2/w/660/interlace/1/q/90"
        }
        
        # 数据库配置
        self.db_config = {
            'host': 'localhost',
            'user': 'root',
            'password': '123456789',
            'database': 'food_recommendation',
            'charset': 'utf8mb4',
            'cursorclass': pymysql.cursors.DictCursor
        }
    
    def get_connection(self):
        """获取数据库连接"""
        return pymysql.connect(**self.db_config)
    
    def get_dish_image_url(self, dish_name):
        """根据菜品名称获取对应的图片URL"""
        # 优先使用精确匹配
        if dish_name in self.dish_image_mapping:
            return self.dish_image_mapping[dish_name]
        
        # 模糊匹配
        for key, url in self.dish_image_mapping.items():
            if key in dish_name or dish_name in key:
                return url
        
        # 根据菜品类型返回默认图片
        if any(keyword in dish_name for keyword in ['鸡', '鸭', '鹅']):
            return "https://images.unsplash.com/photo-1512058564366-18510be2db19?w=400&h=300&fit=crop&auto=format&q=80"
        elif any(keyword in dish_name for keyword in ['鱼', '虾', '蟹', '海鲜']):
            return "https://images.unsplash.com/photo-1544943910-4c1dc44aab44?w=400&h=300&fit=crop&auto=format&q=80"
        elif any(keyword in dish_name for keyword in ['粉', '面', '米']):
            return "https://images.unsplash.com/photo-1569718212165-3a8278d5f624?w=400&h=300&fit=crop&auto=format&q=80"
        elif any(keyword in dish_name for keyword in ['肉', '排骨']):
            return "https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=300&fit=crop&auto=format&q=80"
        elif any(keyword in dish_name for keyword in ['饭', '粥']):
            return "https://images.unsplash.com/photo-1603133872878-684f208fb84b?w=400&h=300&fit=crop&auto=format&q=80"
        elif any(keyword in dish_name for keyword in ['菜', '蔬']):
            return "https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=400&h=300&fit=crop&auto=format&q=80"
        elif any(keyword in dish_name for keyword in ['糕', '饼', '甜品']):
            return "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop&auto=format&q=80"
        else:
            # 默认美食图片
            return "https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=300&fit=crop&auto=format&q=80"
    
    def update_dish_images(self):
        """更新所有菜品的图片URL"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # 获取所有菜品
            cursor.execute("SELECT dish_id, name FROM dishes")
            dishes = cursor.fetchall()
            
            print(f"开始更新 {len(dishes)} 道菜品的图片...")
            
            updated_count = 0
            for dish in dishes:
                dish_id = dish['dish_id']
                dish_name = dish['name']
                
                # 获取匹配的图片URL
                new_image_url = self.get_dish_image_url(dish_name)
                
                # 更新数据库
                cursor.execute(
                    "UPDATE dishes SET image_url = %s WHERE dish_id = %s",
                    (new_image_url, dish_id)
                )
                
                updated_count += 1
                if updated_count % 100 == 0:
                    print(f"已更新 {updated_count} 道菜品...")
            
            conn.commit()
            cursor.close()
            conn.close()
            
            print(f"图片更新完成！共更新 {updated_count} 道菜品")
            return True
            
        except Exception as e:
            print(f"更新图片失败: {e}")
            return False
    
    def update_restaurant_images(self):
        """更新餐厅图片"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # 餐厅图片映射
            restaurant_images = [
                "https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=600&h=400&fit=crop&auto=format&q=80",  # 餐厅内景
                "https://images.unsplash.com/photo-1466978913421-dad2ebd01d17?w=600&h=400&fit=crop&auto=format&q=80",  # 餐厅环境
                "https://images.unsplash.com/photo-1514933651103-005eec06c04b?w=600&h=400&fit=crop&auto=format&q=80",  # 餐厅氛围
                "https://images.unsplash.com/photo-1552566626-52f8b828add9?w=600&h=400&fit=crop&auto=format&q=80",  # 餐厅外观
                "https://images.unsplash.com/photo-1559329007-40df8a9345d8?w=600&h=400&fit=crop&auto=format&q=80",  # 中式餐厅
            ]
            
            cursor.execute("SELECT restaurant_id FROM restaurants")
            restaurants = cursor.fetchall()
            
            print(f"开始更新 {len(restaurants)} 家餐厅的图片...")
            
            for i, restaurant in enumerate(restaurants):
                restaurant_id = restaurant['restaurant_id']
                # 循环使用餐厅图片
                image_url = restaurant_images[i % len(restaurant_images)]
                
                cursor.execute(
                    "UPDATE restaurants SET image_url = %s WHERE restaurant_id = %s",
                    (image_url, restaurant_id)
                )
            
            conn.commit()
            cursor.close()
            conn.close()
            
            print(f"餐厅图片更新完成！")
            return True
            
        except Exception as e:
            print(f"更新餐厅图片失败: {e}")
            return False
    
    def verify_image_updates(self):
        """验证图片更新结果"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # 检查菜品图片
            cursor.execute("""
                SELECT name, image_url, COUNT(*) as count
                FROM dishes 
                GROUP BY name, image_url
                ORDER BY name
                LIMIT 10
            """)
            
            print("\n=== 菜品图片验证 ===")
            for row in cursor.fetchall():
                print(f"- {row['name']}: {row['image_url'][:60]}...")
            
            # 统计不同图片的使用情况
            cursor.execute("""
                SELECT image_url, COUNT(*) as usage_count
                FROM dishes 
                GROUP BY image_url
                ORDER BY usage_count DESC
            """)
            
            print(f"\n=== 图片使用统计 ===")
            for row in cursor.fetchall():
                print(f"使用 {row['usage_count']} 次: {row['image_url'][:60]}...")
            
            cursor.close()
            conn.close()
            
            return True
            
        except Exception as e:
            print(f"验证失败: {e}")
            return False
    
    def fix_all_images(self):
        """修复所有图片"""
        print("=" * 60)
        print("开始修复食品图片匹配问题")
        print("=" * 60)
        
        # 1. 更新菜品图片
        print("步骤1: 更新菜品图片...")
        if not self.update_dish_images():
            return False
        
        # 2. 更新餐厅图片
        print("\n步骤2: 更新餐厅图片...")
        if not self.update_restaurant_images():
            return False
        
        # 3. 验证更新结果
        print("\n步骤3: 验证更新结果...")
        if not self.verify_image_updates():
            return False
        
        print("\n" + "=" * 60)
        print("图片修复完成！")
        print("=" * 60)
        
        return True

def main():
    """主函数"""
    matcher = FoodImageMatcher()
    success = matcher.fix_all_images()
    
    if success:
        print("\n图片匹配修复成功！")
        print("现在每道菜品都有对应的正确图片了。")
    else:
        print("\n图片修复失败！")

if __name__ == "__main__":
    main()