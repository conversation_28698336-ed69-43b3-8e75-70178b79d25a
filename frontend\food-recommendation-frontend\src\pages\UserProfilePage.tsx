import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { getDishImageUrl, handleImageError } from '@/utils/imageUtils';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { 
  User, 
  Heart, 
  ShoppingBag, 
  Star, 
  Settings, 
  Camera, 
  MapPin, 
  Phone, 
  Mail,
  Calendar,
  Award,
  TrendingUp,
  Clock,
  Edit,
  Sparkles
} from 'lucide-react';

interface UserProfile {
  user_id: number;
  username: string;
  email: string;
  phone: string;
  avatar_url: string;
  created_at: string;
  last_login: string;
  bio: string;
  location: string;
  preferences: {
    preferred_cuisines: string[];
    dietary_restrictions: string[];
    price_range: string;
    spice_level: string;
  };
  statistics: {
    total_orders: number;
    total_reviews: number;
    favorite_dishes: number;
    points: number;
    level: string;
  };
}

interface FavoriteDish {
  dish_id: number;
  name: string;
  cuisine_type: string;
  price: number;
  rating: number;
  image_url: string;
  restaurant_name: string;
  favorited_at: string;
}

interface UserOrder {
  order_id: number;
  dishes: Array<{
    dish_id: number;
    name: string;
    quantity: number;
    price: number;
  }>;
  total_amount: number;
  status: string;
  order_time: string;
  restaurant_name: string;
}

interface UserReview {
  review_id: number;
  dish_id: number;
  dish_name: string;
  rating: number;
  comment: string;
  created_at: string;
  restaurant_name: string;
}

const UserProfilePage: React.FC = () => {
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const { toast } = useToast();
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [favorites, setFavorites] = useState<FavoriteDish[]>([]);
  const [orders, setOrders] = useState<UserOrder[]>([]);
  const [reviews, setReviews] = useState<UserReview[]>([]);
  const [loading, setLoading] = useState(true);
  const [editMode, setEditMode] = useState(false);
  const [editedProfile, setEditedProfile] = useState<Partial<UserProfile>>({});

  // 模拟用户数据
  const mockUserProfile: UserProfile = {
    user_id: 1,
    username: '美食爱好者小王',
    email: '<EMAIL>',
    phone: '13800138000',
    avatar_url: '/placeholder.svg?height=120&width=120',
    created_at: '2023-06-15 10:30:00',
    last_login: '2024-01-25 15:30:00',
    bio: '热爱美食，喜欢探索各地特色菜品，尤其偏爱粤菜和川菜。',
    location: '广州市',
    preferences: {
      preferred_cuisines: ['粤菜', '川菜', '湘菜'],
      dietary_restrictions: [],
      price_range: 'medium',
      spice_level: 'medium'
    },
    statistics: {
      total_orders: 25,
      total_reviews: 18,
      favorite_dishes: 12,
      points: 1250,
      level: '美食达人'
    }
  };

  const mockFavorites: FavoriteDish[] = [
    {
      dish_id: 1,
      name: '白切鸡',
      cuisine_type: '粤菜',
      price: 35.0,
      rating: 4.5,
      image_url: 'https://images.unsplash.com/photo-1604503468506-a8da13d82791?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      restaurant_name: '粤味轩',
      favorited_at: '2024-01-20 15:30:00'
    },
    {
      dish_id: 2,
      name: '麻婆豆腐',
      cuisine_type: '川菜',
      price: 28.0,
      rating: 4.3,
      image_url: 'https://images.unsplash.com/photo-1569718212165-3a8278d5f624?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      restaurant_name: '川味居',
      favorited_at: '2024-01-18 12:15:00'
    }
  ];

  const mockOrders: UserOrder[] = [
    {
      order_id: 1001,
      dishes: [
        { dish_id: 1, name: '白切鸡', quantity: 1, price: 35.0 },
        { dish_id: 2, name: '桂林米粉', quantity: 2, price: 25.0 }
      ],
      total_amount: 85.0,
      status: 'completed',
      order_time: '2024-01-20 12:30:00',
      restaurant_name: '粤味轩'
    }
  ];

  const mockReviews: UserReview[] = [
    {
      review_id: 1,
      dish_id: 1,
      dish_name: '白切鸡',
      rating: 5,
      comment: '非常正宗的白切鸡，肉质鲜嫩，蘸料调得很好！',
      created_at: '2024-01-20 18:30:00',
      restaurant_name: '粤味轩'
    }
  ];

  useEffect(() => {
    // 检查用户是否已登录
    if (!user) {
      toast({
        title: "请先登录",
        description: "您需要登录才能查看个人资料",
        variant: "destructive",
      });
      navigate('/');
      return;
    }

    const fetchUserData = async () => {
      setLoading(true);
      try {
        // 从后端获取用户详细信息
        const userResponse = await fetch(`http://localhost:5000/api/users/${user.user_id}`);
        let realUserProfile: UserProfile;
        
        if (userResponse.ok) {
          const userData = await userResponse.json();
          if (userData.success) {
            // 使用后端返回的用户数据
            realUserProfile = {
              user_id: userData.data.user_id,
              username: userData.data.username,
              email: userData.data.email,
              phone: userData.data.phone || '',
              avatar_url: userData.data.avatar_url || '/placeholder.svg?height=120&width=120',
              created_at: userData.data.created_at || new Date().toISOString().split('T')[0] + ' 10:30:00',
              last_login: userData.data.last_login || new Date().toISOString().split('T')[0] + ' 15:30:00',
              bio: userData.data.bio || '热爱美食，喜欢探索各地特色菜品。',
              location: userData.data.location || '广州市',
              preferences: userData.data.preferences || {
                preferred_cuisines: ['粤菜', '川菜'],
                dietary_restrictions: [],
                price_range: 'medium',
                spice_level: 'medium'
              },
              statistics: {
                total_orders: 0,
                total_reviews: 0,
                favorite_dishes: 0,
                points: 100,
                level: '美食新手'
              }
            };
          } else {
            throw new Error('获取用户信息失败');
          }
        } else {
          // 如果后端没有用户详细信息，使用基本信息创建默认资料
          realUserProfile = {
            user_id: user.user_id,
            username: user.username,
            email: user.email,
            phone: user.phone || '',
            avatar_url: user.avatar_url || '/placeholder.svg?height=120&width=120',
            created_at: new Date().toISOString().split('T')[0] + ' 10:30:00',
            last_login: new Date().toISOString().split('T')[0] + ' 15:30:00',
            bio: '热爱美食，喜欢探索各地特色菜品。',
            location: '广州市',
            preferences: {
              preferred_cuisines: ['粤菜', '川菜'],
              dietary_restrictions: [],
              price_range: 'medium',
              spice_level: 'medium'
            },
            statistics: {
              total_orders: 0,
              total_reviews: 0,
              favorite_dishes: 0,
              points: 100,
              level: '美食新手'
            }
          };
        }

        // 获取用户收藏列表
        const favoritesResponse = await fetch(`http://localhost:5000/api/users/${user.user_id}/favorites`);
        let userFavorites: FavoriteDish[] = [];
        
        if (favoritesResponse.ok) {
          const favoritesData = await favoritesResponse.json();
          if (favoritesData.success) {
            userFavorites = favoritesData.data;
            // 更新收藏数统计
            realUserProfile.statistics.favorite_dishes = userFavorites.length;
          }
        }

        setUserProfile(realUserProfile);
        setFavorites(userFavorites);
        setOrders(mockOrders); // 暂时使用模拟数据
        setReviews(mockReviews); // 暂时使用模拟数据
        setEditedProfile(realUserProfile);
        
      } catch (error) {
        console.error('获取用户数据失败:', error);
        toast({
          title: "加载失败",
          description: "无法加载用户数据，请稍后重试",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, [user, navigate, toast]);

  const handleSaveProfile = async () => {
    if (!userProfile || !editedProfile || !user) return;

    try {
      // 调用后端API保存用户信息
      const response = await fetch(`http://localhost:5000/api/users/${user.user_id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: JSON.stringify({
          username: editedProfile.username,
          email: editedProfile.email,
          phone: editedProfile.phone,
          bio: editedProfile.bio,
          location: editedProfile.location,
          preferences: editedProfile.preferences
        })
      });

      const data = await response.json();

      if (response.ok && data.success) {
        // 更新本地状态
        setUserProfile({ ...userProfile, ...editedProfile });
        setEditMode(false);
        
        toast({
          title: "保存成功",
          description: "您的个人信息已更新",
        });
      } else {
        throw new Error(data.message || '保存失败');
      }
      
    } catch (error) {
      console.error('保存用户信息失败:', error);
      toast({
        title: "保存失败",
        description: error instanceof Error ? error.message : "无法保存用户信息，请稍后重试",
        variant: "destructive",
      });
    }
  };

  const handleCancelEdit = () => {
    setEditedProfile(userProfile || {});
    setEditMode(false);
  };

  const handleLogout = () => {
    logout();
    toast({
      title: "退出成功",
      description: "您已成功退出登录",
    });
    navigate('/');
  };

  const handlePreferenceChange = (key: string, value: any) => {
    setEditedProfile(prev => ({
      ...prev,
      preferences: {
        preferred_cuisines: prev.preferences?.preferred_cuisines || [],
        dietary_restrictions: prev.preferences?.dietary_restrictions || [],
        price_range: prev.preferences?.price_range || 'medium',
        spice_level: prev.preferences?.spice_level || 'medium',
        ...prev.preferences,
        [key]: value
      }
    }));
  };

  const StatCard: React.FC<{ icon: React.ReactNode; title: string; value: string | number; description?: string; gradient: string }> = ({ 
    icon, 
    title, 
    value, 
    description,
    gradient
  }) => (
    <Card className="text-center bg-white/80 backdrop-blur-sm hover:bg-white hover:shadow-xl transition-all duration-300 hover:scale-105 border-0 shadow-lg overflow-hidden">
      <div className={`h-2 ${gradient}`}></div>
      <CardContent className="p-6">
        <div className="flex justify-center mb-4">
          <div className={`w-16 h-16 rounded-full ${gradient} flex items-center justify-center shadow-lg`}>
            {icon}
          </div>
        </div>
        <div className="text-3xl font-bold text-gray-900 mb-2">{value}</div>
        <div className="text-lg font-semibold text-gray-700 mb-1">{title}</div>
        {description && <div className="text-sm text-gray-500">{description}</div>}
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-orange-50 via-red-50 to-yellow-50 flex items-center justify-center">
        <div className="text-center">
          <div className="relative">
            <div className="animate-spin rounded-full h-16 w-16 border-4 border-orange-200 border-t-orange-500 mx-auto mb-6"></div>
            <User className="h-8 w-8 text-orange-500 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
          </div>
          <p className="text-gray-700 text-lg font-medium">正在加载用户信息...</p>
          <p className="text-gray-500 text-sm mt-2">请稍候片刻</p>
        </div>
      </div>
    );
  }

  if (!userProfile) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-orange-50 via-red-50 to-yellow-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600 text-lg">用户信息加载失败</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-red-50 to-yellow-50">
      {/* 头部导航 */}
      <header className="bg-white/95 backdrop-blur-md shadow-lg border-b border-orange-100 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-18 py-2">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center shadow-lg transform hover:scale-105 transition-transform duration-200">
                <span className="text-white font-bold text-xl">美</span>
              </div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
                美食推荐
              </h1>
            </div>
            <nav className="hidden md:flex space-x-1">
              <button 
                onClick={() => navigate('/')} 
                className="px-6 py-3 rounded-full text-gray-600 hover:text-orange-600 hover:bg-orange-50 transition-all duration-200 font-medium"
              >
                首页
              </button>
              <button 
                onClick={() => navigate('/search')} 
                className="px-6 py-3 rounded-full text-gray-600 hover:text-orange-600 hover:bg-orange-50 transition-all duration-200 font-medium"
              >
                搜索
              </button>
              <button className="px-6 py-3 rounded-full bg-gradient-to-r from-orange-500 to-red-500 text-white font-medium shadow-lg transform hover:scale-105 transition-all duration-200 hover:shadow-xl">
                我的
              </button>
            </nav>
            <Button 
              className="bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 text-white shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 rounded-full px-6 py-3"
              onClick={() => {
                logout();
                navigate('/');
              }}
            >
              退出登录
            </Button>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* 左侧用户信息卡片 */}
          <div className="lg:col-span-1">
            <Card className="sticky top-24 bg-white/90 backdrop-blur-sm shadow-xl border-0 overflow-hidden">
              <div className="h-3 bg-gradient-to-r from-orange-500 to-red-500"></div>
              <CardContent className="p-8 text-center">
                <div className="relative inline-block mb-6">
                  <Avatar className="w-28 h-28 border-4 border-white shadow-xl">
                    <AvatarImage src={userProfile.avatar_url} />
                    <AvatarFallback className="text-3xl bg-gradient-to-br from-orange-100 to-red-100 text-orange-600">
                      {userProfile.username[0]}
                    </AvatarFallback>
                  </Avatar>
                  <Button 
                    size="sm" 
                    className="absolute -bottom-2 -right-2 rounded-full w-10 h-10 p-0 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 shadow-lg hover:shadow-xl transform hover:scale-110 transition-all duration-200"
                  >
                    <Camera className="h-5 w-5" />
                  </Button>
                </div>
                
                <h2 className="text-2xl font-bold text-gray-900 mb-2">{userProfile.username}</h2>
                <Badge className="mb-4 bg-gradient-to-r from-orange-500 to-red-500 text-white hover:from-orange-600 hover:to-red-600 px-4 py-2 text-sm font-semibold">
                  <Sparkles className="h-4 w-4 mr-1" />
                  {userProfile.statistics.level}
                </Badge>
                
                <div className="text-sm text-gray-600 mb-6 space-y-2">
                  <div className="flex items-center justify-center gap-2">
                    <MapPin className="h-4 w-4 text-orange-500" />
                    <span className="font-medium">{userProfile.location}</span>
                  </div>
                  <div className="flex items-center justify-center gap-2">
                    <Calendar className="h-4 w-4 text-orange-500" />
                    <span>加入于 {userProfile.created_at.split(' ')[0]}</span>
                  </div>
                </div>

                {userProfile.bio && (
                  <p className="text-sm text-gray-700 mb-6 text-left bg-gradient-to-r from-orange-50 to-red-50 p-4 rounded-xl leading-relaxed">
                    {userProfile.bio}
                  </p>
                )}

                <div className="grid grid-cols-2 gap-3 text-center">
                  <div className="bg-gradient-to-br from-orange-50 to-red-50 p-4 rounded-xl border border-orange-100">
                    <div className="text-2xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
                      {userProfile.statistics.points}
                    </div>
                    <div className="text-xs text-gray-600 font-medium">积分</div>
                  </div>
                  <div className="bg-gradient-to-br from-green-50 to-emerald-50 p-4 rounded-xl border border-green-100">
                    <div className="text-2xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                      {userProfile.statistics.total_orders}
                    </div>
                    <div className="text-xs text-gray-600 font-medium">订单</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 右侧主要内容 */}
          <div className="lg:col-span-3">
            {/* 统计卡片 */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
              <StatCard
                icon={<ShoppingBag className="h-8 w-8 text-white" />}
                title="总订单"
                value={userProfile.statistics.total_orders}
                description="历史订单数量"
                gradient="bg-gradient-to-br from-blue-500 to-blue-600"
              />
              <StatCard
                icon={<Star className="h-8 w-8 text-white" />}
                title="评价数"
                value={userProfile.statistics.total_reviews}
                description="发表的评价"
                gradient="bg-gradient-to-br from-yellow-500 to-yellow-600"
              />
              <StatCard
                icon={<Heart className="h-8 w-8 text-white" />}
                title="收藏数"
                value={userProfile.statistics.favorite_dishes}
                description="收藏的菜品"
                gradient="bg-gradient-to-br from-red-500 to-red-600"
              />
              <StatCard
                icon={<Award className="h-8 w-8 text-white" />}
                title="积分"
                value={userProfile.statistics.points}
                description="当前积分"
                gradient="bg-gradient-to-br from-purple-500 to-purple-600"
              />
            </div>

            {/* 主要内容标签页 */}
            <Card className="bg-white/90 backdrop-blur-sm shadow-xl border-0">
              <CardContent className="p-8">
                <Tabs defaultValue="favorites" className="w-full">
                  <TabsList className="grid w-full grid-cols-4 bg-gray-100 p-1 rounded-xl">
                    <TabsTrigger value="favorites" className="rounded-lg font-medium data-[state=active]:bg-white data-[state=active]:shadow-md">我的收藏</TabsTrigger>
                    <TabsTrigger value="orders" className="rounded-lg font-medium data-[state=active]:bg-white data-[state=active]:shadow-md">订单历史</TabsTrigger>
                    <TabsTrigger value="reviews" className="rounded-lg font-medium data-[state=active]:bg-white data-[state=active]:shadow-md">我的评价</TabsTrigger>
                    <TabsTrigger value="settings" className="rounded-lg font-medium data-[state=active]:bg-white data-[state=active]:shadow-md">个人设置</TabsTrigger>
                  </TabsList>

                  {/* 我的收藏 */}
                  <TabsContent value="favorites" className="mt-8">
                    <div className="flex items-center justify-between mb-6">
                      <h3 className="text-2xl font-bold text-gray-900">我的收藏</h3>
                      <Badge variant="outline" className="px-4 py-2 text-sm font-semibold border-orange-200 text-orange-600">
                        {favorites.length} 个菜品
                      </Badge>
                    </div>
                    
                    {favorites.length > 0 ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {favorites.map((dish) => (
                          <Card key={dish.dish_id} className="group hover:shadow-xl transition-all duration-300 cursor-pointer bg-white/80 backdrop-blur-sm hover:bg-white hover:scale-105 border-0 shadow-lg overflow-hidden">
                            <div className="flex">
                              <div className="relative w-32 h-32 flex-shrink-0 overflow-hidden">
                                <img
                                  src={getDishImageUrl(dish.name, dish.image_url)}
                                  alt={dish.name}
                                  className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                                  onError={handleImageError}
                                />
                                <div className="absolute inset-0 bg-gradient-to-r from-transparent to-black/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                              </div>
                              <CardContent className="flex-1 p-5">
                                <div className="flex justify-between items-start mb-3">
                                  <h4 className="font-bold text-lg group-hover:text-orange-600 transition-colors duration-200">{dish.name}</h4>
                                  <span className="text-xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">¥{dish.price}</span>
                                </div>
                                <div className="flex items-center gap-3 mb-3">
                                  <Badge variant="secondary" className="text-xs bg-orange-100 text-orange-700 hover:bg-orange-200">
                                    {dish.cuisine_type}
                                  </Badge>
                                  <div className="flex items-center gap-1">
                                    <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                                    <span className="text-sm font-medium">{dish.rating}</span>
                                  </div>
                                </div>
                                <div className="text-xs text-gray-500 space-y-1">
                                  <div className="font-medium">{dish.restaurant_name}</div>
                                  <div>收藏于 {dish.favorited_at.split(' ')[0]}</div>
                                </div>
                              </CardContent>
                            </div>
                          </Card>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-16 text-gray-500">
                        <Heart className="h-20 w-20 mx-auto mb-6 opacity-30" />
                        <p className="text-xl font-semibold mb-2">还没有收藏任何菜品</p>
                        <p className="text-lg">去发现一些美味的菜品吧！</p>
                      </div>
                    )}
                  </TabsContent>

                  {/* 订单历史 */}
                  <TabsContent value="orders" className="mt-8">
                    <div className="flex items-center justify-between mb-6">
                      <h3 className="text-2xl font-bold text-gray-900">订单历史</h3>
                      <Badge variant="outline" className="px-4 py-2 text-sm font-semibold border-orange-200 text-orange-600">
                        {orders.length} 个订单
                      </Badge>
                    </div>

                    {orders.length > 0 ? (
                      <div className="space-y-6">
                        {orders.map((order) => (
                          <Card key={order.order_id} className="bg-white/80 backdrop-blur-sm hover:bg-white hover:shadow-xl transition-all duration-300 border-0 shadow-lg">
                            <CardContent className="p-6">
                              <div className="flex justify-between items-start mb-4">
                                <div>
                                  <div className="font-bold text-lg text-gray-900">订单 #{order.order_id}</div>
                                  <div className="text-sm text-gray-600 font-medium">{order.restaurant_name}</div>
                                  <div className="text-xs text-gray-500">{order.order_time}</div>
                                </div>
                                <div className="text-right">
                                  <div className="text-2xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">¥{order.total_amount}</div>
                                  <Badge 
                                    className={order.status === 'completed' 
                                      ? 'bg-gradient-to-r from-green-500 to-emerald-500 text-white' 
                                      : 'bg-gradient-to-r from-gray-500 to-gray-600 text-white'
                                    }
                                  >
                                    {order.status === 'completed' ? '已完成' : '进行中'}
                                  </Badge>
                                </div>
                              </div>
                              
                              <div className="space-y-2 bg-gray-50 p-4 rounded-xl">
                                {order.dishes.map((dish, index) => (
                                  <div key={index} className="flex justify-between text-sm">
                                    <span className="font-medium">{dish.name} x{dish.quantity}</span>
                                    <span className="font-semibold">¥{dish.price * dish.quantity}</span>
                                  </div>
                                ))}
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-16 text-gray-500">
                        <ShoppingBag className="h-20 w-20 mx-auto mb-6 opacity-30" />
                        <p className="text-xl font-semibold mb-2">还没有任何订单</p>
                        <p className="text-lg">开始您的美食之旅吧！</p>
                      </div>
                    )}
                  </TabsContent>

                  {/* 我的评价 */}
                  <TabsContent value="reviews" className="mt-8">
                    <div className="flex items-center justify-between mb-6">
                      <h3 className="text-2xl font-bold text-gray-900">我的评价</h3>
                      <Badge variant="outline" className="px-4 py-2 text-sm font-semibold border-orange-200 text-orange-600">
                        {reviews.length} 条评价
                      </Badge>
                    </div>

                    {reviews.length > 0 ? (
                      <div className="space-y-6">
                        {reviews.map((review) => (
                          <Card key={review.review_id} className="bg-white/80 backdrop-blur-sm hover:bg-white hover:shadow-xl transition-all duration-300 border-0 shadow-lg">
                            <CardContent className="p-6">
                              <div className="flex justify-between items-start mb-3">
                                <div>
                                  <h4 className="font-bold text-lg text-gray-900">{review.dish_name}</h4>
                                  <div className="text-sm text-gray-600 font-medium">{review.restaurant_name}</div>
                                </div>
                                <div className="flex items-center gap-1">
                                  {[1, 2, 3, 4, 5].map((star) => (
                                    <Star
                                      key={star}
                                      className={`h-5 w-5 ${
                                        star <= review.rating 
                                          ? 'fill-yellow-400 text-yellow-400' 
                                          : 'text-gray-300'
                                      }`}
                                    />
                                  ))}
                                </div>
                              </div>
                              <p className="text-gray-700 mb-3 bg-gray-50 p-4 rounded-xl leading-relaxed">{review.comment}</p>
                              <div className="text-xs text-gray-500">{review.created_at}</div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-16 text-gray-500">
                        <Star className="h-20 w-20 mx-auto mb-6 opacity-30" />
                        <p className="text-xl font-semibold mb-2">还没有发表任何评价</p>
                        <p className="text-lg">分享您的用餐体验吧！</p>
                      </div>
                    )}
                  </TabsContent>

                  {/* 个人设置 */}
                  <TabsContent value="settings" className="mt-8">
                    <div className="space-y-8">
                      <div className="flex items-center justify-between">
                        <h3 className="text-2xl font-bold text-gray-900">个人设置</h3>
                        <div className="flex gap-3">
                          {editMode ? (
                            <>
                              <Button variant="outline" onClick={handleCancelEdit} className="border-gray-300 hover:bg-gray-50">
                                取消
                              </Button>
                              <Button onClick={handleSaveProfile} className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white shadow-lg hover:shadow-xl">
                                保存
                              </Button>
                            </>
                          ) : (
                            <Button onClick={() => setEditMode(true)} variant="outline" className="border-orange-200 text-orange-600 hover:bg-orange-50">
                              <Edit className="h-4 w-4 mr-2" />
                              编辑
                            </Button>
                          )}
                        </div>
                      </div>

                      {/* 基本信息 */}
                      <Card className="bg-white/80 backdrop-blur-sm shadow-lg border-0">
                        <CardHeader>
                          <CardTitle className="text-lg font-bold text-gray-900">基本信息</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-6">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                              <Label htmlFor="username" className="text-sm font-semibold text-gray-700">用户名</Label>
                              <Input
                                id="username"
                                value={editMode ? editedProfile.username || '' : userProfile.username}
                                onChange={(e) => setEditedProfile(prev => ({ ...prev, username: e.target.value }))}
                                disabled={!editMode}
                                className="mt-2 bg-white/80 border-gray-200 focus:border-orange-400"
                              />
                            </div>
                            <div>
                              <Label htmlFor="email" className="text-sm font-semibold text-gray-700">邮箱</Label>
                              <Input
                                id="email"
                                type="email"
                                value={editMode ? editedProfile.email || '' : userProfile.email}
                                onChange={(e) => setEditedProfile(prev => ({ ...prev, email: e.target.value }))}
                                disabled={!editMode}
                                className="mt-2 bg-white/80 border-gray-200 focus:border-orange-400"
                              />
                            </div>
                            <div>
                              <Label htmlFor="phone" className="text-sm font-semibold text-gray-700">手机号</Label>
                              <Input
                                id="phone"
                                value={editMode ? editedProfile.phone || '' : userProfile.phone}
                                onChange={(e) => setEditedProfile(prev => ({ ...prev, phone: e.target.value }))}
                                disabled={!editMode}
                                className="mt-2 bg-white/80 border-gray-200 focus:border-orange-400"
                              />
                            </div>
                            <div>
                              <Label htmlFor="location" className="text-sm font-semibold text-gray-700">所在地区</Label>
                              <Input
                                id="location"
                                value={editMode ? editedProfile.location || '' : userProfile.location}
                                onChange={(e) => setEditedProfile(prev => ({ ...prev, location: e.target.value }))}
                                disabled={!editMode}
                                className="mt-2 bg-white/80 border-gray-200 focus:border-orange-400"
                              />
                            </div>
                          </div>
                          <div>
                            <Label htmlFor="bio" className="text-sm font-semibold text-gray-700">个人简介</Label>
                            <Textarea
                              id="bio"
                              value={editMode ? editedProfile.bio || '' : userProfile.bio}
                              onChange={(e) => setEditedProfile(prev => ({ ...prev, bio: e.target.value }))}
                              disabled={!editMode}
                              placeholder="介绍一下您的美食偏好..."
                              className="mt-2 bg-white/80 border-gray-200 focus:border-orange-400 min-h-[100px]"
                            />
                          </div>
                        </CardContent>
                      </Card>

                      {/* 偏好设置 */}
                      <Card className="bg-white/80 backdrop-blur-sm shadow-lg border-0">
                        <CardHeader>
                          <CardTitle className="text-lg font-bold text-gray-900">偏好设置</CardTitle>
                          <CardDescription className="text-gray-600">设置您的美食偏好，获得更精准的推荐</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                          <div>
                            <Label className="text-sm font-semibold text-gray-700">喜欢的菜系</Label>
                            <div className="flex flex-wrap gap-3 mt-3">
                              {['粤菜', '川菜', '湘菜', '鲁菜', '苏菜', '浙菜', '闽菜', '徽菜', '桂菜', '琼菜'].map((cuisine) => (
                                <Badge
                                  key={cuisine}
                                  variant={(userProfile?.preferences?.preferred_cuisines || []).includes(cuisine) ? "default" : "outline"}
                                  className={`cursor-pointer px-4 py-2 text-sm font-medium transition-all duration-200 ${
                                    (userProfile?.preferences?.preferred_cuisines || []).includes(cuisine)
                                      ? 'bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white shadow-lg'
                                      : 'border-orange-200 text-orange-600 hover:bg-orange-50 hover:border-orange-300'
                                  }`}
                                >
                                  {cuisine}
                                </Badge>
                              ))}
                            </div>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                              <Label htmlFor="price-range" className="text-sm font-semibold text-gray-700">价格偏好</Label>
                              <Select
                                value={userProfile.preferences.price_range}
                                onValueChange={(value) => handlePreferenceChange('price_range', value)}
                                disabled={!editMode}
                              >
                                <SelectTrigger className="mt-2 bg-white/80 border-gray-200 focus:border-orange-400">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="low">经济实惠 (¥0-30)</SelectItem>
                                  <SelectItem value="medium">中等价位 (¥30-80)</SelectItem>
                                  <SelectItem value="high">高端消费 (¥80+)</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div>
                              <Label htmlFor="spice-level" className="text-sm font-semibold text-gray-700">辣度偏好</Label>
                              <Select
                                value={userProfile.preferences.spice_level}
                                onValueChange={(value) => handlePreferenceChange('spice_level', value)}
                                disabled={!editMode}
                              >
                                <SelectTrigger className="mt-2 bg-white/80 border-gray-200 focus:border-orange-400">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="none">不吃辣</SelectItem>
                                  <SelectItem value="mild">微辣</SelectItem>
                                  <SelectItem value="medium">中辣</SelectItem>
                                  <SelectItem value="hot">重辣</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      {/* 通知设置 */}
                      <Card className="bg-white/80 backdrop-blur-sm shadow-lg border-0">
                        <CardHeader>
                          <CardTitle className="text-lg font-bold text-gray-900">通知设置</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-6">
                          <div className="flex items-center justify-between py-3 border-b border-gray-100">
                            <div>
                              <div className="font-semibold text-gray-900">推荐通知</div>
                              <div className="text-sm text-gray-600">接收个性化美食推荐</div>
                            </div>
                            <Switch defaultChecked className="data-[state=checked]:bg-orange-500" />
                          </div>
                          <div className="flex items-center justify-between py-3 border-b border-gray-100">
                            <div>
                              <div className="font-semibold text-gray-900">订单通知</div>
                              <div className="text-sm text-gray-600">接收订单状态更新</div>
                            </div>
                            <Switch defaultChecked className="data-[state=checked]:bg-orange-500" />
                          </div>
                          <div className="flex items-center justify-between py-3">
                            <div>
                              <div className="font-semibold text-gray-900">优惠通知</div>
                              <div className="text-sm text-gray-600">接收优惠活动信息</div>
                            </div>
                            <Switch className="data-[state=checked]:bg-orange-500" />
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserProfilePage;
