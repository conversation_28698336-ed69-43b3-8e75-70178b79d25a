"""
性能监控工具
"""

import psutil
import time
import json
import requests
import threading
from datetime import datetime
import matplotlib.pyplot as plt
import pandas as pd

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, api_base_url='http://localhost:5000', frontend_url='http://localhost:5173'):
        self.api_base_url = api_base_url
        self.frontend_url = frontend_url
        self.monitoring = False
        self.metrics = {
            'timestamp': [],
            'cpu_percent': [],
            'memory_percent': [],
            'memory_used_mb': [],
            'disk_io_read': [],
            'disk_io_write': [],
            'network_sent': [],
            'network_recv': [],
            'api_response_times': [],
            'api_status_codes': []
        }
    
    def start_monitoring(self, duration=300, interval=5):
        """开始监控系统性能"""
        print(f"🔍 开始性能监控，持续时间: {duration}秒，采样间隔: {interval}秒")
        
        self.monitoring = True
        start_time = time.time()
        
        # 获取初始网络和磁盘IO数据
        initial_net = psutil.net_io_counters()
        initial_disk = psutil.disk_io_counters()
        
        while self.monitoring and (time.time() - start_time) < duration:
            try:
                # 系统资源监控
                cpu_percent = psutil.cpu_percent(interval=1)
                memory = psutil.virtual_memory()
                
                # 网络和磁盘IO
                current_net = psutil.net_io_counters()
                current_disk = psutil.disk_io_counters()
                
                # API响应时间监控
                api_response_time, api_status = self.check_api_response()
                
                # 记录数据
                self.metrics['timestamp'].append(datetime.now())
                self.metrics['cpu_percent'].append(cpu_percent)
                self.metrics['memory_percent'].append(memory.percent)
                self.metrics['memory_used_mb'].append(memory.used / 1024 / 1024)
                self.metrics['disk_io_read'].append(current_disk.read_bytes - initial_disk.read_bytes)
                self.metrics['disk_io_write'].append(current_disk.write_bytes - initial_disk.write_bytes)
                self.metrics['network_sent'].append(current_net.bytes_sent - initial_net.bytes_sent)
                self.metrics['network_recv'].append(current_net.bytes_recv - initial_net.bytes_recv)
                self.metrics['api_response_times'].append(api_response_time)
                self.metrics['api_status_codes'].append(api_status)
                
                # 实时输出关键指标
                print(f"⏰ {datetime.now().strftime('%H:%M:%S')} - "
                      f"CPU: {cpu_percent:.1f}% | "
                      f"内存: {memory.percent:.1f}% | "
                      f"API响应: {api_response_time:.2f}s")
                
                time.sleep(interval)
                
            except Exception as e:
                print(f"⚠️ 监控过程中发生错误: {e}")
                time.sleep(interval)
        
        print("✅ 性能监控完成")
        return self.metrics
    
    def check_api_response(self):
        """检查API响应时间"""
        try:
            start_time = time.time()
            response = requests.get(f"{self.api_base_url}/health", timeout=10)
            response_time = time.time() - start_time
            return response_time, response.status_code
        except Exception:
            return 0, 0
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
    
    def generate_report(self):
        """生成性能报告"""
        if not self.metrics['timestamp']:
            print("❌ 没有监控数据，无法生成报告")
            return
        
        print(f"\n{'='*60}")
        print("📊 性能监控报告")
        print(f"{'='*60}")
        
        # 基本统计
        cpu_avg = sum(self.metrics['cpu_percent']) / len(self.metrics['cpu_percent'])
        cpu_max = max(self.metrics['cpu_percent'])
        
        memory_avg = sum(self.metrics['memory_percent']) / len(self.metrics['memory_percent'])
        memory_max = max(self.metrics['memory_percent'])
        
        api_times = [t for t in self.metrics['api_response_times'] if t > 0]
        if api_times:
            api_avg = sum(api_times) / len(api_times)
            api_max = max(api_times)
        else:
            api_avg = api_max = 0
        
        print(f"📈 CPU使用率:")
        print(f"   平均: {cpu_avg:.1f}%")
        print(f"   峰值: {cpu_max:.1f}%")
        
        print(f"\n💾 内存使用率:")
        print(f"   平均: {memory_avg:.1f}%")
        print(f"   峰值: {memory_max:.1f}%")
        
        print(f"\n🌐 API响应时间:")
        print(f"   平均: {api_avg:.2f}s")
        print(f"   最大: {api_max:.2f}s")
        
        # 性能评估
        self.evaluate_performance(cpu_avg, cpu_max, memory_avg, memory_max, api_avg, api_max)
        
        # 生成图表
        self.generate_charts()
        
        # 保存数据
        self.save_metrics()
    
    def evaluate_performance(self, cpu_avg, cpu_max, memory_avg, memory_max, api_avg, api_max):
        """评估性能表现"""
        print(f"\n{'='*60}")
        print("🎯 性能评估")
        print(f"{'='*60}")
        
        issues = []
        recommendations = []
        
        # CPU评估
        if cpu_avg > 80:
            issues.append("CPU平均使用率过高")
            recommendations.append("优化算法复杂度，考虑异步处理")
        elif cpu_max > 95:
            issues.append("CPU峰值使用率过高")
            recommendations.append("检查是否有CPU密集型操作")
        
        # 内存评估
        if memory_avg > 80:
            issues.append("内存平均使用率过高")
            recommendations.append("检查内存泄漏，优化数据结构")
        elif memory_max > 95:
            issues.append("内存峰值使用率过高")
            recommendations.append("增加内存或优化内存使用")
        
        # API响应时间评估
        if api_avg > 2.0:
            issues.append("API平均响应时间过长")
            recommendations.append("优化数据库查询，添加缓存")
        elif api_max > 5.0:
            issues.append("API最大响应时间过长")
            recommendations.append("检查慢查询，优化算法")
        
        if issues:
            print("⚠️ 发现的性能问题:")
            for i, issue in enumerate(issues, 1):
                print(f"   {i}. {issue}")
            
            print("\n💡 优化建议:")
            for i, rec in enumerate(recommendations, 1):
                print(f"   {i}. {rec}")
        else:
            print("✅ 系统性能表现良好！")
    
    def generate_charts(self):
        """生成性能图表"""
        try:
            import matplotlib.pyplot as plt
            import matplotlib.dates as mdates
            
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle('美食推荐系统性能监控报告', fontsize=16)
            
            # CPU使用率图表
            axes[0, 0].plot(self.metrics['timestamp'], self.metrics['cpu_percent'], 'b-', linewidth=2)
            axes[0, 0].set_title('CPU使用率 (%)')
            axes[0, 0].set_ylabel('使用率 (%)')
            axes[0, 0].grid(True, alpha=0.3)
            axes[0, 0].xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
            
            # 内存使用率图表
            axes[0, 1].plot(self.metrics['timestamp'], self.metrics['memory_percent'], 'g-', linewidth=2)
            axes[0, 1].set_title('内存使用率 (%)')
            axes[0, 1].set_ylabel('使用率 (%)')
            axes[0, 1].grid(True, alpha=0.3)
            axes[0, 1].xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
            
            # API响应时间图表
            valid_times = [(t, rt) for t, rt in zip(self.metrics['timestamp'], self.metrics['api_response_times']) if rt > 0]
            if valid_times:
                times, response_times = zip(*valid_times)
                axes[1, 0].plot(times, response_times, 'r-', linewidth=2)
            axes[1, 0].set_title('API响应时间 (秒)')
            axes[1, 0].set_ylabel('响应时间 (秒)')
            axes[1, 0].grid(True, alpha=0.3)
            axes[1, 0].xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
            
            # 内存使用量图表
            axes[1, 1].plot(self.metrics['timestamp'], self.metrics['memory_used_mb'], 'm-', linewidth=2)
            axes[1, 1].set_title('内存使用量 (MB)')
            axes[1, 1].set_ylabel('使用量 (MB)')
            axes[1, 1].grid(True, alpha=0.3)
            axes[1, 1].xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
            
            plt.tight_layout()
            
            # 保存图表
            chart_filename = f"performance_chart_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            plt.savefig(chart_filename, dpi=300, bbox_inches='tight')
            print(f"📊 性能图表已保存: {chart_filename}")
            
            plt.close()
            
        except ImportError:
            print("⚠️ matplotlib未安装，跳过图表生成")
        except Exception as e:
            print(f"⚠️ 生成图表时发生错误: {e}")
    
    def save_metrics(self):
        """保存监控数据"""
        try:
            # 转换时间戳为字符串
            metrics_copy = self.metrics.copy()
            metrics_copy['timestamp'] = [t.isoformat() for t in self.metrics['timestamp']]
            
            filename = f"performance_metrics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(metrics_copy, f, ensure_ascii=False, indent=2)
            
            print(f"💾 监控数据已保存: {filename}")
            
        except Exception as e:
            print(f"⚠️ 保存监控数据时发生错误: {e}")

class LoadTester:
    """负载测试器"""
    
    def __init__(self, api_base_url='http://localhost:5000'):
        self.api_base_url = api_base_url
        self.results = []
    
    def run_load_test(self, concurrent_users=10, requests_per_user=20, ramp_up_time=10):
        """运行负载测试"""
        print(f"🚀 开始负载测试:")
        print(f"   并发用户数: {concurrent_users}")
        print(f"   每用户请求数: {requests_per_user}")
        print(f"   启动时间: {ramp_up_time}秒")
        
        self.results = []
        threads = []
        
        # 创建用户线程
        for user_id in range(concurrent_users):
            thread = threading.Thread(
                target=self.simulate_user,
                args=(user_id, requests_per_user, ramp_up_time / concurrent_users * user_id)
            )
            threads.append(thread)
        
        # 启动所有线程
        start_time = time.time()
        for thread in threads:
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        total_time = time.time() - start_time
        
        # 分析结果
        self.analyze_load_test_results(total_time)
    
    def simulate_user(self, user_id, num_requests, delay):
        """模拟用户行为"""
        time.sleep(delay)  # 渐进启动
        
        session = requests.Session()
        user_results = []
        
        for request_id in range(num_requests):
            # 模拟不同的用户行为
            endpoints = [
                '/api/dishes',
                '/api/dishes/search?query=鸡',
                '/health'
            ]
            
            endpoint = endpoints[request_id % len(endpoints)]
            
            try:
                start_time = time.time()
                response = session.get(f"{self.api_base_url}{endpoint}", timeout=30)
                response_time = time.time() - start_time
                
                result = {
                    'user_id': user_id,
                    'request_id': request_id,
                    'endpoint': endpoint,
                    'status_code': response.status_code,
                    'response_time': response_time,
                    'success': response.status_code == 200,
                    'timestamp': datetime.now()
                }
                
                user_results.append(result)
                
                # 模拟用户思考时间
                time.sleep(0.1)
                
            except Exception as e:
                result = {
                    'user_id': user_id,
                    'request_id': request_id,
                    'endpoint': endpoint,
                    'status_code': 0,
                    'response_time': 0,
                    'success': False,
                    'error': str(e),
                    'timestamp': datetime.now()
                }
                user_results.append(result)
        
        session.close()
        self.results.extend(user_results)
    
    def analyze_load_test_results(self, total_time):
        """分析负载测试结果"""
        if not self.results:
            print("❌ 没有负载测试结果")
            return
        
        print(f"\n{'='*60}")
        print("📊 负载测试结果分析")
        print(f"{'='*60}")
        
        total_requests = len(self.results)
        successful_requests = len([r for r in self.results if r['success']])
        failed_requests = total_requests - successful_requests
        
        success_rate = (successful_requests / total_requests) * 100
        requests_per_second = total_requests / total_time
        
        # 响应时间统计
        response_times = [r['response_time'] for r in self.results if r['success']]
        if response_times:
            avg_response_time = sum(response_times) / len(response_times)
            min_response_time = min(response_times)
            max_response_time = max(response_times)
            
            # 计算百分位数
            response_times.sort()
            p50 = response_times[int(len(response_times) * 0.5)]
            p90 = response_times[int(len(response_times) * 0.9)]
            p95 = response_times[int(len(response_times) * 0.95)]
        else:
            avg_response_time = min_response_time = max_response_time = 0
            p50 = p90 = p95 = 0
        
        print(f"📈 总体统计:")
        print(f"   总请求数: {total_requests}")
        print(f"   成功请求: {successful_requests}")
        print(f"   失败请求: {failed_requests}")
        print(f"   成功率: {success_rate:.2f}%")
        print(f"   总耗时: {total_time:.2f}秒")
        print(f"   吞吐量: {requests_per_second:.2f} 请求/秒")
        
        print(f"\n⏱️ 响应时间统计:")
        print(f"   平均响应时间: {avg_response_time:.3f}秒")
        print(f"   最小响应时间: {min_response_time:.3f}秒")
        print(f"   最大响应时间: {max_response_time:.3f}秒")
        print(f"   50%百分位: {p50:.3f}秒")
        print(f"   90%百分位: {p90:.3f}秒")
        print(f"   95%百分位: {p95:.3f}秒")
        
        # 性能评估
        self.evaluate_load_test_performance(success_rate, avg_response_time, requests_per_second)
        
        # 保存结果
        self.save_load_test_results()
    
    def evaluate_load_test_performance(self, success_rate, avg_response_time, rps):
        """评估负载测试性能"""
        print(f"\n🎯 性能评估:")
        
        if success_rate >= 99:
            print("✅ 系统稳定性: 优秀")
        elif success_rate >= 95:
            print("👍 系统稳定性: 良好")
        else:
            print("⚠️ 系统稳定性: 需要改进")
        
        if avg_response_time <= 1.0:
            print("✅ 响应速度: 优秀")
        elif avg_response_time <= 2.0:
            print("👍 响应速度: 良好")
        else:
            print("⚠️ 响应速度: 需要优化")
        
        if rps >= 100:
            print("✅ 系统吞吐量: 优秀")
        elif rps >= 50:
            print("👍 系统吞吐量: 良好")
        else:
            print("⚠️ 系统吞吐量: 需要提升")
    
    def save_load_test_results(self):
        """保存负载测试结果"""
        try:
            # 转换时间戳
            results_copy = []
            for result in self.results:
                result_copy = result.copy()
                result_copy['timestamp'] = result['timestamp'].isoformat()
                results_copy.append(result_copy)
            
            filename = f"load_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results_copy, f, ensure_ascii=False, indent=2)
            
            print(f"💾 负载测试结果已保存: {filename}")
            
        except Exception as e:
            print(f"⚠️ 保存负载测试结果时发生错误: {e}")

def main():
    """主函数"""
    print("🔧 美食推荐系统性能监控工具")
    print("="*50)
    
    while True:
        print("\n请选择操作:")
        print("1. 启动性能监控")
        print("2. 运行负载测试")
        print("3. 退出")
        
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == '1':
            monitor = PerformanceMonitor()
            try:
                duration = int(input("监控持续时间(秒，默认300): ") or "300")
                interval = int(input("采样间隔(秒，默认5): ") or "5")
                
                monitor.start_monitoring(duration, interval)
                monitor.generate_report()
                
            except KeyboardInterrupt:
                print("\n⏹️ 监控被用户中断")
                monitor.stop_monitoring()
                monitor.generate_report()
            except ValueError:
                print("❌ 输入无效，请输入数字")
        
        elif choice == '2':
            tester = LoadTester()
            try:
                users = int(input("并发用户数(默认10): ") or "10")
                requests = int(input("每用户请求数(默认20): ") or "20")
                ramp_up = int(input("启动时间(秒，默认10): ") or "10")
                
                tester.run_load_test(users, requests, ramp_up)
                
            except ValueError:
                print("❌ 输入无效，请输入数字")
        
        elif choice == '3':
            print("👋 再见！")
            break
        
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == '__main__':
    main()