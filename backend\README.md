# 美食推荐系统后端

## 📁 文件说明

### 核心文件
- `unified_app.py` - **主应用文件**，集成了所有功能的统一后端服务
- `enhanced_food_data.json` - 美食数据文件（80道菜品 + 40家餐厅）
- `requirements.txt` - Python依赖包列表

## 🚀 快速启动

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置MySQL数据库
- 确保MySQL服务正在运行
- 用户名: `root`
- 密码: `123456789`
- 数据库会自动创建

### 3. 启动服务
```bash
python unified_app.py
```

## 📋 功能特性

### ✅ 已集成功能
1. **数据库管理** - 自动初始化MySQL数据库和表结构
2. **示例数据生成** - 自动生成川菜、粤菜、湘菜等菜品数据
3. **用户认证** - JWT token认证，注册/登录功能
4. **菜品管理** - 菜品列表、详情、搜索、筛选
5. **收藏功能** - 用户收藏菜品的增删查功能
6. **用户管理** - 用户信息查询和管理
7. **健康检查** - API服务状态监控

### 🗃️ 数据库结构
- `users` - 用户表
- `dishes` - 菜品表
- `restaurants` - 餐厅表  
- `favorites` - 收藏表

## 🔗 API端点

### 认证接口
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录

### 菜品接口
- `GET /api/dishes` - 获取菜品列表
- `GET /api/dishes/{id}` - 获取菜品详情

### 收藏接口
- `GET /api/users/{user_id}/favorites` - 获取收藏列表
- `POST /api/users/{user_id}/favorites` - 添加收藏
- `DELETE /api/users/{user_id}/favorites/{dish_id}` - 取消收藏
- `GET /api/users/{user_id}/favorites/{dish_id}/check` - 检查收藏状态

### 用户接口
- `GET /api/users/{user_id}` - 获取用户信息

### 系统接口
- `GET /api/health` - 健康检查
- `POST /api/init-data` - 初始化示例数据

## 👥 测试账号
- 管理员: `admin` / `123456`
- 普通用户: `user1` / `123456`

## 🔧 优化说明

### 已删除的文件
- 移除了所有冗余的数据库脚本
- 删除了重复的应用入口文件
- 清理了不必要的工具类和配置文件
- 移除了旧的JSON数据文件

### 代码优化
- 统一数据库连接管理
- 集成数据初始化功能
- 简化API路由结构
- 统一错误处理机制
- 优化JSON字段处理逻辑