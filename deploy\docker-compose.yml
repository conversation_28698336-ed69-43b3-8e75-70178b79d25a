version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: food_recommendation_mysql
    environment:
      MYSQL_ROOT_PASSWORD: 123456789
      MYSQL_DATABASE: food_recommendation
      MYSQL_USER: food_user
      MYSQL_PASSWORD: 123456789
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ../database/init_db.sql:/docker-entrypoint-initdb.d/init_db.sql
    networks:
      - food_network
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: food_recommendation_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - food_network
    restart: unless-stopped

  # 后端API服务
  backend:
    build:
      context: ../backend
      dockerfile: ../deploy/Dockerfile.backend
    container_name: food_recommendation_backend
    environment:
      - DATABASE_HOST=mysql
      - DATABASE_USER=food_user
      - DATABASE_PASSWORD=food_password
      - DATABASE_NAME=food_recommendation
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - FLASK_ENV=production
    ports:
      - "5000:5000"
    depends_on:
      - mysql
      - redis
    networks:
      - food_network
    restart: unless-stopped
    volumes:
      - ../backend:/app
      - ../data:/app/data

  # 前端Web服务
  frontend:
    build:
      context: ../frontend/food-recommendation-frontend
      dockerfile: ../../deploy/Dockerfile.frontend
    container_name: food_recommendation_frontend
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - food_network
    restart: unless-stopped

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: food_recommendation_nginx
    ports:
      - "8080:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - food_network
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:

networks:
  food_network:
    driver: bridge