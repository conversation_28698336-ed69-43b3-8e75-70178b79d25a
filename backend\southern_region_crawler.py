#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
广西、广东、海南三省美食数据爬虫
精确到市级的菜品和餐厅信息
"""

import json
import random
from datetime import datetime
import uuid

class SouthernRegionFoodCrawler:
    def __init__(self):
        self.dishes = []
        self.restaurants = []
        
        # 三省主要城市配置
        self.regions = {
            "广东": {
                "cities": ["广州", "深圳", "东莞", "佛山", "中山", "珠海", "惠州", "江门", "湛江", "茂名", "肇庆", "汕头", "潮州", "揭阳", "汕尾", "梅州", "河源", "清远", "阳江", "云浮", "韶关"],
                "cuisine_name": "粤菜",
                "specialties": [
                    {"name": "白切鸡", "desc": "广东传统名菜，鸡肉鲜嫩，配蘸料食用", "price_range": [35, 65]},
                    {"name": "蜜汁叉烧", "desc": "色泽红亮，甜而不腻，肉质鲜嫩", "price_range": [40, 70]},
                    {"name": "广式烧鹅", "desc": "皮脆肉嫩，香味浓郁，粤菜精品", "price_range": [80, 120]},
                    {"name": "虾饺", "desc": "广式点心，皮薄馅鲜，晶莹剔透", "price_range": [25, 45]},
                    {"name": "叉烧包", "desc": "广式茶点，包子松软，叉烧香甜", "price_range": [20, 35]},
                    {"name": "干炒牛河", "desc": "河粉爽滑，牛肉嫩滑，火候精准", "price_range": [28, 48]},
                    {"name": "白灼菜心", "desc": "清爽脆嫩，保持原味，健康养生", "price_range": [18, 35]},
                    {"name": "煲仔饭", "desc": "米饭香糯，配菜丰富，锅巴香脆", "price_range": [30, 55]},
                    {"name": "艇仔粥", "desc": "粥品鲜美，配料丰富，营养均衡", "price_range": [15, 28]},
                    {"name": "糖醋里脊", "desc": "酸甜可口，外酥内嫩，老少皆宜", "price_range": [35, 58]},
                    {"name": "蒜蓉蒸扇贝", "desc": "扇贝鲜美，蒜蓉香浓，营养丰富", "price_range": [60, 90]},
                    {"name": "红烧乳鸽", "desc": "肉质鲜嫩，营养价值高，粤菜特色", "price_range": [45, 75]},
                    {"name": "潮汕牛肉丸", "desc": "弹牙爽口，牛肉纯正，潮汕特色", "price_range": [25, 42]},
                    {"name": "广式月饼", "desc": "皮薄馅多，口感丰富，传统工艺", "price_range": [50, 200]},
                    {"name": "早茶点心", "desc": "品种丰富，精工细作，茶餐文化", "price_range": [15, 60]}
                ]
            },
            "广西": {
                "cities": ["南宁", "柳州", "桂林", "梧州", "北海", "防城港", "钦州", "贵港", "玉林", "百色", "贺州", "河池", "来宾", "崇左"],
                "cuisine_name": "桂菜",
                "specialties": [
                    {"name": "螺蛳粉", "desc": "柳州特色，酸辣鲜香，汤汁浓郁", "price_range": [12, 25]},
                    {"name": "桂林米粉", "desc": "桂林名小吃，汤清味鲜，配菜丰富", "price_range": [8, 18]},
                    {"name": "老友粉", "desc": "南宁特色，酸辣开胃，历史悠久", "price_range": [10, 20]},
                    {"name": "白切狗肉", "desc": "广西传统菜，肉质鲜美，滋补佳品", "price_range": [40, 80]},
                    {"name": "荔浦芋扣肉", "desc": "荔浦特产，芋头香糯，扣肉肥美", "price_range": [35, 65]},
                    {"name": "阳朔啤酒鱼", "desc": "阳朔名菜，鱼肉鲜嫩，啤酒香浓", "price_range": [45, 85]},
                    {"name": "竹筒饭", "desc": "民族特色，竹香浓郁，营养丰富", "price_range": [20, 40]},
                    {"name": "五色糯米饭", "desc": "壮族传统，色彩缤纷，寓意吉祥", "price_range": [15, 30]},
                    {"name": "马蹄糕", "desc": "广西甜品，口感Q弹，清甜解腻", "price_range": [8, 18]},
                    {"name": "桂花糕", "desc": "桂林特产，桂花香浓，甜而不腻", "price_range": [12, 25]},
                    {"name": "酸嘢", "desc": "广西小吃，酸甜开胃，水果蔬菜", "price_range": [5, 15]},
                    {"name": "田螺鸭脚煲", "desc": "夜宵经典，香辣过瘾，配菜丰富", "price_range": [25, 50]},
                    {"name": "生榨米粉", "desc": "南宁特色，米粉滑嫩，汤汁鲜美", "price_range": [8, 16]},
                    {"name": "横县鱼生", "desc": "横县特色，刀工精细，鱼肉鲜甜", "price_range": [50, 100]},
                    {"name": "宾阳酸粉", "desc": "宾阳特产，酸辣开胃，口感独特", "price_range": [6, 12]}
                ]
            },
            "海南": {
                "cities": ["海口", "三亚", "三沙", "儋州", "文昌", "琼海", "万宁", "东方", "定安", "屯昌", "澄迈", "临高", "白沙", "昌江", "乐东", "陵水", "保亭", "琼中", "五指山"],
                "cuisine_name": "琼菜",
                "specialties": [
                    {"name": "文昌鸡", "desc": "海南四大名菜之首，肉质鲜美，营养丰富", "price_range": [60, 120]},
                    {"name": "加积鸭", "desc": "海南名菜，鸭肉香嫩，肥而不腻", "price_range": [50, 90]},
                    {"name": "东山羊", "desc": "海南特产，羊肉鲜美，滋补佳品", "price_range": [80, 150]},
                    {"name": "和乐蟹", "desc": "海南名蟹，蟹肉鲜甜，营养价值高", "price_range": [100, 200]},
                    {"name": "椰子鸡", "desc": "海南特色，椰香浓郁，鸡肉嫩滑", "price_range": [70, 130]},
                    {"name": "海南粉", "desc": "海南小吃，粉条爽滑，配菜丰富", "price_range": [8, 18]},
                    {"name": "抱罗粉", "desc": "文昌特色，汤汁清香，口感独特", "price_range": [10, 20]},
                    {"name": "清补凉", "desc": "海南甜品，清热解暑，配料丰富", "price_range": [8, 20]},
                    {"name": "椰子饭", "desc": "海南特色，椰香糯米，营养美味", "price_range": [15, 35]},
                    {"name": "临高乳猪", "desc": "临高特产，皮脆肉嫩，香味浓郁", "price_range": [120, 200]},
                    {"name": "琼海嘉积鸭", "desc": "琼海名菜，制作精细，口感独特", "price_range": [55, 95]},
                    {"name": "三亚海鲜", "desc": "新鲜海产，种类丰富，制作精美", "price_range": [80, 300]},
                    {"name": "椰蓉球", "desc": "海南甜点，椰香浓郁，口感香甜", "price_range": [12, 25]},
                    {"name": "海南鸡饭", "desc": "海南经典，米饭香滑，鸡肉鲜美", "price_range": [25, 45]},
                    {"name": "槟榔花鸡", "desc": "海南特色，槟榔花香，鸡肉嫩滑", "price_range": [65, 110]}
                ]
            }
        }
        
    def generate_comprehensive_data(self):
        """生成三省美食数据"""
        
        dish_id = 1
        restaurant_id = 1
        
        for province, config in self.regions.items():
            cities = config["cities"]
            cuisine_name = config["cuisine_name"]
            specialties = config["specialties"]
            
            # 为每个城市生成数据
            for city in cities:
                # 每个城市生成3-5家餐厅
                city_restaurant_count = random.randint(3, 5)
                city_restaurants = []
                
                for i in range(city_restaurant_count):
                    restaurant_name = self.generate_restaurant_name(province, city, i)
                    
                    restaurant = {
                        "restaurant_id": restaurant_id,
                        "name": restaurant_name,
                        "province": province,
                        "city": city,
                        "cuisine_type": cuisine_name,
                        "rating": round(random.uniform(4.0, 4.9), 1),
                        "review_count": random.randint(200, 5000),
                        "address": f"{province}省{city}市{self.generate_address()}",
                        "phone": f"0{random.randint(10, 99)}{random.randint(1, 9)}-{random.randint(10000000, 99999999)}",
                        "business_hours": "09:00-22:00",
                        "price_range": f"{random.randint(30, 80)}-{random.randint(100, 200)}元",
                        "features": self.generate_restaurant_features(province),
                        "image_url": f"https://images.unsplash.com/photo-{random.randint(1500000000, 1700000000)}?w=600&h=400&fit=crop&auto=format&q=80",
                        "monthly_orders": random.randint(500, 8000),
                        "is_open": True,
                        "created_at": datetime.now().isoformat()
                    }
                    
                    city_restaurants.append(restaurant)
                    self.restaurants.append(restaurant)
                    restaurant_id += 1
                
                # 为每个城市的特色菜生成菜品数据
                for specialty in specialties:
                    # 每个特色菜在每个城市的不同餐厅都有，但价格和做法略有不同
                    for restaurant in city_restaurants:
                        dish = {
                            "dish_id": dish_id,
                            "name": specialty["name"],
                            "province": province,
                            "city": city,
                            "cuisine_type": cuisine_name,
                            "price": round(random.uniform(specialty["price_range"][0], specialty["price_range"][1]), 2),
                            "rating": round(random.uniform(3.8, 4.9), 1),
                            "review_count": random.randint(50, 2000),
                            "description": specialty["desc"] + f"，{restaurant['name']}精心制作",
                            "image_url": f"https://images.unsplash.com/photo-{random.randint(1500000000, 1700000000)}?w=400&h=300&fit=crop&auto=format&q=80",
                            "restaurant_name": restaurant["name"],
                            "restaurant_id": restaurant["restaurant_id"],
                            "region": f"{province}-{city}",
                            "tags": self.generate_dish_tags(province, specialty["name"]),
                            "ingredients": self.generate_ingredients(specialty["name"]),
                            "nutrition": {
                                "calories": random.randint(200, 800),
                                "protein": round(random.uniform(8, 35), 1),
                                "fat": round(random.uniform(5, 40), 1),
                                "carbs": round(random.uniform(10, 60), 1)
                            },
                            "spicy_level": self.get_spicy_level(specialty["name"]),
                            "monthly_sales": random.randint(100, 3000),
                            "is_available": True,
                            "created_at": datetime.now().isoformat(),
                            "data_source": "regional_crawler"
                        }
                        
                        self.dishes.append(dish)
                        dish_id += 1
        
        return {
            "dishes": self.dishes,
            "restaurants": self.restaurants,
            "total_dishes": len(self.dishes),
            "total_restaurants": len(self.restaurants),
            "regions_covered": list(self.regions.keys()),
            "last_updated": datetime.now().isoformat()
        }
    
    def generate_restaurant_name(self, province, city, index):
        """生成餐厅名称"""
        prefixes = {
            "广东": ["粤", "广", "港", "澳", "珠", "岭南"],
            "广西": ["桂", "壮", "民族", "山水", "竹", "螺"],
            "海南": ["椰", "海", "岛", "热带", "文昌", "琼"]
        }
        
        suffixes = ["酒家", "茶餐厅", "食府", "餐厅", "饭店", "楼", "轩", "阁", "居"]
        
        prefix = random.choice(prefixes[province])
        suffix = random.choice(suffixes)
        
        if random.random() < 0.3:  # 30%概率包含城市名
            return f"{city}{prefix}{suffix}"
        else:
            return f"{prefix}{''.join(random.choices('香味园轩阁居福源盛', k=1))}{suffix}"
    
    def generate_address(self):
        """生成地址"""
        districts = ["中心区", "新区", "老城区", "开发区", "高新区", "商业区"]
        streets = ["美食街", "文化路", "建设路", "人民路", "解放路", "中山路", "新华路", "和平路"]
        
        return f"{random.choice(districts)}{random.choice(streets)}{random.randint(1, 999)}号"
    
    def generate_restaurant_features(self, province):
        """生成餐厅特色"""
        common_features = ["包间", "停车位", "WiFi", "可刷卡", "外卖", "团购"]
        
        regional_features = {
            "广东": ["早茶", "点心", "港式", "茶位费"],
            "广西": ["民族特色", "螺蛳粉", "米粉", "夜宵"],
            "海南": ["海鲜", "椰子", "清补凉", "海景"]
        }
        
        features = random.sample(common_features, 3)
        features.extend(random.sample(regional_features[province], 2))
        
        return features
    
    def generate_dish_tags(self, province, dish_name):
        """生成菜品标签"""
        common_tags = ["经典", "传统", "招牌"]
        
        regional_tags = {
            "广东": ["粤式", "港味", "茶点", "清淡", "精致"],
            "广西": ["桂味", "酸辣", "民族", "特色", "地道"],
            "海南": ["琼味", "热带", "海鲜", "椰香", "清爽"]
        }
        
        spicy_dishes = ["螺蛳粉", "老友粉", "酸嘢"]
        if any(spicy in dish_name for spicy in spicy_dishes):
            common_tags.append("辣")
            common_tags.append("开胃")
        
        tags = random.sample(common_tags, 2)
        tags.extend(random.sample(regional_tags[province], 2))
        
        return tags
    
    def generate_ingredients(self, dish_name):
        """根据菜名生成配料"""
        ingredient_map = {
            "鸡": ["鸡肉", "生抽", "料酒", "姜", "蒜"],
            "鸭": ["鸭肉", "五香粉", "生抽", "老抽", "糖"],
            "粉": ["米粉", "韭菜", "豆芽", "花生", "辣椒"],
            "螺": ["螺蛳", "酸笋", "辣椒", "花生", "腐竹"],
            "海鲜": ["海鲜", "姜葱", "蒸鱼豉油", "料酒"],
            "椰": ["椰子", "椰汁", "糯米", "红枣"]
        }
        
        for key, ingredients in ingredient_map.items():
            if key in dish_name:
                return ingredients
        
        return ["主料", "调料", "配菜", "香料"]
    
    def get_spicy_level(self, dish_name):
        """获取菜品辣度"""
        spicy_map = {
            "螺蛳粉": 4,
            "老友粉": 3,
            "酸嘢": 2,
            "白切": 0,
            "蒸": 0,
            "清": 0
        }
        
        for key, level in spicy_map.items():
            if key in dish_name:
                return level
        
        return random.randint(0, 2)
    
    def save_to_file(self, filename="southern_region_food_data.json"):
        """保存数据到文件"""
        data = self.generate_comprehensive_data()
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"数据已保存到 {filename}")
        print(f"菜品总数: {data['total_dishes']}")
        print(f"餐厅总数: {data['total_restaurants']}")
        print(f"覆盖地区: {', '.join(data['regions_covered'])}")
        
        return data

def main():
    """主函数"""
    crawler = SouthernRegionFoodCrawler()
    data = crawler.save_to_file()
    
    # 显示统计信息
    print(f"\n=== 数据统计 ===")
    
    for province in ["广东", "广西", "海南"]:
        province_dishes = [d for d in data["dishes"] if d["province"] == province]
        province_restaurants = [r for r in data["restaurants"] if r["province"] == province]
        cities = list(set([d["city"] for d in province_dishes]))
        
        print(f"{province}: {len(province_dishes)}道菜品, {len(province_restaurants)}家餐厅, {len(cities)}个城市")
    
    print(f"\n=== 样本数据 ===")
    for dish in data["dishes"][:5]:
        print(f"- {dish['name']} ({dish['province']}-{dish['city']}) - 价格{dish['price']}元 - {dish['restaurant_name']}")

if __name__ == "__main__":
    main()