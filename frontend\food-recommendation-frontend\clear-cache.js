#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🧹 清理前端缓存...');

// 要删除的目录和文件
const pathsToDelete = [
  'node_modules/.vite',
  'node_modules/.cache',
  'dist',
  '.vite',
  'vite.config.ts.timestamp-*'
];

function deleteRecursive(targetPath) {
  if (fs.existsSync(targetPath)) {
    if (fs.lstatSync(targetPath).isDirectory()) {
      fs.readdirSync(targetPath).forEach((file) => {
        const curPath = path.join(targetPath, file);
        deleteRecursive(curPath);
      });
      fs.rmdirSync(targetPath);
      console.log(`✅ 删除目录: ${targetPath}`);
    } else {
      fs.unlinkSync(targetPath);
      console.log(`✅ 删除文件: ${targetPath}`);
    }
  }
}

// 删除缓存目录
pathsToDelete.forEach(targetPath => {
  try {
    if (targetPath.includes('*')) {
      // 处理通配符
      const dir = path.dirname(targetPath);
      const pattern = path.basename(targetPath).replace('*', '');
      if (fs.existsSync(dir)) {
        fs.readdirSync(dir).forEach(file => {
          if (file.startsWith(pattern)) {
            deleteRecursive(path.join(dir, file));
          }
        });
      }
    } else {
      deleteRecursive(targetPath);
    }
  } catch (error) {
    console.log(`⚠️  无法删除 ${targetPath}: ${error.message}`);
  }
});

console.log('🎉 缓存清理完成！');
console.log('💡 建议运行: npm install && npm run dev');
