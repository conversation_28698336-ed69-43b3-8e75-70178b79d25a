import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { getDishImageUrl, handleImageError } from '@/utils/imageUtils';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Star, MapPin, Clock, Phone, Users, ArrowLeft, Heart } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { restaurantApi } from '@/services/api';
import LoginModal from '@/components/LoginModal';

interface Restaurant {
  restaurant_id: number;
  name: string;
  cuisine_type: string;
  rating: number;
  review_count: number;
  address: string;
  phone: string;
  business_hours: string;
  price_range: string;
  features: string[];
  image_url: string;
  monthly_orders: number;
  is_open: boolean;
}

interface RestaurantDish {
  dish_id: number;
  name: string;
  price: number;
  rating: number;
  description: string;
  image_url: string;
  monthly_sales: number;
}

const RestaurantDetailPage: React.FC = () => {
  const navigate = useNavigate();
  const { restaurantName } = useParams<{ restaurantName: string }>();
  const { user, isAuthenticated } = useAuth();
  const [restaurant, setRestaurant] = useState<Restaurant | null>(null);
  const [dishes, setDishes] = useState<RestaurantDish[]>([]);
  const [loading, setLoading] = useState(true);
  const [showLoginModal, setShowLoginModal] = useState(false);

  const handleGoBack = () => {
    navigate(-1);
  };

  // 模拟餐厅数据
  const mockRestaurant: Restaurant = {
    restaurant_id: 1,
    name: decodeURIComponent(restaurantName || '粤味轩'),
    cuisine_type: '粤菜',
    rating: 4.6,
    review_count: 1234,
    address: '广东省广州市天河区珠江新城花城大道123号',
    phone: '020-12345678',
    business_hours: '10:00 - 22:00',
    price_range: '50-150元',
    features: ['包间', '停车位', 'WiFi', '儿童座椅', '外卖'],
    image_url: 'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    monthly_orders: 3200,
    is_open: true
  };

  const mockDishes: RestaurantDish[] = [
    {
      dish_id: 1,
      name: '白切鸡',
      price: 45.0,
      rating: 4.5,
      description: '粤菜经典，鸡肉鲜嫩，配蘸料食用',
      image_url: 'https://images.unsplash.com/photo-1604503468506-a8da13d82791?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      monthly_sales: 856
    },
    {
      dish_id: 2,
      name: '蒜蓉蒸扇贝',
      price: 68.0,
      rating: 4.7,
      description: '扇贝鲜美，蒜蓉香浓，营养丰富',
      image_url: 'https://images.unsplash.com/photo-1559847844-d8553cd73ca3?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      monthly_sales: 642
    },
    {
      dish_id: 3,
      name: '广式烧鹅',
      price: 78.0,
      rating: 4.8,
      description: '皮脆肉嫩，香味浓郁，粤菜精品',
      image_url: 'https://images.unsplash.com/photo-1598515214211-89d3c73ae83b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      monthly_sales: 423
    },
    {
      dish_id: 4,
      name: '虾饺',
      price: 32.0,
      rating: 4.4,
      description: '广式点心，皮薄馅鲜，晶莹剔透',
      image_url: 'https://images.unsplash.com/photo-1563379091339-03246963d51a?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      monthly_sales: 1205
    }
  ];

  useEffect(() => {
    const fetchRestaurantDetail = async () => {
      setLoading(true);
      try {
        const data = await restaurantApi.getRestaurantDetail(decodeURIComponent(restaurantName || ''));
        if (data) {
          setRestaurant(data);
          setDishes(data.dishes || []);
        } else {
          // 如果API失败，使用模拟数据
          setRestaurant(mockRestaurant);
          setDishes(mockDishes);
        }
      } catch (error) {
        console.error('获取餐厅详情失败:', error);
        setRestaurant(mockRestaurant);
        setDishes(mockDishes);
      } finally {
        setLoading(false);
      }
    };

    fetchRestaurantDetail();
  }, [restaurantName]);

  const StarRating: React.FC<{ rating: number; size?: 'sm' | 'md' | 'lg' }> = ({ rating, size = 'md' }) => {
    const sizeClasses = {
      sm: 'h-4 w-4',
      md: 'h-5 w-5',
      lg: 'h-6 w-6'
    };

    return (
      <div className="flex gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`${sizeClasses[size]} ${
              star <= rating 
                ? 'fill-yellow-400 text-yellow-400' 
                : 'text-gray-300'
            }`}
          />
        ))}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-orange-50 to-green-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
          <p className="text-gray-600">正在加载餐厅详情...</p>
        </div>
      </div>
    );
  }

  if (!restaurant) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-orange-50 to-green-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">餐厅不存在</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-green-50">
      {/* 头部导航 */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center gap-4">
              <Button variant="ghost" size="sm" onClick={handleGoBack}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回
              </Button>
              <h1 className="text-2xl font-bold text-orange-600">美食推荐</h1>
            </div>
            <nav className="hidden md:flex space-x-8">
              <button onClick={() => navigate('/')} className="text-gray-500 hover:text-orange-600">首页</button>
              <button onClick={() => navigate('/')} className="text-gray-500 hover:text-orange-600">推荐</button>
              <button onClick={() => navigate('/search')} className="text-gray-500 hover:text-orange-600">搜索</button>
              <button onClick={() => navigate('/profile')} className="text-gray-500 hover:text-orange-600">我的</button>
            </nav>
            {!isAuthenticated && (
              <Button 
                className="bg-orange-500 hover:bg-orange-600"
                onClick={() => setShowLoginModal(true)}
              >
                登录
              </Button>
            )}
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 左侧主要内容 */}
          <div className="lg:col-span-2 space-y-6">
            {/* 餐厅主要信息 */}
            <Card className="overflow-hidden">
              <div className="relative">
                <img
                  src={restaurant.image_url}
                  alt={restaurant.name}
                  className="w-full h-64 md:h-80 object-cover"
                  onError={handleImageError}
                />
                <div className="absolute top-4 right-4">
                  <Badge className={`${restaurant.is_open ? 'bg-green-500' : 'bg-red-500'} text-white`}>
                    {restaurant.is_open ? '营业中' : '已打烊'}
                  </Badge>
                </div>
              </div>
              
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h1 className="text-3xl font-bold text-gray-900 mb-2">{restaurant.name}</h1>
                    <div className="flex items-center gap-4 mb-3">
                      <Badge className="bg-orange-100 text-orange-800 hover:bg-orange-200">
                        {restaurant.cuisine_type}
                      </Badge>
                      <div className="flex items-center gap-1">
                        <MapPin className="h-4 w-4 text-gray-500" />
                        <span className="text-gray-600">{restaurant.price_range}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Users className="h-4 w-4 text-gray-500" />
                        <span className="text-gray-600">月售{restaurant.monthly_orders}单</span>
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="flex items-center gap-2 mb-1">
                      <StarRating rating={restaurant.rating} />
                      <span className="text-lg font-semibold">{restaurant.rating}</span>
                    </div>
                    <span className="text-sm text-gray-600">({restaurant.review_count}条评价)</span>
                  </div>
                </div>

                <div className="flex flex-wrap gap-2 mb-4">
                  {restaurant.features.map((feature, index) => (
                    <Badge key={index} variant="outline">
                      {feature}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* 餐厅信息标签页 */}
            <Card>
              <CardContent className="p-6">
                <Tabs defaultValue="info" className="w-full">
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="info">餐厅信息</TabsTrigger>
                    <TabsTrigger value="dishes">热门菜品</TabsTrigger>
                    <TabsTrigger value="reviews">用户评价</TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="info" className="mt-4">
                    <div className="space-y-4">
                      <div className="flex items-center gap-3">
                        <MapPin className="h-5 w-5 text-gray-500" />
                        <div>
                          <div className="font-medium">地址</div>
                          <div className="text-sm text-gray-600">{restaurant.address}</div>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <Phone className="h-5 w-5 text-gray-500" />
                        <div>
                          <div className="font-medium">电话</div>
                          <div className="text-sm text-gray-600">{restaurant.phone}</div>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <Clock className="h-5 w-5 text-gray-500" />
                        <div>
                          <div className="font-medium">营业时间</div>
                          <div className="text-sm text-gray-600">{restaurant.business_hours}</div>
                        </div>
                      </div>
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="dishes" className="mt-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {dishes.map((dish) => (
                        <div key={dish.dish_id} className="flex gap-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                             onClick={() => navigate(`/dish/${dish.dish_id}`)}>
                          <img
                            src={getDishImageUrl(dish.name, dish.image_url)}
                            alt={dish.name}
                            className="w-20 h-20 object-cover rounded"
                            onError={handleImageError}
                          />
                          <div className="flex-1">
                            <h4 className="font-medium mb-1">{dish.name}</h4>
                            <p className="text-sm text-gray-600 mb-2">{dish.description}</p>
                            <div className="flex justify-between items-center">
                              <span className="text-orange-600 font-bold">¥{dish.price}</span>
                              <div className="flex items-center gap-1">
                                <StarRating rating={dish.rating} size="sm" />
                                <span className="text-xs text-gray-500">月售{dish.monthly_sales}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="reviews" className="mt-4">
                    <div className="text-center py-8 text-gray-500">
                      <Star className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">暂无用户评价</p>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </div>

          {/* 右侧信息 */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">快速信息</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">人均消费</span>
                    <span className="font-medium">{restaurant.price_range}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">月订单量</span>
                    <span className="font-medium">{restaurant.monthly_orders}单</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">菜品数量</span>
                    <span className="font-medium">{dishes.length}道</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">营业状态</span>
                    <span className={`font-medium ${restaurant.is_open ? 'text-green-600' : 'text-red-600'}`}>
                      {restaurant.is_open ? '营业中' : '已打烊'}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">推荐菜品</CardTitle>
                <CardDescription>本店最受欢迎的菜品</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {dishes.slice(0, 3).map((dish) => (
                    <div key={dish.dish_id} className="flex gap-3 cursor-pointer hover:bg-gray-50 p-2 rounded transition-colors"
                         onClick={() => navigate(`/dish/${dish.dish_id}`)}>
                      <img
                        src={getDishImageUrl(dish.name, dish.image_url)}
                        alt={dish.name}
                        className="w-12 h-12 object-cover rounded"
                        onError={handleImageError}
                      />
                      <div className="flex-1">
                        <h4 className="font-medium text-sm">{dish.name}</h4>
                        <div className="flex justify-between items-center mt-1">
                          <span className="text-orange-600 font-medium text-sm">¥{dish.price}</span>
                          <StarRating rating={dish.rating} size="sm" />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* 登录模态框 */}
      <LoginModal 
        open={showLoginModal} 
        onOpenChange={setShowLoginModal} 
      />
    </div>
  );
};

export default RestaurantDetailPage;