import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { handleImageError, getDishImageUrl } from '@/utils/imageUtils';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from '@/components/ui/carousel';
import { Star, MapPin, Clock, Heart, TrendingUp, User, Sparkles, Award, ChefHat } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import LoginModal from '@/components/LoginModal';

interface Dish {
  dish_id?: number;
  id?: number;
  name: string;
  cuisine_type?: string;
  cuisine?: string;
  price: number;
  rating: number;
  review_count?: number;
  description: string;
  image_url?: string;
  image?: string;
  restaurant_name?: string;
  region: string;
  recommendation_reason?: string;
}

const HomePage: React.FC = () => {
  const navigate = useNavigate();
  const { user, isAuthenticated, logout } = useAuth();
  const [selectedCuisine, setSelectedCuisine] = useState<string>('');
  const [featuredDishes, setFeaturedDishes] = useState<Dish[]>([]);
  const [popularDishes, setPopularDishes] = useState<Dish[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [showLoginModal, setShowLoginModal] = useState(false);

  const cuisineTypes = [
    { name: '全部', value: '', emoji: '🍽️' },
    { name: '粤菜', value: '粤菜', emoji: '🥢' },
    { name: '桂菜', value: '桂菜', emoji: '🍲' },
    { name: '琼菜', value: '琼菜', emoji: '🥥' }
  ];

  // 模拟精选推荐数据
  const mockFeaturedDishes: Dish[] = [
    {
      dish_id: 1,
      name: '文昌鸡',
      cuisine_type: '琼菜',
      price: 78.0,
      rating: 4.9,
      review_count: 40,
      description: '海南文昌鸡，肉质细嫩，皮薄骨软，味道鲜美',
      image_url: 'https://images.unsplash.com/photo-1598103442097-8b74394b95c6?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      restaurant_name: '文昌鸡专门店',
      region: '海南',
      recommendation_reason: '高评分推荐'
    },
    {
      dish_id: 2,
      name: '豆汁焦圈',
      cuisine_type: '京菜',
      price: 85.6,
      rating: 4.9,
      review_count: 65,
      description: '来自川味人家的招牌豆汁焦圈，选用优质食材，传统工艺制作，口感正宗',
      image_url: 'https://images.unsplash.com/photo-1569718212165-3a8278d5f624?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      restaurant_name: '川味人家',
      region: '广东',
      recommendation_reason: '地方特色'
    },
    {
      dish_id: 3,
      name: '海南鸡饭',
      cuisine_type: '琼菜',
      price: 30.0,
      rating: 4.3,
      review_count: 156,
      description: '海南风味鸡饭，香米配白切鸡，口感丰富',
      image_url: 'https://images.unsplash.com/photo-1512058564366-18510be2db19?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      restaurant_name: '海南风情',
      region: '海南',
      recommendation_reason: '热门选择'
    },
    {
      dish_id: 4,
      name: '叉烧包',
      cuisine_type: '粤菜',
      price: 18.0,
      rating: 4.4,
      review_count: 95,
      description: '经典粤式茶点，叉烧馅料丰富，包子皮松软',
      image_url: 'https://images.unsplash.com/photo-1563379091339-03246963d96a?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      restaurant_name: '港式茶餐厅',
      region: '广东',
      recommendation_reason: '主厨推荐'
    },
    {
      dish_id: 5,
      name: '螺蛳粉',
      cuisine_type: '桂菜',
      price: 22.0,
      rating: 4.1,
      review_count: 203,
      description: '广西特色小吃，酸辣鲜香，回味无穷',
      image_url: 'https://images.unsplash.com/photo-1582878826629-29b7ad1cdc43?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      restaurant_name: '柳州风味',
      region: '广西',
      recommendation_reason: '人气之选'
    },
    {
      dish_id: 6,
      name: '椰子鸡',
      cuisine_type: '琼菜',
      price: 68.0,
      rating: 4.6,
      review_count: 87,
      description: '海南特色汤品，椰香浓郁，鸡肉鲜嫩',
      image_url: 'https://images.unsplash.com/photo-1604908176997-125f25cc6f3d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      restaurant_name: '椰风海韵',
      region: '海南',
      recommendation_reason: '经典美味'
    },
    {
      dish_id: 3375,
      name: '西湖醋鱼',
      cuisine_type: '浙菜',
      price: 88.0,
      rating: 4.6,
      review_count: 283,
      description: '杭州名菜，酸甜适口，鱼肉鲜嫩',
      image_url: 'https://i.pinimg.com/originals/ec/cc/27/eccc278034bf1cc3551d6e3c853ee111.jpg',
      restaurant_name: '地道风味',
      region: '浙江',
      recommendation_reason: '杭州特色'
    }
  ];

  // 菜品去重函数 - 按名称去重，保留评分最高的记录
  const deduplicateDishes = (dishes: any[]) => {
    const dishMap = new Map();
    
    dishes.forEach(dish => {
      const name = dish.name;
      if (!dishMap.has(name) || dish.rating > dishMap.get(name).rating) {
        dishMap.set(name, dish);
      }
    });
    
    return Array.from(dishMap.values());
  };

  // 获取更多样化的菜品数据
  const getVariedDishes = (dishes: Dish[], count: number) => {
    const cuisineGroups = new Map();
    
    // 按菜系分组
    dishes.forEach(dish => {
      const cuisine = dish.cuisine_type;
      if (!cuisineGroups.has(cuisine)) {
        cuisineGroups.set(cuisine, []);
      }
      cuisineGroups.get(cuisine).push(dish);
    });
    
    // 从每个菜系中均匀选择菜品
    const result: Dish[] = [];
    const cuisines = Array.from(cuisineGroups.keys());
    let currentIndex = 0;
    
    while (result.length < count && cuisines.length > 0) {
      const cuisine = cuisines[currentIndex % cuisines.length];
      const cuisineDishes = cuisineGroups.get(cuisine);
      
      if (cuisineDishes && cuisineDishes.length > 0) {
        const dish = cuisineDishes.shift(); // 取出第一个
        if (dish) {
          result.push(dish);
        }
        
        // 如果这个菜系没有菜品了，移除它
        if (cuisineDishes.length === 0) {
          cuisines.splice(cuisines.indexOf(cuisine), 1);
          cuisineGroups.delete(cuisine);
        }
      }
      
      currentIndex++;
    }
    
    return result;
  };

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // 获取更多数据以确保去重后仍有足够的菜品
        const response = await fetch('http://localhost:5000/api/dishes?per_page=500');
        if (response.ok) {
          const data = await response.json();
          
          console.log('API返回的数据:', data); // 调试日志
          
          let dishes = [];
          if (data.success && data.data && Array.isArray(data.data)) {
            dishes = data.data;
          } else if (Array.isArray(data.dishes)) {
            dishes = data.dishes;
          } else if (Array.isArray(data)) {
            dishes = data;
          } else {
            console.log('数据格式不正确，使用模拟数据');
            dishes = [];
          }
          
          console.log('处理前的菜品数据量:', dishes.length); // 调试日志
          
          const formattedDishes = Array.isArray(dishes) ? dishes.map((dish: any) => ({
            dish_id: dish.dish_id || dish.id,
            name: dish.name || '美味菜品',
            cuisine_type: dish.cuisine_type || dish.cuisine || '中餐',
            price: dish.price || 0,
            rating: dish.rating || 4.0,
            review_count: dish.review_count || Math.floor(Math.random() * 100) + 10,
            description: dish.description || '美味可口的特色菜品',
            image_url: dish.image_url || dish.image || 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=300&h=200&fit=crop',
            restaurant_name: dish.restaurant_name || '美食餐厅',
            region: dish.region || '广东'
          })) : [];

          // 🔥 去重处理 - 按菜品名称去重，保留评分最高的记录
          const uniqueDishes = deduplicateDishes(formattedDishes);
          console.log('去重后的菜品数据量:', uniqueDishes.length); // 调试日志

          // 精选推荐：选择评分最高的6个不重复菜品
          const sortedByRating = [...uniqueDishes].sort((a, b) => b.rating - a.rating);
          const featured = sortedByRating.slice(0, 6).map((dish: Dish, index: number) => ({
            ...dish,
            recommendation_reason: ['高评分推荐', '地方特色', '热门选择', '主厨推荐', '人气之选', '经典美味'][index]
          }));
          setFeaturedDishes(featured.length > 0 ? featured : mockFeaturedDishes);

          // 热门菜品：使用多样化选择策略
          let filteredDishes = uniqueDishes;
          if (selectedCuisine) {
            filteredDishes = uniqueDishes.filter((dish: Dish) => 
              dish.cuisine_type === selectedCuisine || dish.cuisine === selectedCuisine
            );
          }
          
          // 获取多样化的菜品展示
          const variedDishes = getVariedDishes(filteredDishes, 24);
          setPopularDishes(variedDishes);
          
          console.log('最终展示的菜品数量:', variedDishes.length); // 调试日志
        } else {
          console.log('API响应失败，使用模拟数据');
          setFeaturedDishes(mockFeaturedDishes);
          setPopularDishes(mockFeaturedDishes);
        }
      } catch (error) {
        console.error('获取数据失败:', error);
        // 确保即使API失败，精选推荐也能正常显示
        setFeaturedDishes(mockFeaturedDishes);
        setPopularDishes(mockFeaturedDishes);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [selectedCuisine]);

  const handleCuisineFilter = (cuisine: string) => {
    setSelectedCuisine(cuisine);
  };

  // 自动轮播功能
  useEffect(() => {
    if (featuredDishes.length > 3) {
      const interval = setInterval(() => {
        setCurrentSlide(prev => {
          const maxSlide = Math.max(0, featuredDishes.length - 3);
          return prev >= maxSlide ? 0 : prev + 1;
        });
      }, 3000); // 每3秒切换一次

      return () => clearInterval(interval);
    }
  }, [featuredDishes.length]);

  // 添加一个useEffect来处理滚动，当popularDishes更新后执行
  useEffect(() => {
    if (!loading && popularDishes.length > 0 && selectedCuisine !== '') {
      // 延迟执行滚动，确保DOM已更新
      const timer = setTimeout(() => {
        const hotDishesSection = document.getElementById('hot-dishes-section');
        if (hotDishesSection) {
          hotDishesSection.scrollIntoView({ 
            behavior: 'smooth',
            block: 'start'
          });
        }
      }, 200);
      
      return () => clearTimeout(timer);
    }
  }, [selectedCuisine, popularDishes, loading]);

  const handleDishClick = (dish: Dish) => {
    const dishId = dish.dish_id || dish.id;
    navigate(`/dish/${dishId}`);
  };

  const handleFavorite = (dish: Dish, e: React.MouseEvent) => {
    e.stopPropagation();
    if (!isAuthenticated || !user) {
      // 如果用户未登录，显示登录模态框
      setShowLoginModal(true);
      return;
    }
    console.log('收藏菜品:', dish.name);
    // 这里可以添加真实的收藏API调用
  };

  const DishCard: React.FC<{ dish: Dish; showReason?: boolean }> = React.memo(({ dish, showReason = false }) => {
    return (
    <Card
      className="group hover:shadow-2xl transition-all duration-500 cursor-pointer bg-white/90 backdrop-blur-sm hover:bg-white hover:scale-[1.02] overflow-hidden h-full flex flex-col w-full"
      onClick={() => navigate(`/dish/${dish.dish_id || dish.id}`)}
    >
      <div className="relative overflow-hidden">
        <img
          src={getDishImageUrl(dish.name, dish.image_url)}
          alt={dish.name}
          className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-700"
          onError={handleImageError}
          loading="lazy"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        <div className="absolute top-3 right-3">
          <Button 
            size="sm" 
            variant="ghost" 
            className="bg-white/90 hover:bg-white shadow-lg backdrop-blur-sm rounded-full w-10 h-10 p-0 hover:scale-110 transition-all duration-200"
            onClick={(e) => handleFavorite(dish, e)}
          >
            <Heart className="h-4 w-4 text-red-500 hover:fill-red-500 transition-all duration-200" />
          </Button>
        </div>
        {showReason && dish.recommendation_reason && (
          <Badge className="absolute top-3 left-3 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white shadow-lg">
            <Sparkles className="h-3 w-3 mr-1" />
            {dish.recommendation_reason}
          </Badge>
        )}
      </div>
      <CardContent className="p-5">
        <div className="flex justify-between items-start mb-3">
          <h3 className="font-bold text-lg text-gray-900 group-hover:text-orange-600 transition-colors duration-200">{dish.name}</h3>
          <span className="text-xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">¥{dish.price}</span>
        </div>
        
        <div className="flex items-center gap-2 mb-3">
          <Badge variant="secondary" className="text-xs bg-orange-100 text-orange-700 hover:bg-orange-200">
            {dish.cuisine_type || dish.cuisine}
          </Badge>
          <div className="flex items-center gap-1">
            <MapPin className="h-3 w-3 text-gray-500" />
            <span className="text-xs text-gray-500">{dish.region}</span>
          </div>
        </div>

        <p className="text-sm text-gray-600 mb-4 line-clamp-2 leading-relaxed">{dish.description}</p>

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1">
            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
            <span className="text-sm font-medium">{dish.rating}</span>
            <span className="text-xs text-gray-500">({dish.review_count || 0})</span>
          </div>
          <div className="flex items-center gap-1 text-xs text-gray-500">
            <Clock className="h-3 w-3" />
            <span>{dish.restaurant_name || '美食餐厅'}</span>
          </div>
        </div>
      </CardContent>
    </Card>
    );
  }, (prevProps, nextProps) => {
    return prevProps.dish.dish_id === nextProps.dish.dish_id && 
           prevProps.dish.name === nextProps.dish.name &&
           prevProps.showReason === nextProps.showReason;
  });

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-orange-50 via-red-50 to-yellow-50 flex items-center justify-center">
        <div className="text-center">
          <div className="relative">
            <div className="animate-spin rounded-full h-16 w-16 border-4 border-orange-200 border-t-orange-500 mx-auto mb-6"></div>
            <ChefHat className="h-8 w-8 text-orange-500 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
          </div>
          <p className="text-gray-700 text-lg font-medium">正在为您准备美食推荐...</p>
          <p className="text-gray-500 text-sm mt-2">请稍候片刻</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-red-50 to-yellow-50">
      {/* 头部导航 */}
      <header className="bg-white/95 backdrop-blur-md shadow-lg border-b border-orange-100 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-18 py-2">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center shadow-lg transform hover:scale-105 transition-transform duration-200">
                <span className="text-white font-bold text-xl">美</span>
              </div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
                美食推荐
              </h1>
            </div>
            <nav className="hidden md:flex space-x-1">
              <button className="px-6 py-3 rounded-full bg-gradient-to-r from-orange-500 to-red-500 text-white font-medium shadow-lg transform hover:scale-105 transition-all duration-200 hover:shadow-xl">
                首页
              </button>
              <button 
                onClick={() => navigate('/search')} 
                className="px-6 py-3 rounded-full text-gray-600 hover:text-orange-600 hover:bg-orange-50 transition-all duration-200 font-medium"
              >
                搜索
              </button>
              <button 
                onClick={() => navigate('/profile')} 
                className="px-6 py-3 rounded-full text-gray-600 hover:text-orange-600 hover:bg-orange-50 transition-all duration-200 font-medium"
              >
                我的
              </button>
            </nav>
            {isAuthenticated && user ? (
              <div className="flex items-center gap-3">
                <span className="text-gray-700 font-medium">欢迎，{user.username}</span>
                <Button 
                  variant="outline" 
                  onClick={() => {
                    logout();
                    navigate('/');
                  }}
                  className="rounded-full px-4 py-2"
                >
                  退出登录
                </Button>
              </div>
            ) : (
              <Button 
                className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 rounded-full px-6 py-3"
                onClick={() => setShowLoginModal(true)}
              >
                <User className="h-4 w-4 mr-2" />
                登录
              </Button>
            )}
          </div>
        </div>
      </header>

      {/* 欢迎区域 */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-orange-100 via-red-50 to-yellow-100"></div>
        <div className="relative max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-6xl md:text-7xl font-bold bg-gradient-to-r from-orange-600 via-red-600 to-yellow-600 bg-clip-text text-transparent mb-8 leading-tight">
              发现美味
              <br />
              <span className="text-5xl md:text-6xl">享受生活</span>
            </h2>
            <p className="text-xl text-gray-700 mb-10 max-w-3xl mx-auto leading-relaxed">
              为您精心推荐广西、广东、海南三省的地道特色美食
              <br />
              <span className="text-orange-600 font-semibold text-2xl">让每一餐都成为美好回忆</span>
            </p>
            <div className="flex flex-wrap justify-center gap-6 mb-12">
              <div className="flex items-center space-x-3 bg-white/90 backdrop-blur-sm rounded-2xl px-6 py-4 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                <span className="text-3xl">🥢</span>
                <span className="text-gray-700 font-semibold text-lg">正宗粤菜</span>
              </div>
              <div className="flex items-center space-x-3 bg-white/90 backdrop-blur-sm rounded-2xl px-6 py-4 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                <span className="text-3xl">🍲</span>
                <span className="text-gray-700 font-semibold text-lg">特色桂菜</span>
              </div>
              <div className="flex items-center space-x-3 bg-white/90 backdrop-blur-sm rounded-2xl px-6 py-4 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                <span className="text-3xl">🥥</span>
                <span className="text-gray-700 font-semibold text-lg">海南风味</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 精选推荐轮播 */}
      <section className="py-16 bg-gradient-to-r from-orange-50 to-red-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <div className="inline-flex items-center gap-3 mb-6">
              <div className="w-14 h-14 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center shadow-xl">
                <TrendingUp className="h-7 w-7 text-white" />
              </div>
              <h2 className="text-4xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
                精选推荐
              </h2>
            </div>
            <p className="text-gray-600 text-xl">为您精心挑选的人气美食</p>
          </div>
          
          {featuredDishes.length > 0 ? (
            <div className="relative max-w-6xl mx-auto">
              {/* 轮播容器 */}
              <div className="overflow-hidden rounded-2xl">
                <div 
                  className="flex transition-transform duration-500 ease-in-out"
                  style={{ transform: `translateX(-${currentSlide * (100/3)}%)` }}
                >
                  {featuredDishes.map((dish, index) => (
                    <div key={dish.dish_id || dish.id || index} className="w-1/3 flex-shrink-0 px-2">
                      <div className="h-full flex flex-col">
                        <DishCard dish={dish} showReason={true} />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              
              {/* 轮播指示器 */}
              <div className="flex justify-center mt-8 space-x-2">
                {Array.from({ length: Math.max(1, featuredDishes.length - 2) }).map((_, index) => (
                  <button
                    key={index}
                    className={`w-2 h-2 rounded-full transition-all duration-300 ${
                      index === currentSlide 
                        ? 'bg-orange-500 scale-125' 
                        : 'bg-orange-300 hover:bg-orange-400'
                    }`}
                    onClick={() => setCurrentSlide(index)}
                  />
                ))}
              </div>
            </div>
          ) : (
            <div className="text-center py-12 text-gray-500">
              <div className="animate-pulse">
                <ChefHat className="h-16 w-16 mx-auto mb-4 opacity-50" />
                <p className="text-lg">正在加载精选推荐...</p>
              </div>
            </div>
          )}
        </div>
      </section>

      {/* 菜系分类 */}
      <section className="py-12 bg-white/60 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-10">
            <div className="inline-flex items-center gap-3 mb-4">
              <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center shadow-lg">
                <Award className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-3xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
                南方三省菜系
              </h3>
            </div>
            <p className="text-gray-600 text-lg">广东、广西、海南特色美食</p>
          </div>
          <div className="flex flex-wrap gap-6 justify-center">
            {cuisineTypes.map((cuisine) => (
              <Button
                key={cuisine.value}
                variant={selectedCuisine === cuisine.value ? "default" : "outline"}
                onClick={() => handleCuisineFilter(cuisine.value)}
                className={selectedCuisine === cuisine.value 
                  ? "bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white shadow-xl transform hover:scale-110 transition-all duration-300 rounded-2xl px-8 py-4 font-semibold text-lg" 
                  : "border-2 border-orange-200 text-orange-600 hover:bg-gradient-to-r hover:from-orange-50 hover:to-red-50 hover:border-orange-300 rounded-2xl px-8 py-4 font-semibold text-lg transition-all duration-300 hover:shadow-lg hover:scale-105 bg-white/80 backdrop-blur-sm"
                }
              >
                <span className="mr-2 text-xl">{cuisine.emoji}</span>
                {cuisine.name}
              </Button>
            ))}
          </div>
        </div>
      </section>

      {/* 热门菜品网格 */}
      <section id="hot-dishes-section" className="py-16 bg-white/60 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <div className="inline-flex items-center gap-3 mb-6">
              <div className="w-14 h-14 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-full flex items-center justify-center shadow-xl">
                <Star className="h-7 w-7 text-white" />
              </div>
              <h2 className="text-4xl font-bold bg-gradient-to-r from-yellow-600 to-orange-600 bg-clip-text text-transparent">
                南方三省热门菜品
              </h2>
            </div>
            <p className="text-gray-600 text-xl">广东、广西、海南特色美食，精致到每个城市</p>
          </div>
          
          {popularDishes.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
              {popularDishes.map((dish, index) => (
                <DishCard key={`popular-${dish.dish_id || dish.id}-${dish.name}`} dish={dish} />
              ))}
            </div>
          ) : (
            <div className="text-center py-12 text-gray-500">
              <div className="animate-pulse">
                <Star className="h-16 w-16 mx-auto mb-4 opacity-50" />
                <p className="text-lg">正在加载热门菜品...</p>
              </div>
            </div>
          )}
        </div>
      </section>

      {/* 底部 */}
      <footer className="bg-gradient-to-r from-gray-900 to-gray-800 text-white border-t mt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="flex items-center justify-center space-x-3 mb-6">
              <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center shadow-lg">
                <span className="text-white font-bold text-lg">美</span>
              </div>
              <h3 className="text-2xl font-bold bg-gradient-to-r from-orange-400 to-red-400 bg-clip-text text-transparent">
                美食推荐系统
              </h3>
            </div>
            <p className="text-gray-300 text-lg mb-4">发现美味，享受生活</p>
            <p className="text-gray-400">&copy; 2024 美食推荐系统. 保留所有权利.</p>
          </div>
        </div>
      </footer>

      {/* 登录模态框 */}
      <LoginModal 
        open={showLoginModal} 
        onOpenChange={setShowLoginModal} 
      />
    </div>
  );
};

export default HomePage;
