#!/bin/bash

# 美食推荐系统部署脚本
# 使用方法: ./deploy.sh [环境] [操作]
# 环境: dev|prod
# 操作: build|start|stop|restart|logs|status

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
PROJECT_NAME="food-recommendation-system"
COMPOSE_FILE="docker-compose.yml"
ENV_FILE=".env"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker和Docker Compose
check_dependencies() {
    log_info "检查系统依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log_success "系统依赖检查通过"
}

# 创建环境配置文件
create_env_file() {
    local env=$1
    log_info "创建环境配置文件..."
    
    cat > $ENV_FILE << EOF
# 环境配置
ENVIRONMENT=$env

# 数据库配置
MYSQL_ROOT_PASSWORD=123456789
MYSQL_DATABASE=food_recommendation
MYSQL_USER=food_user
MYSQL_PASSWORD=123456789

# Redis配置
REDIS_PASSWORD=

# 应用配置
API_SECRET_KEY=your-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key-here

# 外部服务配置
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-email-password

# 监控配置
ENABLE_MONITORING=true
LOG_LEVEL=INFO
EOF
    
    log_success "环境配置文件创建完成: $ENV_FILE"
}

# 构建镜像
build_images() {
    log_info "开始构建Docker镜像..."
    
    # 构建后端镜像
    log_info "构建后端镜像..."
    docker-compose build backend
    
    # 构建前端镜像
    log_info "构建前端镜像..."
    docker-compose build frontend
    
    log_success "所有镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 创建网络和卷
    docker-compose up -d mysql redis
    
    # 等待数据库启动
    log_info "等待数据库启动..."
    sleep 30
    
    # 启动后端服务
    log_info "启动后端服务..."
    docker-compose up -d backend
    
    # 等待后端服务启动
    sleep 15
    
    # 启动前端和Nginx
    log_info "启动前端和代理服务..."
    docker-compose up -d frontend nginx
    
    log_success "所有服务启动完成"
}

# 停止服务
stop_services() {
    log_info "停止服务..."
    docker-compose down
    log_success "所有服务已停止"
}

# 重启服务
restart_services() {
    log_info "重启服务..."
    stop_services
    sleep 5
    start_services
}

# 查看日志
show_logs() {
    local service=$1
    if [ -z "$service" ]; then
        docker-compose logs -f
    else
        docker-compose logs -f $service
    fi
}

# 查看服务状态
show_status() {
    log_info "服务状态:"
    docker-compose ps
    
    log_info "系统资源使用:"
    docker stats --no-stream
    
    # 健康检查
    log_info "健康检查:"
    
    # 检查后端API
    if curl -f http://localhost:5000/health &> /dev/null; then
        log_success "后端API服务正常"
    else
        log_error "后端API服务异常"
    fi
    
    # 检查前端
    if curl -f http://localhost:80 &> /dev/null; then
        log_success "前端服务正常"
    else
        log_error "前端服务异常"
    fi
    
    # 检查Nginx代理
    if curl -f http://localhost:8080 &> /dev/null; then
        log_success "Nginx代理服务正常"
    else
        log_error "Nginx代理服务异常"
    fi
}

# 数据库初始化
init_database() {
    log_info "初始化数据库..."
    
    # 等待MySQL启动
    sleep 30
    
    # 执行数据库初始化脚本
    docker-compose exec mysql mysql -u root -proot123456 food_recommendation < ../database/init_db.sql
    
    log_success "数据库初始化完成"
}

# 备份数据
backup_data() {
    local backup_dir="backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p $backup_dir
    
    log_info "备份数据到: $backup_dir"
    
    # 备份数据库
    docker-compose exec mysql mysqldump -u root -proot123456 food_recommendation > $backup_dir/database.sql
    
    # 备份上传的文件
    docker cp $(docker-compose ps -q backend):/app/data $backup_dir/
    
    log_success "数据备份完成"
}

# 恢复数据
restore_data() {
    local backup_dir=$1
    if [ -z "$backup_dir" ]; then
        log_error "请指定备份目录"
        exit 1
    fi
    
    log_info "从备份恢复数据: $backup_dir"
    
    # 恢复数据库
    if [ -f "$backup_dir/database.sql" ]; then
        docker-compose exec mysql mysql -u root -proot123456 food_recommendation < $backup_dir/database.sql
        log_success "数据库恢复完成"
    fi
    
    # 恢复文件
    if [ -d "$backup_dir/data" ]; then
        docker cp $backup_dir/data $(docker-compose ps -q backend):/app/
        log_success "文件恢复完成"
    fi
}

# 清理系统
cleanup() {
    log_info "清理系统..."
    
    # 停止并删除容器
    docker-compose down -v
    
    # 删除未使用的镜像
    docker image prune -f
    
    # 删除未使用的卷
    docker volume prune -f
    
    log_success "系统清理完成"
}

# 显示帮助信息
show_help() {
    echo "美食推荐系统部署脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [环境] [操作] [参数]"
    echo ""
    echo "环境:"
    echo "  dev     开发环境"
    echo "  prod    生产环境"
    echo ""
    echo "操作:"
    echo "  build           构建Docker镜像"
    echo "  start           启动所有服务"
    echo "  stop            停止所有服务"
    echo "  restart         重启所有服务"
    echo "  logs [服务名]   查看日志"
    echo "  status          查看服务状态"
    echo "  init-db         初始化数据库"
    echo "  backup          备份数据"
    echo "  restore [目录]  恢复数据"
    echo "  cleanup         清理系统"
    echo "  help            显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 dev build     # 开发环境构建镜像"
    echo "  $0 prod start    # 生产环境启动服务"
    echo "  $0 dev logs backend  # 查看后端服务日志"
}

# 主函数
main() {
    local env=${1:-dev}
    local action=${2:-help}
    local param=$3
    
    # 检查参数
    if [[ ! "$env" =~ ^(dev|prod)$ ]]; then
        log_error "无效的环境参数: $env"
        show_help
        exit 1
    fi
    
    # 切换到部署目录
    cd "$(dirname "$0")"
    
    # 检查依赖
    check_dependencies
    
    # 创建环境配置
    if [ ! -f "$ENV_FILE" ]; then
        create_env_file $env
    fi
    
    # 执行操作
    case $action in
        build)
            build_images
            ;;
        start)
            start_services
            ;;
        stop)
            stop_services
            ;;
        restart)
            restart_services
            ;;
        logs)
            show_logs $param
            ;;
        status)
            show_status
            ;;
        init-db)
            init_database
            ;;
        backup)
            backup_data
            ;;
        restore)
            restore_data $param
            ;;
        cleanup)
            cleanup
            ;;
        help|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"