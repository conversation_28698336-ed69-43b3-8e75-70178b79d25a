#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
区域美食数据库更新器 - 专门用于导入广西、广东、海南三省数据
"""

import json
import pymysql
from datetime import datetime

class RegionalDataUpdater:
    def __init__(self):
        self.db_config = {
            'host': 'localhost',
            'user': 'root',
            'password': '123456789',
            'database': 'food_recommendation',
            'charset': 'utf8mb4',
            'cursorclass': pymysql.cursors.DictCursor
        }
    
    def get_connection(self):
        """获取数据库连接"""
        return pymysql.connect(**self.db_config)
    
    def update_database_schema(self):
        """更新数据库结构以支持省市信息"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # 检查并添加省市字段
            cursor.execute("SHOW COLUMNS FROM dishes LIKE 'province'")
            if not cursor.fetchone():
                cursor.execute("ALTER TABLE dishes ADD COLUMN province VARCHAR(20) AFTER region")
                print("已为dishes表添加province字段")
            
            cursor.execute("SHOW COLUMNS FROM dishes LIKE 'city'")
            if not cursor.fetchone():
                cursor.execute("ALTER TABLE dishes ADD COLUMN city VARCHAR(50) AFTER province")
                print("已为dishes表添加city字段")
            
            cursor.execute("SHOW COLUMNS FROM restaurants LIKE 'province'")
            if not cursor.fetchone():
                cursor.execute("ALTER TABLE restaurants ADD COLUMN province VARCHAR(20) AFTER name")
                print("已为restaurants表添加province字段")
            
            cursor.execute("SHOW COLUMNS FROM restaurants LIKE 'city'")
            if not cursor.fetchone():
                cursor.execute("ALTER TABLE restaurants ADD COLUMN city VARCHAR(50) AFTER province")
                print("已为restaurants表添加city字段")
            
            # 添加索引
            try:
                cursor.execute("CREATE INDEX idx_dishes_province_city ON dishes (province, city)")
                cursor.execute("CREATE INDEX idx_restaurants_province_city ON restaurants (province, city)")
                print("已添加省市索引")
            except pymysql.err.OperationalError:
                print("索引已存在，跳过创建")
            
            conn.commit()
            cursor.close()
            conn.close()
            
            return True
            
        except Exception as e:
            print(f"更新数据库结构失败: {e}")
            return False
    
    def clear_existing_data(self):
        """清除现有数据"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # 清空现有数据
            cursor.execute("DELETE FROM favorites")
            cursor.execute("DELETE FROM dishes")
            cursor.execute("DELETE FROM restaurants")
            
            # 重置自增ID
            cursor.execute("ALTER TABLE dishes AUTO_INCREMENT = 1")
            cursor.execute("ALTER TABLE restaurants AUTO_INCREMENT = 1")
            cursor.execute("ALTER TABLE favorites AUTO_INCREMENT = 1")
            
            conn.commit()
            cursor.close()
            conn.close()
            
            print("已清除现有数据")
            return True
            
        except Exception as e:
            print(f"清除数据失败: {e}")
            return False
    
    def import_regional_data(self, json_file="southern_region_food_data.json"):
        """导入区域数据"""
        try:
            # 加载数据
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print(f"开始导入数据: {data['total_dishes']}道菜品, {data['total_restaurants']}家餐厅")
            
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # 导入餐厅数据
            restaurant_sql = '''
                INSERT INTO restaurants (
                    name, province, city, cuisine_type, rating, review_count,
                    address, phone, business_hours, price_range, features,
                    image_url, monthly_orders, is_open, created_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            '''
            
            restaurant_data = []
            for restaurant in data['restaurants']:
                restaurant_data.append((
                    restaurant['name'],
                    restaurant['province'],
                    restaurant['city'],
                    restaurant['cuisine_type'],
                    restaurant['rating'],
                    restaurant['review_count'],
                    restaurant['address'],
                    restaurant['phone'],
                    restaurant['business_hours'],
                    restaurant['price_range'],
                    json.dumps(restaurant['features'], ensure_ascii=False),
                    restaurant['image_url'],
                    restaurant['monthly_orders'],
                    restaurant['is_open'],
                    restaurant['created_at']
                ))
            
            cursor.executemany(restaurant_sql, restaurant_data)
            print(f"已导入 {len(restaurant_data)} 家餐厅")
            
            # 创建餐厅ID映射
            cursor.execute("SELECT restaurant_id, name, province, city FROM restaurants")
            restaurant_mapping = {}
            for row in cursor.fetchall():
                key = f"{row['name']}-{row['province']}-{row['city']}"
                restaurant_mapping[key] = row['restaurant_id']
            
            # 导入菜品数据
            dish_sql = '''
                INSERT INTO dishes (
                    name, province, city, cuisine_type, price, rating, review_count,
                    description, image_url, restaurant_name, region, tags,
                    ingredients, nutrition, spicy_level, monthly_sales,
                    is_available, created_at, data_source
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            '''
            
            dish_data = []
            for dish in data['dishes']:
                dish_data.append((
                    dish['name'],
                    dish['province'],
                    dish['city'],
                    dish['cuisine_type'],
                    dish['price'],
                    dish['rating'],
                    dish['review_count'],
                    dish['description'],
                    dish['image_url'],
                    dish['restaurant_name'],
                    dish['region'],
                    json.dumps(dish['tags'], ensure_ascii=False),
                    json.dumps(dish['ingredients'], ensure_ascii=False),
                    json.dumps(dish['nutrition'], ensure_ascii=False),
                    dish['spicy_level'],
                    dish['monthly_sales'],
                    dish['is_available'],
                    dish['created_at'],
                    dish['data_source']
                ))
            
            cursor.executemany(dish_sql, dish_data)
            print(f"已导入 {len(dish_data)} 道菜品")
            
            conn.commit()
            cursor.close()
            conn.close()
            
            return True
            
        except Exception as e:
            print(f"导入数据失败: {e}")
            return False
    
    def verify_data(self):
        """验证导入的数据"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # 统计数据
            cursor.execute("SELECT COUNT(*) as count FROM dishes")
            dish_count = cursor.fetchone()['count']
            
            cursor.execute("SELECT COUNT(*) as count FROM restaurants")
            restaurant_count = cursor.fetchone()['count']
            
            print(f"\n=== 数据验证 ===")
            print(f"菜品总数: {dish_count}")
            print(f"餐厅总数: {restaurant_count}")
            
            # 按省份统计
            cursor.execute('''
                SELECT province, COUNT(*) as dish_count 
                FROM dishes 
                GROUP BY province 
                ORDER BY dish_count DESC
            ''')
            
            print(f"\n=== 各省菜品统计 ===")
            for row in cursor.fetchall():
                print(f"{row['province']}: {row['dish_count']}道菜品")
            
            # 按城市统计前10
            cursor.execute('''
                SELECT province, city, COUNT(*) as dish_count 
                FROM dishes 
                GROUP BY province, city 
                ORDER BY dish_count DESC 
                LIMIT 10
            ''')
            
            print(f"\n=== 热门城市菜品TOP10 ===")
            for row in cursor.fetchall():
                print(f"{row['province']}-{row['city']}: {row['dish_count']}道菜品")
            
            # 样本菜品
            cursor.execute('''
                SELECT name, province, city, price, rating, restaurant_name
                FROM dishes 
                ORDER BY rating DESC 
                LIMIT 5
            ''')
            
            print(f"\n=== 高评分菜品样本 ===")
            for row in cursor.fetchall():
                print(f"- {row['name']} ({row['province']}-{row['city']}) 价格{row['price']}元 评分{row['rating']} - {row['restaurant_name']}")
            
            cursor.close()
            conn.close()
            
            return True
            
        except Exception as e:
            print(f"数据验证失败: {e}")
            return False
    
    def update_complete_process(self):
        """完整更新流程"""
        print("=" * 60)
        print("广西、广东、海南三省美食数据更新")
        print("=" * 60)
        
        # 1. 更新数据库结构
        print("步骤1: 更新数据库结构...")
        if not self.update_database_schema():
            return False
        
        # 2. 清除现有数据
        print("\n步骤2: 清除现有数据...")
        if not self.clear_existing_data():
            return False
        
        # 3. 导入新数据
        print("\n步骤3: 导入区域数据...")
        if not self.import_regional_data():
            return False
        
        # 4. 验证数据
        print("\n步骤4: 验证导入数据...")
        if not self.verify_data():
            return False
        
        print("\n" + "=" * 60)
        print("数据更新完成！")
        print("=" * 60)
        
        return True

def main():
    """主函数"""
    updater = RegionalDataUpdater()
    success = updater.update_complete_process()
    
    if success:
        print("\n✓ 区域美食数据库更新成功!")
        print("现在可以启动后端服务: python unified_app.py")
    else:
        print("\n✗ 数据库更新失败!")

if __name__ == "__main__":
    main()