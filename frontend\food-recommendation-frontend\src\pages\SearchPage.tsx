import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { getDishImageUrl, handleImageError } from '@/utils/imageUtils';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Checkbox } from '@/components/ui/checkbox';
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { Search, Filter, Star, MapPin, Clock, Heart, SlidersHorizontal, X, User, Sparkles } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { dishApi, userApi } from '@/services/api';
import LoginModal from '@/components/LoginModal';

interface Dish {
  dish_id: number;
  name: string;
  cuisine_type: string;
  price: number;
  rating: number;
  review_count: number;
  description: string;
  image_url: string;
  restaurant_name: string;
  region: string;
  tags: string[];
}

interface SearchFilters {
  cuisine_types: string[];
  regions: string[];
  price_range: [number, number];
  rating_min: number;
  sort_by: string;
}

const SearchPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const { user, isAuthenticated, logout } = useAuth();
  const [searchQuery, setSearchQuery] = useState(searchParams.get('q') || '');
  const [searchResults, setSearchResults] = useState<Dish[]>([]);
  const [loading, setLoading] = useState(false);
  const [totalResults, setTotalResults] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [filters, setFilters] = useState<SearchFilters>({
    cuisine_types: searchParams.get('cuisine')?.split(',').filter(Boolean) || [],
    regions: searchParams.get('region')?.split(',').filter(Boolean) || [],
    price_range: [
      parseInt(searchParams.get('min_price') || '0'),
      parseInt(searchParams.get('max_price') || '200')
    ],
    rating_min: parseFloat(searchParams.get('min_rating') || '0'),
    sort_by: searchParams.get('sort') || 'relevance'
  });
  const [showFilters, setShowFilters] = useState(false);
  const [showLoginModal, setShowLoginModal] = useState(false);

  // 更新URL参数
  const updateURLParams = () => {
    const params = new URLSearchParams();
    if (searchQuery.trim()) params.set('q', searchQuery.trim());
    if (filters.cuisine_types.length > 0) params.set('cuisine', filters.cuisine_types.join(','));
    if (filters.regions.length > 0) params.set('region', filters.regions.join(','));
    if (filters.price_range[0] > 0) params.set('min_price', filters.price_range[0].toString());
    if (filters.price_range[1] < 200) params.set('max_price', filters.price_range[1].toString());
    if (filters.rating_min > 0) params.set('min_rating', filters.rating_min.toString());
    if (filters.sort_by !== 'relevance') params.set('sort', filters.sort_by);
    
    setSearchParams(params);
  };

  // 菜品去重函数 - 按名称去重，保留评分最高的记录
  const deduplicateDishes = (dishes: Dish[]) => {
    const dishMap = new Map();
    
    dishes.forEach(dish => {
      const name = dish.name;
      if (!dishMap.has(name) || dish.rating > dishMap.get(name).rating) {
        dishMap.set(name, dish);
      }
    });
    
    return Array.from(dishMap.values());
  };

  // 模拟搜索结果数据
  const mockSearchResults: Dish[] = [
    // 鸡肉类
    {
      dish_id: 1,
      name: '白切鸡',
      cuisine_type: '粤菜',
      price: 35.0,
      rating: 4.5,
      review_count: 128,
      description: '传统粤式白切鸡，肉质鲜嫩，配以特制蘸料，口感清淡鲜美',
      image_url: 'https://images.unsplash.com/photo-1604503468506-a8da13d82791?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      restaurant_name: '粤味轩',
      region: '广东',
      tags: ['经典', '清淡', '营养']
    },
    {
      dish_id: 2,
      name: '海南鸡饭',
      cuisine_type: '琼菜',
      price: 30.0,
      rating: 4.3,
      review_count: 156,
      description: '海南风味鸡饭，香米配白切鸡，口感丰富，营养均衡',
      image_url: 'https://images.unsplash.com/photo-1512058564366-18510be2db19?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      restaurant_name: '海南风情',
      region: '海南',
      tags: ['经典', '营养', '香米']
    },
    {
      dish_id: 3,
      name: '口水鸡',
      cuisine_type: '川菜',
      price: 32.0,
      rating: 4.4,
      review_count: 189,
      description: '四川经典凉菜，鸡肉嫩滑，麻辣鲜香，开胃下饭',
      image_url: 'https://images.unsplash.com/photo-1598515214211-89d3c73ae83b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      restaurant_name: '川香园',
      region: '四川',
      tags: ['麻辣', '凉菜', '开胃']
    },
    {
      dish_id: 4,
      name: '宫保鸡丁',
      cuisine_type: '川菜',
      price: 28.0,
      rating: 4.2,
      review_count: 234,
      description: '川菜经典，鸡肉嫩滑，花生香脆，酸甜微辣',
      image_url: 'https://images.unsplash.com/photo-1563379091339-03246963d51a?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      restaurant_name: '蜀味轩',
      region: '四川',
      tags: ['经典', '酸甜', '下饭']
    },
    // 面条类
    {
      dish_id: 5,
      name: '桂林米粉',
      cuisine_type: '桂菜',
      price: 25.0,
      rating: 4.2,
      review_count: 89,
      description: '桂林特色米粉，汤鲜味美，配菜丰富，是当地人的最爱',
      image_url: 'https://images.unsplash.com/photo-1569718212165-3a8278d5f624?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      restaurant_name: '桂林人家',
      region: '广西',
      tags: ['特色', '汤粉', '实惠']
    },
    {
      dish_id: 6,
      name: '兰州拉面',
      cuisine_type: '兰菜',
      price: 22.0,
      rating: 4.1,
      review_count: 167,
      description: '西北特色面条，汤清味鲜，面条劲道，牛肉香嫩',
      image_url: 'https://images.unsplash.com/photo-1582204742900-72bb5e4cace3?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      restaurant_name: '兰州正宗面馆',
      region: '甘肃',
      tags: ['清汤', '劲道', '牛肉']
    },
    {
      dish_id: 7,
      name: '担担面',
      cuisine_type: '川菜',
      price: 18.0,
      rating: 4.3,
      review_count: 145,
      description: '四川特色面条，芝麻香浓，辣而不燥，口感丰富',
      image_url: 'https://images.unsplash.com/photo-1612929633738-8fe44f7ec841?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      restaurant_name: '川香面馆',
      region: '四川',
      tags: ['芝麻', '麻辣', '特色']
    },
    // 点心类
    {
      dish_id: 8,
      name: '叉烧包',
      cuisine_type: '粤菜',
      price: 18.0,
      rating: 4.1,
      review_count: 203,
      description: '广式茶点经典，叉烧馅料丰富，包子皮松软香甜',
      image_url: 'https://images.unsplash.com/photo-1496116218417-1a781b1c416c?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      restaurant_name: '港式茶餐厅',
      region: '广东',
      tags: ['茶点', '经典', '甜味']
    },
    {
      dish_id: 9,
      name: '小笼包',
      cuisine_type: '苏菜',
      price: 26.0,
      rating: 4.6,
      review_count: 312,
      description: '江南名点，皮薄汁多，鲜美可口，轻咬一口满嘴香',
      image_url: 'https://images.unsplash.com/photo-1601314002957-3f1df6b4da90?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      restaurant_name: '江南小笼',
      region: '江苏',
      tags: ['皮薄', '汁多', '精致']
    },
    // 火锅类
    {
      dish_id: 10,
      name: '麻辣火锅',
      cuisine_type: '川菜',
      price: 88.0,
      rating: 4.7,
      review_count: 456,
      description: '正宗四川火锅，麻辣鲜香，涮菜丰富，适合聚餐',
      image_url: 'https://images.unsplash.com/photo-1582878826629-29b7ad1cdc43?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      restaurant_name: '重庆老火锅',
      region: '四川',
      tags: ['麻辣', '聚餐', '正宗']
    },
    // 鱼类
    {
      dish_id: 11,
      name: '水煮鱼',
      cuisine_type: '川菜',
      price: 58.0,
      rating: 4.4,
      review_count: 278,
      description: '川菜经典，鱼片鲜嫩，汤汁麻辣，配菜丰富',
      image_url: 'https://images.unsplash.com/photo-1544963150-1c7ebe1fe982?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      restaurant_name: '川味鱼庄',
      region: '四川',
      tags: ['鲜嫩', '麻辣', '经典']
    },
    {
      dish_id: 12,
      name: '清蒸鲈鱼',
      cuisine_type: '粤菜',
      price: 68.0,
      rating: 4.5,
      review_count: 156,
      description: '粤菜经典，鱼肉鲜嫩，清香淡雅，营养丰富',
      image_url: 'https://images.unsplash.com/photo-1571026073021-9e3e30d63641?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      restaurant_name: '海鲜酒家',
      region: '广东',
      tags: ['清蒸', '鲜嫩', '营养']
    }
  ];

  const [cuisineOptions, setCuisineOptions] = useState(['粤菜', '川菜', '湘菜', '苏菜', '琼菜', '桂菜', '兰菜']);
  const [regionOptions, setRegionOptions] = useState(['广东', '广西', '海南', '四川', '湖南', '江苏', '甘肃']);
  const sortOptions = [
    { value: 'relevance', label: '相关度' },
    { value: 'rating', label: '评分' },
    { value: 'price_low', label: '价格从低到高' },
    { value: 'price_high', label: '价格从高到低' },
    { value: 'popularity', label: '热门度' }
  ];

  // 获取菜系分类选项
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const categories = await dishApi.getCategories();
        if (Array.isArray(categories) && categories.length > 0) {
          setCuisineOptions(categories);
        }
      } catch (error) {
        console.error('获取菜系分类失败:', error);
      }
    };
    fetchCategories();
  }, []);

  // 页面加载时执行搜索（如果有URL参数）
  useEffect(() => {
    if (searchQuery.trim()) {
      handleSearch();
    }
  }, []);

  // 添加防抖功能
  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      if (searchQuery.trim()) {
        handleSearch();
      }
    }, 500); // 500ms 防抖

    return () => clearTimeout(delayedSearch);
  }, [searchQuery]);

  useEffect(() => {
    if (searchQuery.trim()) {
      handleSearch();
    }
  }, [filters]);

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      setSearchResults([]);
      setTotalResults(0);
      return;
    }

    setLoading(true);
    
    try {
      // 构建搜索参数
      const searchParams = {
        query: searchQuery.trim(),
        page: currentPage,
        per_page: 20,
        cuisine_type: filters.cuisine_types.length > 0 ? filters.cuisine_types[0] : undefined,
        price_min: filters.price_range[0],
        price_max: filters.price_range[1],
        rating_min: filters.rating_min,
        sort_by: filters.sort_by
      };

      // 调用真实的搜索API
      const response = await dishApi.searchDishes(searchParams);
      
      if (response && response.items) {
        // 处理搜索结果数据格式
        const processedResults = response.items.map((dish: any) => ({
          dish_id: dish.dish_id || dish.id,
          name: dish.name,
          cuisine_type: dish.cuisine_type || dish.cuisine,
          price: dish.price,
          rating: dish.rating,
          review_count: dish.review_count || 0,
          description: dish.description,
          image_url: dish.image_url || dish.image,
          restaurant_name: dish.restaurant_name,
          region: dish.region,
          tags: dish.tags || []
        }));

        // 🔥 应用去重逻辑 - 确保搜索结果不重复
        const uniqueResults = deduplicateDishes(processedResults);
        console.log(`搜索结果去重: ${processedResults.length} -> ${uniqueResults.length}`);

        setSearchResults(uniqueResults);
        setTotalResults(uniqueResults.length);
      } else {
        // 如果API失败，使用模拟数据作为后备
        let results = [...mockSearchResults];
        
        // 应用搜索关键词过滤
        if (searchQuery.trim()) {
          const searchTerm = searchQuery.toLowerCase();
          results = results.filter(dish => 
            dish.name.toLowerCase().includes(searchTerm) ||
            dish.description.toLowerCase().includes(searchTerm) ||
            dish.cuisine_type.toLowerCase().includes(searchTerm) ||
            dish.restaurant_name.toLowerCase().includes(searchTerm) ||
            dish.tags.some(tag => tag.toLowerCase().includes(searchTerm))
          );
        }
        
        // 应用筛选条件
        if (filters.cuisine_types.length > 0) {
          results = results.filter(dish => filters.cuisine_types.includes(dish.cuisine_type));
        }
        
        if (filters.regions.length > 0) {
          results = results.filter(dish => filters.regions.includes(dish.region));
        }
        
        results = results.filter(dish => 
          dish.price >= filters.price_range[0] && 
          dish.price <= filters.price_range[1] &&
          dish.rating >= filters.rating_min
        );
        
        // 应用排序
        switch (filters.sort_by) {
          case 'rating':
            results.sort((a, b) => b.rating - a.rating);
            break;
          case 'price_low':
            results.sort((a, b) => a.price - b.price);
            break;
          case 'price_high':
            results.sort((a, b) => b.price - a.price);
            break;
          case 'popularity':
            results.sort((a, b) => b.review_count - a.review_count);
            break;
        }
        
        // 🔥 应用去重逻辑到模拟数据
        const uniqueResults = deduplicateDishes(results);
        
        setSearchResults(uniqueResults);
        setTotalResults(uniqueResults.length);
      }
    } catch (error) {
      console.error('搜索失败:', error);
      // 发生错误时也使用智能筛选的模拟数据
      let results = [...mockSearchResults];
      
      if (searchQuery.trim()) {
        const searchTerm = searchQuery.toLowerCase();
        results = results.filter(dish => 
          dish.name.toLowerCase().includes(searchTerm) ||
          dish.description.toLowerCase().includes(searchTerm) ||
          dish.cuisine_type.toLowerCase().includes(searchTerm) ||
          dish.restaurant_name.toLowerCase().includes(searchTerm) ||
          dish.tags.some(tag => tag.toLowerCase().includes(searchTerm))
        );
      }
      
      // 🔥 应用去重逻辑到错误恢复数据
      const uniqueResults = deduplicateDishes(results);
      
      setSearchResults(uniqueResults);
      setTotalResults(uniqueResults.length);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (key: keyof SearchFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
    updateURLParams();
  };

  const clearFilters = () => {
    setFilters({
      cuisine_types: [],
      regions: [],
      price_range: [0, 200],
      rating_min: 0,
      sort_by: 'relevance'
    });
  };

  const DishCard = ({ dish }: { dish: Dish }) => {
    const [isFavorited, setIsFavorited] = useState(false);
    const [favoriteLoading, setFavoriteLoading] = useState(false);

    // 检查收藏状态
    useEffect(() => {
      const checkFavoriteStatus = async () => {
        if (isAuthenticated && user) {
          try {
            const favoriteStatus = await userApi.checkFavorite(user.user_id, dish.dish_id);
            setIsFavorited(favoriteStatus);
          } catch (error) {
            console.error('检查收藏状态失败:', error);
          }
        }
      };
      checkFavoriteStatus();
    }, [isAuthenticated, user, dish.dish_id]);

    const handleFavorite = async (e: React.MouseEvent) => {
      e.stopPropagation();
      
      if (!isAuthenticated || !user) {
        setShowLoginModal(true);
        return;
      }

      setFavoriteLoading(true);
      try {
        if (isFavorited) {
          await userApi.removeFavorite(user.user_id, dish.dish_id);
          setIsFavorited(false);
        } else {
          await userApi.addFavorite(user.user_id, dish.dish_id);
          setIsFavorited(true);
        }
      } catch (error) {
        console.error('收藏操作失败:', error);
      } finally {
        setFavoriteLoading(false);
      }
    };

    return (
      <Card 
        className="group hover:shadow-2xl transition-all duration-500 cursor-pointer bg-white/90 backdrop-blur-sm hover:bg-white hover:scale-[1.02] overflow-hidden"
        onClick={() => navigate(`/dish/${dish.dish_id}`)}
      >
        <div className="flex min-h-[200px]">
          <div className="relative w-56 flex-shrink-0 overflow-hidden">
            <img
              src={getDishImageUrl(dish.name, dish.image_url)}
              alt={dish.name}
              className="w-full h-full object-cover rounded-l-lg group-hover:scale-105 transition-transform duration-500"
              onError={handleImageError}
              style={{ objectPosition: 'center', minHeight: '200px' }}
            />
            <div className="absolute inset-0 bg-gradient-to-r from-transparent to-black/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <div className="absolute top-2 right-2">
              <Button 
                size="sm" 
                variant="ghost" 
                className="bg-white/90 hover:bg-white p-2 rounded-full shadow-lg backdrop-blur-sm hover:scale-110 transition-all duration-200"
                onClick={handleFavorite}
                disabled={favoriteLoading}
              >
                <Heart className={`h-4 w-4 ${isFavorited ? 'fill-red-500 text-red-500' : 'text-red-500 hover:fill-red-500'} transition-all duration-200 ${favoriteLoading ? 'animate-pulse' : ''}`} />
              </Button>
            </div>
          </div>
        
        <CardContent className="flex-1 p-6 flex flex-col justify-between">
          <div className="space-y-3">
            <div className="flex justify-between items-start">
              <h3 className="font-bold text-xl text-gray-900 group-hover:text-orange-600 transition-colors duration-200 line-clamp-1">{dish.name}</h3>
              <span className="text-2xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent ml-3">¥{dish.price}</span>
            </div>
            
            <div className="flex items-center gap-3">
              <Badge variant="secondary" className="text-sm bg-orange-100 text-orange-700 hover:bg-orange-200 px-3 py-1">
                {dish.cuisine_type}
              </Badge>
              <div className="flex items-center gap-1">
                <MapPin className="h-4 w-4 text-gray-500" />
                <span className="text-sm text-gray-600 font-medium">{dish.region}</span>
              </div>
            </div>

            <p className="text-sm text-gray-700 line-clamp-2 leading-relaxed">{dish.description}</p>
          </div>

          <div className="space-y-3 mt-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Star className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                <span className="text-lg font-semibold text-gray-900">{dish.rating}</span>
                <span className="text-sm text-gray-500">({dish.review_count}条评价)</span>
              </div>
              <div className="flex items-center gap-1 text-sm text-gray-600">
                <Clock className="h-4 w-4" />
                <span className="font-medium truncate max-w-[120px]">{dish.restaurant_name}</span>
              </div>
            </div>

            <div className="flex gap-2 flex-wrap">
              {dish.tags.slice(0, 3).map((tag, index) => (
                <Badge key={index} variant="outline" className="text-xs px-2 py-1 bg-gray-50 text-gray-600 border-gray-200 hover:bg-gray-100">
                  <Sparkles className="h-3 w-3 mr-1" />
                  {tag}
                </Badge>
              ))}
              {dish.tags.length > 3 && (
                <Badge variant="outline" className="text-xs px-2 py-1 bg-gray-50 text-gray-600 border-gray-200">
                  +{dish.tags.length - 3}
                </Badge>
              )}
            </div>
          </div>
        </CardContent>
      </div>
    </Card>
    );
  };

  const FilterPanel = () => (
    <div className="space-y-8">
      {/* 菜系筛选 */}
      <div>
        <h3 className="font-semibold text-lg mb-4 text-gray-900">菜系类型</h3>
        <div className="space-y-3">
          {cuisineOptions.map((cuisine) => (
            <div key={cuisine} className="flex items-center space-x-3">
              <Checkbox
                id={cuisine}
                checked={filters.cuisine_types.includes(cuisine)}
                onCheckedChange={(checked) => {
                  if (checked) {
                    handleFilterChange('cuisine_types', [...filters.cuisine_types, cuisine]);
                  } else {
                    handleFilterChange('cuisine_types', filters.cuisine_types.filter(c => c !== cuisine));
                  }
                }}
                className="data-[state=checked]:bg-orange-500 data-[state=checked]:border-orange-500"
              />
              <label htmlFor={cuisine} className="text-sm font-medium text-gray-700 cursor-pointer">{cuisine}</label>
            </div>
          ))}
        </div>
      </div>

      {/* 地区筛选 */}
      <div>
        <h3 className="font-semibold text-lg mb-4 text-gray-900">地区</h3>
        <div className="space-y-3">
          {regionOptions.map((region) => (
            <div key={region} className="flex items-center space-x-3">
              <Checkbox
                id={region}
                checked={filters.regions.includes(region)}
                onCheckedChange={(checked) => {
                  if (checked) {
                    handleFilterChange('regions', [...filters.regions, region]);
                  } else {
                    handleFilterChange('regions', filters.regions.filter(r => r !== region));
                  }
                }}
                className="data-[state=checked]:bg-orange-500 data-[state=checked]:border-orange-500"
              />
              <label htmlFor={region} className="text-sm font-medium text-gray-700 cursor-pointer">{region}</label>
            </div>
          ))}
        </div>
      </div>

      {/* 价格范围 */}
      <div>
        <h3 className="font-semibold text-lg mb-4 text-gray-900">价格范围</h3>
        <div className="px-3">
          <Slider
            value={filters.price_range}
            onValueChange={(value) => handleFilterChange('price_range', value)}
            max={200}
            min={0}
            step={5}
            className="mb-4"
          />
          <div className="flex justify-between text-sm font-medium text-gray-600">
            <span>¥{filters.price_range[0]}</span>
            <span>¥{filters.price_range[1]}</span>
          </div>
        </div>
      </div>

      {/* 最低评分 */}
      <div>
        <h3 className="font-semibold text-lg mb-4 text-gray-900">最低评分</h3>
        <div className="px-3">
          <Slider
            value={[filters.rating_min]}
            onValueChange={(value) => handleFilterChange('rating_min', value[0])}
            max={5}
            min={0}
            step={0.1}
            className="mb-4"
          />
          <div className="text-sm font-medium text-gray-600">
            {filters.rating_min.toFixed(1)}分及以上
          </div>
        </div>
      </div>

      <Button 
        onClick={clearFilters} 
        variant="outline" 
        className="w-full border-orange-200 text-orange-600 hover:bg-orange-50 hover:border-orange-300 font-medium"
      >
        清除筛选
      </Button>
    </div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-red-50 to-yellow-50">
      {/* 头部导航 */}
      <header className="bg-white/95 backdrop-blur-md shadow-lg border-b border-orange-100 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-18 py-2">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center shadow-lg transform hover:scale-105 transition-transform duration-200">
                <span className="text-white font-bold text-xl">美</span>
              </div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
                美食推荐
              </h1>
            </div>
            <nav className="hidden md:flex space-x-1">
              <button 
                onClick={() => navigate('/')} 
                className="px-6 py-3 rounded-full text-gray-600 hover:text-orange-600 hover:bg-orange-50 transition-all duration-200 font-medium"
              >
                首页
              </button>
              <button className="px-6 py-3 rounded-full bg-gradient-to-r from-orange-500 to-red-500 text-white font-medium shadow-lg transform hover:scale-105 transition-all duration-200 hover:shadow-xl">
                搜索
              </button>
              <button 
                onClick={() => navigate('/profile')} 
                className="px-6 py-3 rounded-full text-gray-600 hover:text-orange-600 hover:bg-orange-50 transition-all duration-200 font-medium"
              >
                我的
              </button>
            </nav>
            {isAuthenticated && user ? (
              <div className="flex items-center gap-3">
                <span className="text-gray-700 font-medium">欢迎，{user.username}</span>
                <Button 
                  variant="outline" 
                  onClick={() => {
                    logout();
                    navigate('/');
                  }}
                  className="rounded-full px-4 py-2"
                >
                  退出登录
                </Button>
              </div>
            ) : (
              <Button 
                className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 rounded-full px-6 py-3"
                onClick={() => setShowLoginModal(true)}
              >
                <User className="h-4 w-4 mr-2" />
                登录
              </Button>
            )}
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 搜索栏 */}
        <div className="mb-8">
          <div className="flex gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-6 w-6" />
              <Input
                placeholder="搜索菜品、餐厅或菜系..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-12 h-14 text-lg bg-white/90 backdrop-blur-sm border-2 border-orange-200 focus:border-orange-400 rounded-2xl shadow-lg"
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
            </div>
            <Button 
              onClick={handleSearch}
              className="h-14 px-8 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 rounded-2xl font-semibold"
              disabled={loading}
            >
              {loading ? '搜索中...' : '搜索'}
            </Button>
            
            {/* 移动端筛选按钮 */}
            <Sheet open={showFilters} onOpenChange={setShowFilters}>
              <SheetTrigger asChild>
                <Button variant="outline" className="h-14 px-4 md:hidden border-2 border-orange-200 hover:bg-orange-50 rounded-2xl">
                  <SlidersHorizontal className="h-6 w-6 text-orange-600" />
                </Button>
              </SheetTrigger>
              <SheetContent className="bg-white/95 backdrop-blur-md">
                <SheetHeader>
                  <SheetTitle className="text-xl font-bold text-gray-900">筛选条件</SheetTitle>
                  <SheetDescription className="text-gray-600">
                    设置筛选条件来精确查找您想要的美食
                  </SheetDescription>
                </SheetHeader>
                <div className="mt-8">
                  <FilterPanel />
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>

        <div className="flex gap-8">
          {/* 左侧筛选面板 - 桌面端 */}
          <div className="hidden md:block w-80 flex-shrink-0">
            <Card className="p-6 sticky top-24 bg-white/90 backdrop-blur-sm shadow-xl border-0">
              <div className="flex items-center justify-between mb-6">
                <h2 className="font-bold text-xl text-gray-900">筛选条件</h2>
                <Filter className="h-6 w-6 text-orange-500" />
              </div>
              <FilterPanel />
            </Card>
          </div>

          {/* 右侧搜索结果 */}
          <div className="flex-1">
            {/* 结果统计和排序 */}
            <div className="flex justify-between items-center mb-6">
              <div className="text-lg font-medium text-gray-700">
                {loading ? '搜索中...' : `找到 ${totalResults} 个结果`}
              </div>
              <div className="flex items-center gap-3">
                <span className="text-sm font-medium text-gray-600">排序:</span>
                <Select 
                  value={filters.sort_by} 
                  onValueChange={(value) => handleFilterChange('sort_by', value)}
                >
                  <SelectTrigger className="w-40 bg-white/90 backdrop-blur-sm border-orange-200 focus:border-orange-400">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {sortOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* 活跃筛选标签 */}
            {(filters.cuisine_types.length > 0 || filters.regions.length > 0 || filters.rating_min > 0) && (
              <div className="flex flex-wrap gap-3 mb-6">
                {filters.cuisine_types.map((cuisine) => (
                  <Badge key={cuisine} variant="secondary" className="flex items-center gap-2 px-3 py-2 bg-orange-100 text-orange-700 hover:bg-orange-200">
                    {cuisine}
                    <X 
                      className="h-4 w-4 cursor-pointer hover:text-orange-900" 
                      onClick={() => handleFilterChange('cuisine_types', filters.cuisine_types.filter(c => c !== cuisine))}
                    />
                  </Badge>
                ))}
                {filters.regions.map((region) => (
                  <Badge key={region} variant="secondary" className="flex items-center gap-2 px-3 py-2 bg-orange-100 text-orange-700 hover:bg-orange-200">
                    {region}
                    <X 
                      className="h-4 w-4 cursor-pointer hover:text-orange-900" 
                      onClick={() => handleFilterChange('regions', filters.regions.filter(r => r !== region))}
                    />
                  </Badge>
                ))}
                {filters.rating_min > 0 && (
                  <Badge variant="secondary" className="flex items-center gap-2 px-3 py-2 bg-orange-100 text-orange-700 hover:bg-orange-200">
                    {filters.rating_min.toFixed(1)}分+
                    <X 
                      className="h-4 w-4 cursor-pointer hover:text-orange-900" 
                      onClick={() => handleFilterChange('rating_min', 0)}
                    />
                  </Badge>
                )}
              </div>
            )}

            {/* 搜索结果列表 */}
            {loading ? (
              <div className="space-y-6">
                {[1, 2, 3].map((i) => (
                  <Card key={i} className="animate-pulse bg-white/60">
                    <div className="flex">
                      <div className="w-48 h-32 bg-gray-200 rounded-l-lg"></div>
                      <div className="flex-1 p-6 space-y-3">
                        <div className="h-6 bg-gray-200 rounded w-1/3"></div>
                        <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                        <div className="h-4 bg-gray-200 rounded w-full"></div>
                        <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            ) : searchResults.length > 0 ? (
              <div className="space-y-6">
                {searchResults.map((dish) => (
                  <DishCard key={dish.dish_id} dish={dish} />
                ))}
              </div>
            ) : searchQuery.trim() ? (
              <div className="text-center py-16">
                <div className="text-gray-500 mb-6">
                  <Search className="h-20 w-20 mx-auto mb-6 opacity-30" />
                  <p className="text-2xl font-semibold mb-2">没有找到相关结果</p>
                  <p className="text-lg">试试调整搜索关键词或筛选条件</p>
                </div>
              </div>
            ) : (
              <div className="text-center py-16">
                <div className="text-gray-500">
                  <Search className="h-20 w-20 mx-auto mb-6 opacity-30" />
                  <p className="text-2xl font-semibold mb-2">输入关键词开始搜索</p>
                  <p className="text-lg">发现您喜欢的美食</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 登录模态框 */}
      <LoginModal 
        open={showLoginModal} 
        onOpenChange={setShowLoginModal} 
      />
    </div>
  );
};

export default SearchPage;
