"""
数据库迁移脚本 - 创建基础表结构
"""

from flask_migrate import upgrade, downgrade
from sqlalchemy import text

def upgrade():
    """升级数据库"""
    # 创建用户表
    op.create_table('users',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('username', sa.String(length=80), nullable=False),
        sa.<PERSON>umn('email', sa.String(length=120), nullable=False),
        sa.<PERSON>umn('password_hash', sa.String(length=255), nullable=False),
        sa.Column('phone', sa.String(length=20), nullable=True),
        sa.Column('avatar', sa.String(length=255), nullable=True),
        sa.Column('preferred_cuisine', sa.String(length=100), nullable=True),
        sa.Column('price_range', sa.String(length=20), nullable=True),
        sa.Column('taste_preference', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('last_login', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('email'),
        sa.UniqueConstraint('username')
    )
    
    # 创建菜品表
    op.create_table('dishes',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('cuisine_type', sa.String(length=50), nullable=True),
        sa.Column('region', sa.String(length=50), nullable=True),
        sa.Column('price', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('price_range', sa.String(length=20), nullable=True),
        sa.Column('image_url', sa.String(length=255), nullable=True),
        sa.Column('images', sa.Text(), nullable=True),
        sa.Column('calories', sa.Integer(), nullable=True),
        sa.Column('ingredients', sa.Text(), nullable=True),
        sa.Column('cooking_method', sa.String(length=50), nullable=True),
        sa.Column('taste_tags', sa.Text(), nullable=True),
        sa.Column('spicy_level', sa.Integer(), nullable=True),
        sa.Column('rating', sa.Float(), nullable=True),
        sa.Column('review_count', sa.Integer(), nullable=True),
        sa.Column('order_count', sa.Integer(), nullable=True),
        sa.Column('source_platform', sa.String(length=50), nullable=True),
        sa.Column('source_url', sa.String(length=255), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    
    # 创建索引
    op.create_index('idx_dishes_name', 'dishes', ['name'])
    op.create_index('idx_dishes_cuisine_type', 'dishes', ['cuisine_type'])
    op.create_index('idx_dishes_region', 'dishes', ['region'])
    op.create_index('idx_dishes_rating', 'dishes', ['rating'])

def downgrade():
    """降级数据库"""
    op.drop_table('dishes')
    op.drop_table('users')