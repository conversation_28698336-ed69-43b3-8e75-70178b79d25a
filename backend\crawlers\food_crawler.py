#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
美食数据爬取系统
支持从多个平台爬取广西、广东、海南地区的美食数据
"""

import asyncio
import aiohttp
import requests
import json
import time
import random
import re
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict
from urllib.parse import urljoin, quote, unquote
import pymysql
import logging
from concurrent.futures import ThreadPoolExecutor
import hashlib
import os
from fake_useragent import UserAgent
import threading
from queue import Queue

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('food_crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class DishInfo:
    """菜品信息数据类"""
    name: str
    restaurant_name: str = ""
    region: str = ""
    city: str = ""
    cuisine_type: str = ""
    category: str = ""
    price: float = 0.0
    rating: float = 0.0
    review_count: int = 0
    description: str = ""
    ingredients: str = ""
    cooking_method: str = ""
    taste_tags: List[str] = None
    image_url: str = ""
    image_urls: List[str] = None
    recipe: str = ""
    source_platform: str = ""
    source_url: str = ""
    spice_level: int = 0
    cooking_time: int = 0
    calories: int = 0
    cultural_background: str = ""

    def __post_init__(self):
        if self.taste_tags is None:
            self.taste_tags = []
        if self.image_urls is None:
            self.image_urls = []

@dataclass
class RestaurantInfo:
    """餐厅信息数据类"""
    name: str
    region: str = ""
    city: str = ""
    address: str = ""
    phone: str = ""
    latitude: float = 0.0
    longitude: float = 0.0
    business_hours: str = ""
    avg_price: float = 0.0
    rating: float = 0.0
    review_count: int = 0
    restaurant_type: str = ""
    environment_score: float = 0.0
    service_score: float = 0.0
    taste_score: float = 0.0
    source_platform: str = ""
    source_url: str = ""
    image_urls: List[str] = None

    def __post_init__(self):
        if self.image_urls is None:
            self.image_urls = []

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.connection = None
        self.connect()
    
    def connect(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(
                host='localhost',
                user='root',
                password='123456',
                database='food_recommendation',
                charset='utf8mb4',
                autocommit=True
            )
            logger.info("数据库连接成功")
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    def execute_sql_file(self, sql_file_path: str):
        """执行SQL文件"""
        try:
            with open(sql_file_path, 'r', encoding='utf-8') as f:
                sql_content = f.read()
            
            # 分割多个SQL语句
            statements = sql_content.split(';')
            cursor = self.connection.cursor()
            
            for statement in statements:
                statement = statement.strip()
                if statement:
                    cursor.execute(statement)
            
            cursor.close()
            logger.info(f"成功执行SQL文件: {sql_file_path}")
        except Exception as e:
            logger.error(f"执行SQL文件失败: {e}")
            raise
    
    def get_region_id(self, province: str, city: str, district: str = None) -> Optional[int]:
        """获取地区ID"""
        try:
            cursor = self.connection.cursor()
            if district:
                cursor.execute(
                    "SELECT region_id FROM regions WHERE province=%s AND city=%s AND district=%s",
                    (province, city, district)
                )
            else:
                cursor.execute(
                    "SELECT region_id FROM regions WHERE province=%s AND city=%s",
                    (province, city)
                )
            result = cursor.fetchone()
            cursor.close()
            return result[0] if result else None
        except Exception as e:
            logger.error(f"获取地区ID失败: {e}")
            return None
    
    def save_dish(self, dish: DishInfo) -> bool:
        """保存菜品信息"""
        try:
            cursor = self.connection.cursor()
            
            # 获取地区ID
            region_id = self.get_region_id(dish.region, dish.city)
            
            # 检查是否已存在
            cursor.execute(
                "SELECT dish_id FROM dishes_extended WHERE name=%s AND source_platform=%s AND source_url=%s",
                (dish.name, dish.source_platform, dish.source_url)
            )
            if cursor.fetchone():
                cursor.close()
                return True  # 已存在，跳过
            
            # 插入菜品数据
            sql = """
            INSERT INTO dishes_extended (
                name, region_id, cuisine_type, category, price, rating, review_count,
                description, ingredients, cooking_method, taste_tags, image_url, image_urls,
                recipe, source_platform, source_url, spice_level, cooking_time,
                calories_per_serving, cultural_background, crawl_date
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            cursor.execute(sql, (
                dish.name, region_id, dish.cuisine_type, dish.category, dish.price,
                dish.rating, dish.review_count, dish.description, dish.ingredients,
                dish.cooking_method, json.dumps(dish.taste_tags, ensure_ascii=False),
                dish.image_url, json.dumps(dish.image_urls, ensure_ascii=False),
                dish.recipe, dish.source_platform, dish.source_url, dish.spice_level,
                dish.cooking_time, dish.calories, dish.cultural_background, datetime.now()
            ))
            
            cursor.close()
            return True
        except Exception as e:
            logger.error(f"保存菜品失败: {e}")
            return False
    
    def save_restaurant(self, restaurant: RestaurantInfo) -> bool:
        """保存餐厅信息"""
        try:
            cursor = self.connection.cursor()
            
            # 获取地区ID
            region_id = self.get_region_id(restaurant.region, restaurant.city)
            
            # 检查是否已存在
            cursor.execute(
                "SELECT restaurant_id FROM restaurants WHERE name=%s AND source_platform=%s",
                (restaurant.name, restaurant.source_platform)
            )
            if cursor.fetchone():
                cursor.close()
                return True  # 已存在，跳过
            
            # 插入餐厅数据
            sql = """
            INSERT INTO restaurants (
                name, region_id, address, phone, latitude, longitude, business_hours,
                avg_price, rating, review_count, restaurant_type, environment_score,
                service_score, taste_score, source_platform, source_url, image_urls
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            cursor.execute(sql, (
                restaurant.name, region_id, restaurant.address, restaurant.phone,
                restaurant.latitude, restaurant.longitude, restaurant.business_hours,
                restaurant.avg_price, restaurant.rating, restaurant.review_count,
                restaurant.restaurant_type, restaurant.environment_score,
                restaurant.service_score, restaurant.taste_score,
                restaurant.source_platform, restaurant.source_url,
                json.dumps(restaurant.image_urls, ensure_ascii=False)
            ))
            
            cursor.close()
            return True
        except Exception as e:
            logger.error(f"保存餐厅失败: {e}")
            return False

class BaseCrawler:
    """爬虫基类"""
    
    def __init__(self, platform_name: str):
        self.platform_name = platform_name
        self.ua = UserAgent()
        self.session = requests.Session()
        self.db = DatabaseManager()
        self.request_count = 0
        self.last_request_time = 0
        self.rate_limit = 2  # 每秒最多2个请求
        
        # 设置默认请求头
        self.default_headers = {
            'User-Agent': self.ua.random,
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        }
        self.session.headers.update(self.default_headers)
    
    def rate_limit_wait(self):
        """限制请求频率"""
        current_time = time.time()
        if current_time - self.last_request_time < 1.0 / self.rate_limit:
            time.sleep(1.0 / self.rate_limit - (current_time - self.last_request_time))
        self.last_request_time = time.time()
        self.request_count += 1
    
    def get_with_retry(self, url: str, max_retries: int = 3, **kwargs) -> Optional[requests.Response]:
        """带重试的GET请求"""
        for attempt in range(max_retries):
            try:
                self.rate_limit_wait()
                response = self.session.get(url, timeout=10, **kwargs)
                if response.status_code == 200:
                    return response
                elif response.status_code == 429:  # Too Many Requests
                    wait_time = 2 ** attempt
                    logger.warning(f"请求过于频繁，等待 {wait_time} 秒")
                    time.sleep(wait_time)
                else:
                    logger.warning(f"请求失败，状态码: {response.status_code}")
            except Exception as e:
                logger.error(f"请求异常 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(random.uniform(1, 3))
        return None
    
    def extract_dish_info(self, data: Dict[str, Any], region: str, city: str) -> Optional[DishInfo]:
        """从数据中提取菜品信息 - 子类需要实现"""
        raise NotImplementedError
    
    def extract_restaurant_info(self, data: Dict[str, Any], region: str, city: str) -> Optional[RestaurantInfo]:
        """从数据中提取餐厅信息 - 子类需要实现"""
        raise NotImplementedError
    
    def crawl_region_food(self, province: str, city: str, max_pages: int = 10) -> List[DishInfo]:
        """爬取指定地区的美食数据 - 子类需要实现"""
        raise NotImplementedError

class XiaChuFangCrawler(BaseCrawler):
    """下厨房爬虫 - 菜谱和制作方法"""
    
    def __init__(self):
        super().__init__("下厨房")
        self.base_url = "https://www.xiachufang.com"
    
    def search_recipes(self, keyword: str, page: int = 1) -> List[Dict]:
        """搜索菜谱"""
        search_url = f"{self.base_url}/search/"
        params = {
            'keyword': keyword,
            'cat': '1001',  # 菜谱分类
            'page': page
        }
        
        response = self.get_with_retry(search_url, params=params)
        if not response:
            return []
        
        try:
            # 解析搜索结果页面
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(response.text, 'html.parser')
            
            recipes = []
            recipe_items = soup.find_all('div', class_='recipe-item')
            
            for item in recipe_items:
                recipe_data = self.parse_recipe_item(item)
                if recipe_data:
                    recipes.append(recipe_data)
            
            return recipes
        except Exception as e:
            logger.error(f"解析下厨房搜索结果失败: {e}")
            return []
    
    def parse_recipe_item(self, item) -> Optional[Dict]:
        """解析单个菜谱项目"""
        try:
            # 获取菜谱基本信息
            title_elem = item.find('a', class_='recipe-title')
            if not title_elem:
                return None
            
            name = title_elem.get_text().strip()
            recipe_url = urljoin(self.base_url, title_elem.get('href', ''))
            
            # 获取图片
            img_elem = item.find('img')
            image_url = img_elem.get('src', '') if img_elem else ''
            
            # 获取简介
            desc_elem = item.find('div', class_='recipe-desc')
            description = desc_elem.get_text().strip() if desc_elem else ''
            
            # 获取统计信息
            stats = {}
            stat_items = item.find_all('span', class_='stat-item')
            for stat in stat_items:
                text = stat.get_text().strip()
                if '收藏' in text:
                    stats['collections'] = int(re.findall(r'\d+', text)[0]) if re.findall(r'\d+', text) else 0
                elif '浏览' in text:
                    stats['views'] = int(re.findall(r'\d+', text)[0]) if re.findall(r'\d+', text) else 0
            
            return {
                'name': name,
                'url': recipe_url,
                'image_url': image_url,
                'description': description,
                'stats': stats
            }
        except Exception as e:
            logger.error(f"解析菜谱项目失败: {e}")
            return None
    
    def get_recipe_detail(self, recipe_url: str) -> Optional[Dict]:
        """获取菜谱详细信息"""
        response = self.get_with_retry(recipe_url)
        if not response:
            return None
        
        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 菜谱名称
            title_elem = soup.find('h1', class_='recipe-title')
            name = title_elem.get_text().strip() if title_elem else ''
            
            # 菜谱描述
            desc_elem = soup.find('div', class_='recipe-desc')
            description = desc_elem.get_text().strip() if desc_elem else ''
            
            # 食材列表
            ingredients = []
            ingredient_items = soup.find_all('li', class_='ingredient-item')
            for item in ingredient_items:
                name_elem = item.find('span', class_='name')
                amount_elem = item.find('span', class_='amount')
                if name_elem:
                    ingredient_name = name_elem.get_text().strip()
                    amount = amount_elem.get_text().strip() if amount_elem else ''
                    ingredients.append(f"{ingredient_name} {amount}".strip())
            
            # 制作步骤
            steps = []
            step_items = soup.find_all('li', class_='step-item')
            for i, item in enumerate(step_items, 1):
                step_text_elem = item.find('div', class_='step-text')
                if step_text_elem:
                    step_text = step_text_elem.get_text().strip()
                    steps.append(f"{i}. {step_text}")
            
            # 营养信息
            nutrition = {}
            nutrition_items = soup.find_all('div', class_='nutrition-item')
            for item in nutrition_items:
                label_elem = item.find('span', class_='label')
                value_elem = item.find('span', class_='value')
                if label_elem and value_elem:
                    nutrition[label_elem.get_text().strip()] = value_elem.get_text().strip()
            
            # 标签
            tags = []
            tag_items = soup.find_all('a', class_='tag-item')
            for tag in tag_items:
                tags.append(tag.get_text().strip())
            
            return {
                'name': name,
                'description': description,
                'ingredients': ingredients,
                'steps': steps,
                'nutrition': nutrition,
                'tags': tags,
                'url': recipe_url
            }
        except Exception as e:
            logger.error(f"获取菜谱详情失败: {e}")
            return None
    
    def extract_dish_info(self, recipe_data: Dict, region: str, city: str) -> Optional[DishInfo]:
        """从菜谱数据提取菜品信息"""
        try:
            # 获取详细信息
            detail = self.get_recipe_detail(recipe_data['url'])
            if not detail:
                return None
            
            # 推断菜系类型
            cuisine_type = self.infer_cuisine_type(detail['name'], detail.get('tags', []))
            
            # 推断口味标签
            taste_tags = self.extract_taste_tags(detail.get('tags', []))
            
            return DishInfo(
                name=detail['name'],
                region=region,
                city=city,
                cuisine_type=cuisine_type,
                category=self.infer_category(detail['name']),
                description=detail['description'],
                ingredients='; '.join(detail.get('ingredients', [])),
                recipe='\n'.join(detail.get('steps', [])),
                taste_tags=taste_tags,
                image_url=recipe_data.get('image_url', ''),
                source_platform=self.platform_name,
                source_url=recipe_data['url'],
                cultural_background=self.infer_cultural_background(detail['name'], cuisine_type)
            )
        except Exception as e:
            logger.error(f"提取菜品信息失败: {e}")
            return None
    
    def infer_cuisine_type(self, name: str, tags: List[str]) -> str:
        """推断菜系类型"""
        cuisine_keywords = {
            '川菜': ['川菜', '四川', '成都', '重庆', '麻辣', '水煮', '回锅', '宫保', '麻婆'],
            '粤菜': ['粤菜', '广东', '广州', '深圳', '白切', '叉烧', '虾饺', '烧鹅'],
            '湘菜': ['湘菜', '湖南', '长沙', '剁椒', '口味', '湘式'],
            '鲁菜': ['鲁菜', '山东', '济南', '糖醋', '九转', '德州'],
            '苏菜': ['苏菜', '江苏', '南京', '松鼠', '盐水'],
            '浙菜': ['浙菜', '浙江', '杭州', '西湖', '东坡', '龙井'],
            '闽菜': ['闽菜', '福建', '福州', '佛跳墙', '荔枝'],
            '徽菜': ['徽菜', '安徽', '毛豆腐', '臭鳜鱼'],
            '桂菜': ['桂菜', '广西', '桂林', '柳州', '螺蛳粉'],
            '琼菜': ['琼菜', '海南', '文昌鸡', '椰子']
        }
        
        text = name + ' ' + ' '.join(tags)
        for cuisine, keywords in cuisine_keywords.items():
            if any(keyword in text for keyword in keywords):
                return cuisine
        
        return '家常菜'
    
    def infer_category(self, name: str) -> str:
        """推断菜品分类"""
        category_keywords = {
            '主菜': ['肉', '鱼', '鸡', '鸭', '牛', '羊', '猪'],
            '汤品': ['汤', '羹', '煲'],
            '凉菜': ['凉拌', '腌', '泡菜'],
            '面食': ['面', '饺子', '包子', '馄饨'],
            '米饭': ['饭', '粥', '米'],
            '点心': ['糕', '饼', '酥'],
            '饮品': ['茶', '汁', '奶', '酒'],
            '素食': ['豆腐', '青菜', '萝卜', '土豆']
        }
        
        for category, keywords in category_keywords.items():
            if any(keyword in name for keyword in keywords):
                return category
        
        return '其他'
    
    def extract_taste_tags(self, tags: List[str]) -> List[str]:
        """提取口味标签"""
        taste_keywords = ['麻辣', '酸甜', '清淡', '香辣', '鲜美', '爽口', '浓郁', '清香']
        return [tag for tag in tags if any(keyword in tag for keyword in taste_keywords)]
    
    def infer_cultural_background(self, name: str, cuisine_type: str) -> str:
        """推断文化背景"""
        backgrounds = {
            '川菜': '川菜历史悠久，以麻辣鲜香著称，体现了巴蜀文化的热情奔放。',
            '粤菜': '粤菜注重原汁原味，体现了岭南文化的包容开放和对食材的精细追求。',
            '湘菜': '湘菜以辣著称，体现了湖湘文化的豪爽和对香辣口味的偏好。',
            '桂菜': '桂菜融合了汉族和壮族等少数民族的饮食文化，具有浓郁的地方特色。',
            '琼菜': '琼菜以海鲜和热带水果为特色，体现了海南独特的海岛饮食文化。'
        }
        return backgrounds.get(cuisine_type, '体现了中华饮食文化的丰富多样性。')
    
    def crawl_region_food(self, province: str, city: str, max_pages: int = 5) -> List[DishInfo]:
        """爬取指定地区的美食数据"""
        dishes = []
        
        # 根据地区搜索相关菜谱
        region_keywords = [
            f"{province}菜", f"{city}菜", f"{province}特色", f"{city}特色",
            f"{province}美食", f"{city}美食"
        ]
        
        for keyword in region_keywords:
            logger.info(f"搜索关键词: {keyword}")
            
            for page in range(1, max_pages + 1):
                recipes = self.search_recipes(keyword, page)
                if not recipes:
                    break
                
                for recipe in recipes:
                    dish = self.extract_dish_info(recipe, province, city)
                    if dish:
                        dishes.append(dish)
                        # 保存到数据库
                        self.db.save_dish(dish)
                        logger.info(f"成功爬取菜品: {dish.name}")
                
                time.sleep(random.uniform(1, 2))  # 避免请求过快
        
        return dishes

class FoodCrawlerManager:
    """美食爬虫管理器"""
    
    def __init__(self):
        self.crawlers = {
            '下厨房': XiaChuFangCrawler(),
            # 可以继续添加其他平台的爬虫
        }
        self.db = DatabaseManager()
        
        # 初始化数据库表
        sql_file = os.path.join(os.path.dirname(__file__), 'create_food_tables.sql')
        if os.path.exists(sql_file):
            self.db.execute_sql_file(sql_file)
    
    def crawl_all_regions(self, max_pages_per_keyword: int = 3):
        """爬取所有目标地区的美食数据"""
        target_regions = [
            ('广东', '广州'), ('广东', '深圳'), ('广东', '珠海'), ('广东', '佛山'),
            ('广西', '南宁'), ('广西', '桂林'), ('广西', '柳州'),
            ('海南', '海口'), ('海南', '三亚')
        ]
        
        total_dishes = 0
        
        for province, city in target_regions:
            logger.info(f"开始爬取 {province} {city} 的美食数据")
            
            for platform_name, crawler in self.crawlers.items():
                try:
                    dishes = crawler.crawl_region_food(province, city, max_pages_per_keyword)
                    total_dishes += len(dishes)
                    logger.info(f"{platform_name} - {province} {city}: 爬取到 {len(dishes)} 道菜品")
                except Exception as e:
                    logger.error(f"爬取 {platform_name} - {province} {city} 失败: {e}")
        
        logger.info(f"总共爬取到 {total_dishes} 道菜品")
        return total_dishes

def main():
    """主函数"""
    try:
        # 安装必要的依赖
        os.system("pip install beautifulsoup4 fake-useragent pymysql")
        
        manager = FoodCrawlerManager()
        
        logger.info("开始爬取美食数据...")
        total_count = manager.crawl_all_regions(max_pages_per_keyword=3)
        logger.info(f"爬取完成，共获取 {total_count} 道菜品数据")
        
    except Exception as e:
        logger.error(f"爬取过程出错: {e}")

if __name__ == "__main__":
    main()