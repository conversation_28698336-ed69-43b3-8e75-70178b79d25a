/**
 * 全局类型定义文件
 * 包含应用中使用的所有TypeScript类型和接口
 */

// 用户相关类型
export interface User {
  user_id: number;
  username: string;
  email: string;
  phone?: string;
  avatar_url?: string;
}

// 菜品相关类型
export interface Dish {
  dish_id: number;
  name: string;
  cuisine_type: string;
  price: number;
  rating: number;
  review_count: number;
  description: string;
  image_url: string;
  restaurant_name: string;
  region: string;
  tags: string[];
  ingredients?: string[];
  nutrition?: {
    calories: number;
    protein: number;
    fat: number;
    carbs: number;
  };
  spicy_level?: number;
  monthly_sales?: number;
  is_available?: boolean;
  created_at?: string;
  data_source?: string;
}

// 餐厅相关类型
export interface Restaurant {
  restaurant_id: number;
  name: string;
  cuisine_type: string;
  rating: number;
  review_count: number;
  address: string;
  phone?: string;
  business_hours?: string;
  price_range?: string;
  features?: string[];
  image_url?: string;
  monthly_orders?: number;
  is_open?: boolean;
  created_at?: string;
}

// 评价相关类型
export interface Review {
  review_id: number;
  user_id: number;
  dish_id?: number;
  restaurant_id?: number;
  rating: number;
  comment: string;
  created_at: string;
  user_name?: string;
  user_avatar?: string;
  helpful_count?: number;
}

// 订单相关类型
export interface Order {
  order_id: number;
  user_id: number;
  dish_id: number;
  quantity: number;
  total_price: number;
  status: 'pending' | 'confirmed' | 'preparing' | 'delivered' | 'cancelled';
  created_at: string;
  dish_name?: string;
  dish_image?: string;
  restaurant_name?: string;
}

// 收藏相关类型
export interface Favorite {
  favorite_id: number;
  user_id: number;
  dish_id: number;
  created_at: string;
  dish?: Dish;
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message: string;
  error_code?: number;
}

// 分页响应类型
export interface PaginatedResponse<T> {
  items: T[];
  pagination: {
    page: number;
    per_page: number;
    total: number;
    pages: number;
  };
}

// 搜索参数类型
export interface SearchParams {
  query?: string;
  cuisine_type?: string;
  min_price?: number;
  max_price?: number;
  min_rating?: number;
  region?: string;
  tags?: string[];
  sort_by?: 'rating' | 'price' | 'popularity' | 'created_at';
  sort_order?: 'asc' | 'desc';
  page?: number;
  per_page?: number;
}

// 认证相关类型
export interface LoginRequest {
  username: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  phone?: string;
}

export interface AuthResponse {
  success: boolean;
  user?: User;
  token?: string;
  message: string;
}

// 用户资料类型
export interface UserProfile extends User {
  bio?: string;
  location?: string;
  favorite_cuisines?: string[];
  dietary_restrictions?: string[];
  created_at?: string;
  last_login?: string;
}

// 统计数据类型
export interface Statistics {
  total_dishes: number;
  total_restaurants: number;
  total_users: number;
  total_reviews: number;
  popular_cuisines: Array<{
    cuisine_type: string;
    count: number;
  }>;
  top_rated_dishes: Dish[];
  recent_reviews: Review[];
}

// 推荐相关类型
export interface Recommendation {
  dish: Dish;
  score: number;
  reason: string;
}

// 表单状态类型
export interface FormState {
  loading: boolean;
  error: string | null;
  success: boolean;
}

// 模态框状态类型
export interface ModalState {
  isOpen: boolean;
  title?: string;
  content?: React.ReactNode;
}

// 主题类型
export type Theme = 'light' | 'dark' | 'system';

// 语言类型
export type Language = 'zh-CN' | 'en-US';

// 排序选项类型
export type SortOption = {
  value: string;
  label: string;
  field: string;
  order: 'asc' | 'desc';
};

// 筛选选项类型
export type FilterOption = {
  value: string;
  label: string;
  count?: number;
};

// 导出所有类型的联合类型
export type EntityType = User | Dish | Restaurant | Review | Order | Favorite;
