#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版美食推荐API
集成新的爬取数据和扩展功能
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import pymysql
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
import math

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

class EnhancedDatabaseManager:
    """增强版数据库管理器"""
    
    def __init__(self):
        self.connection = self.get_connection()
    
    def get_connection(self):
        """获取数据库连接"""
        try:
            return pymysql.connect(
                host='localhost',
                user='root',
                password='123456',
                database='food_recommendation',
                charset='utf8mb4',
                autocommit=True
            )
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    def search_dishes_enhanced(self, 
                             query: str = "",
                             province: str = "",
                             city: str = "",
                             cuisine_type: str = "",
                             category: str = "",
                             min_price: float = 0,
                             max_price: float = 999999,
                             min_rating: float = 0,
                             source_platform: str = "",
                             page: int = 1,
                             per_page: int = 20) -> Dict[str, Any]:
        """增强版菜品搜索"""
        try:
            cursor = self.connection.cursor(pymysql.cursors.DictCursor)
            
            # 构建基础查询
            base_query = """
            SELECT 
                d.dish_id,
                d.name,
                d.cuisine_type,
                d.category,
                d.price,
                d.rating,
                d.review_count,
                d.description,
                d.ingredients,
                d.cooking_method,
                d.taste_tags,
                d.image_url,
                d.image_urls,
                d.recipe,
                d.spice_level,
                d.cooking_time,
                d.calories_per_serving as calories,
                d.cultural_background,
                d.source_platform,
                d.source_url,
                r.name as restaurant_name,
                rg.province,
                rg.city,
                rg.district
            FROM dishes_extended d
            LEFT JOIN restaurants r ON d.restaurant_id = r.restaurant_id
            LEFT JOIN regions rg ON d.region_id = rg.region_id
            WHERE 1=1
            """
            
            params = []
            
            # 添加搜索条件
            if query:
                base_query += " AND (d.name LIKE %s OR d.description LIKE %s OR d.ingredients LIKE %s)"
                query_param = f"%{query}%"
                params.extend([query_param, query_param, query_param])
            
            if province:
                base_query += " AND rg.province = %s"
                params.append(province)
            
            if city:
                base_query += " AND rg.city = %s"
                params.append(city)
            
            if cuisine_type:
                base_query += " AND d.cuisine_type = %s"
                params.append(cuisine_type)
            
            if category:
                base_query += " AND d.category = %s"
                params.append(category)
            
            if min_price > 0:
                base_query += " AND d.price >= %s"
                params.append(min_price)
            
            if max_price < 999999:
                base_query += " AND d.price <= %s"
                params.append(max_price)
            
            if min_rating > 0:
                base_query += " AND d.rating >= %s"
                params.append(min_rating)
            
            if source_platform:
                base_query += " AND d.source_platform = %s"
                params.append(source_platform)
            
            # 获取总数
            count_query = f"SELECT COUNT(*) as total FROM ({base_query}) as subquery"
            cursor.execute(count_query, params)
            total = cursor.fetchone()['total']
            
            # 添加排序和分页
            base_query += " ORDER BY d.rating DESC, d.review_count DESC"
            offset = (page - 1) * per_page
            base_query += f" LIMIT {per_page} OFFSET {offset}"
            
            # 执行查询
            cursor.execute(base_query, params)
            dishes = cursor.fetchall()
            
            # 处理JSON字段
            for dish in dishes:
                try:
                    if dish['taste_tags']:
                        dish['taste_tags'] = json.loads(dish['taste_tags'])
                    else:
                        dish['taste_tags'] = []
                    
                    if dish['image_urls']:
                        dish['image_urls'] = json.loads(dish['image_urls'])
                    else:
                        dish['image_urls'] = []
                except:
                    dish['taste_tags'] = []
                    dish['image_urls'] = []
                
                # 添加一些默认值
                dish['region'] = dish['province'] or '未知'
                dish['tags'] = dish['taste_tags']
                
                # 如果没有餐厅名称，使用默认值
                if not dish['restaurant_name']:
                    dish['restaurant_name'] = f"{dish['region']}美食"
            
            cursor.close()
            
            # 计算分页信息
            total_pages = math.ceil(total / per_page) if total > 0 else 0
            
            return {
                'success': True,
                'items': dishes,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': total,
                    'pages': total_pages
                },
                'data_sources': {
                    'database_count': len(dishes),
                    'mock_count': 0
                }
            }
            
        except Exception as e:
            logger.error(f"搜索菜品失败: {e}")
            return {
                'success': False,
                'items': [],
                'pagination': {'page': 1, 'per_page': per_page, 'total': 0, 'pages': 0},
                'data_sources': {'database_count': 0, 'mock_count': 0},
                'error': str(e)
            }
    
    def get_dish_detail(self, dish_id: int) -> Optional[Dict]:
        """获取菜品详细信息"""
        try:
            cursor = self.connection.cursor(pymysql.cursors.DictCursor)
            query = """
            SELECT 
                d.*,
                r.name as restaurant_name,
                r.address as restaurant_address,
                r.phone as restaurant_phone,
                r.rating as restaurant_rating,
                rg.province,
                rg.city,
                rg.district
            FROM dishes_extended d
            LEFT JOIN restaurants r ON d.restaurant_id = r.restaurant_id
            LEFT JOIN regions rg ON d.region_id = rg.region_id
            WHERE d.dish_id = %s
            """
            
            cursor.execute(query, (dish_id,))
            dish = cursor.fetchone()
            cursor.close()
            
            if dish:
                # 处理JSON字段
                try:
                    dish['taste_tags'] = json.loads(dish['taste_tags']) if dish['taste_tags'] else []
                    dish['image_urls'] = json.loads(dish['image_urls']) if dish['image_urls'] else []
                    dish['nutritional_info'] = json.loads(dish['nutritional_info']) if dish['nutritional_info'] else {}
                except:
                    dish['taste_tags'] = []
                    dish['image_urls'] = []
                    dish['nutritional_info'] = {}
                
                dish['region'] = dish['province'] or '未知'
            
            return dish
            
        except Exception as e:
            logger.error(f"获取菜品详情失败: {e}")
            return None
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取数据统计信息"""
        try:
            cursor = self.connection.cursor(pymysql.cursors.DictCursor)
            
            stats = {}
            
            # 总菜品数
            cursor.execute("SELECT COUNT(*) as count FROM dishes_extended")
            stats['total_dishes'] = cursor.fetchone()['count']
            
            # 总餐厅数
            cursor.execute("SELECT COUNT(*) as count FROM restaurants")
            stats['total_restaurants'] = cursor.fetchone()['count']
            
            # 按省份统计
            cursor.execute("""
                SELECT rg.province, COUNT(d.dish_id) as count 
                FROM dishes_extended d 
                LEFT JOIN regions rg ON d.region_id = rg.region_id 
                WHERE rg.province IS NOT NULL 
                GROUP BY rg.province
            """)
            stats['by_province'] = {row['province']: row['count'] for row in cursor.fetchall()}
            
            # 按菜系统计
            cursor.execute("""
                SELECT cuisine_type, COUNT(*) as count 
                FROM dishes_extended 
                WHERE cuisine_type IS NOT NULL AND cuisine_type != '' 
                GROUP BY cuisine_type 
                ORDER BY count DESC 
                LIMIT 10
            """)
            stats['by_cuisine'] = {row['cuisine_type']: row['count'] for row in cursor.fetchall()}
            
            # 按平台统计
            cursor.execute("""
                SELECT source_platform, COUNT(*) as count 
                FROM dishes_extended 
                GROUP BY source_platform
            """)
            stats['by_platform'] = {row['source_platform']: row['count'] for row in cursor.fetchall()}
            
            cursor.close()
            return stats
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {}
    
    def get_categories(self) -> List[str]:
        """获取所有菜系分类"""
        try:
            cursor = self.connection.cursor()
            cursor.execute("""
                SELECT DISTINCT cuisine_type 
                FROM dishes_extended 
                WHERE cuisine_type IS NOT NULL AND cuisine_type != '' 
                ORDER BY cuisine_type
            """)
            categories = [row[0] for row in cursor.fetchall()]
            cursor.close()
            return categories
        except Exception as e:
            logger.error(f"获取菜系分类失败: {e}")
            return []
    
    def get_regions(self) -> List[Dict[str, str]]:
        """获取所有地区信息"""
        try:
            cursor = self.connection.cursor(pymysql.cursors.DictCursor)
            cursor.execute("""
                SELECT DISTINCT province, city 
                FROM regions 
                ORDER BY province, city
            """)
            regions = cursor.fetchall()
            cursor.close()
            return regions
        except Exception as e:
            logger.error(f"获取地区信息失败: {e}")
            return []

# 创建数据库管理器实例
db_manager = EnhancedDatabaseManager()

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'ok',
        'timestamp': datetime.now().isoformat(),
        'version': '2.0.0',
        'features': ['enhanced_search', 'multi_platform_data', 'detailed_dishes']
    })

@app.route('/api/dishes/search', methods=['GET'])
def search_dishes():
    """搜索菜品 - 增强版"""
    try:
        # 获取查询参数
        query = request.args.get('query', '').strip()
        province = request.args.get('province', '').strip()
        city = request.args.get('city', '').strip()
        cuisine_type = request.args.get('cuisine_type', '').strip()
        category = request.args.get('category', '').strip()
        min_price = float(request.args.get('min_price', 0))
        max_price = float(request.args.get('max_price', 999999))
        min_rating = float(request.args.get('min_rating', 0))
        source_platform = request.args.get('source_platform', '').strip()
        page = int(request.args.get('page', 1))
        per_page = min(int(request.args.get('per_page', 20)), 100)  # 限制最大100
        
        # 执行搜索
        result = db_manager.search_dishes_enhanced(
            query=query,
            province=province,
            city=city,
            cuisine_type=cuisine_type,
            category=category,
            min_price=min_price,
            max_price=max_price,
            min_rating=min_rating,
            source_platform=source_platform,
            page=page,
            per_page=per_page
        )
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"搜索API错误: {e}")
        return jsonify({
            'success': False,
            'items': [],
            'pagination': {'page': 1, 'per_page': 20, 'total': 0, 'pages': 0},
            'data_sources': {'database_count': 0, 'mock_count': 0},
            'error': str(e)
        }), 500

@app.route('/api/dishes/<int:dish_id>', methods=['GET'])
def get_dish_detail(dish_id):
    """获取菜品详情"""
    try:
        dish = db_manager.get_dish_detail(dish_id)
        if dish:
            return jsonify({
                'success': True,
                'data': dish
            })
        else:
            return jsonify({
                'success': False,
                'error': '菜品不存在'
            }), 404
            
    except Exception as e:
        logger.error(f"获取菜品详情错误: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/categories', methods=['GET'])
def get_categories():
    """获取菜系分类"""
    try:
        categories = db_manager.get_categories()
        return jsonify({
            'success': True,
            'data': categories
        })
    except Exception as e:
        logger.error(f"获取菜系分类错误: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/regions', methods=['GET'])
def get_regions():
    """获取地区信息"""
    try:
        regions = db_manager.get_regions()
        return jsonify({
            'success': True,
            'data': regions
        })
    except Exception as e:
        logger.error(f"获取地区信息错误: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/statistics', methods=['GET'])
def get_statistics():
    """获取数据统计"""
    try:
        stats = db_manager.get_statistics()
        return jsonify({
            'success': True,
            'data': stats
        })
    except Exception as e:
        logger.error(f"获取统计信息错误: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/dishes/random', methods=['GET'])
def get_random_dishes():
    """获取随机推荐菜品"""
    try:
        limit = min(int(request.args.get('limit', 10)), 50)
        province = request.args.get('province', '').strip()
        
        cursor = db_manager.connection.cursor(pymysql.cursors.DictCursor)
        
        query = """
        SELECT 
            d.dish_id, d.name, d.cuisine_type, d.price, d.rating, 
            d.review_count, d.description, d.image_url, d.source_platform,
            r.name as restaurant_name, rg.province, rg.city
        FROM dishes_extended d
        LEFT JOIN restaurants r ON d.restaurant_id = r.restaurant_id
        LEFT JOIN regions rg ON d.region_id = rg.region_id
        WHERE d.rating > 0
        """
        
        params = []
        if province:
            query += " AND rg.province = %s"
            params.append(province)
        
        query += f" ORDER BY RAND() LIMIT {limit}"
        
        cursor.execute(query, params)
        dishes = cursor.fetchall()
        cursor.close()
        
        # 处理数据
        for dish in dishes:
            dish['region'] = dish['province'] or '未知'
            dish['tags'] = []
            if not dish['restaurant_name']:
                dish['restaurant_name'] = f"{dish['region']}美食"
        
        return jsonify({
            'success': True,
            'data': dishes
        })
        
    except Exception as e:
        logger.error(f"获取随机菜品错误: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

if __name__ == '__main__':
    print("=" * 60)
    print("增强版美食推荐API服务启动")
    print("功能特性:")
    print("- 多平台数据整合")
    print("- 增强搜索功能")
    print("- 地区筛选")
    print("- 菜系分类")
    print("- 详细菜品信息")
    print("=" * 60)
    app.run(host='0.0.0.0', port=5000, debug=True)