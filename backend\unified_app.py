#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
美食推荐系统 - 统一后端服务
集成数据库管理、API服务、数据爬虫功能
"""

import json
import hashlib
import jwt
import datetime
import os
import random
from flask import Flask, jsonify, request
from flask_cors import CORS
import pymysql
from contextlib import contextmanager

app = Flask(__name__)
CORS(app, origins=["http://localhost:5173", "http://127.0.0.1:5173", "http://localhost:5174", "http://127.0.0.1:5174"])

# 应用配置
app.config['JWT_SECRET_KEY'] = 'food-recommendation-secret-key-2024'

# MySQL数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '123456789',
    'database': 'food_recommendation',
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

# ================================
# 数据库连接管理
# ================================

@contextmanager
def get_db_connection():
    """获取数据库连接的上下文管理器"""
    conn = None
    try:
        conn = pymysql.connect(**DB_CONFIG)
        yield conn
    except Exception as e:
        if conn:
            conn.rollback()
        raise e
    finally:
        if conn:
            conn.close()

def init_database():
    """初始化数据库和数据表"""
    try:
        # 连接MySQL服务器
        conn = pymysql.connect(
            host=DB_CONFIG['host'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            charset='utf8mb4'
        )
        cursor = conn.cursor()
        
        # 创建数据库
        cursor.execute("CREATE DATABASE IF NOT EXISTS food_recommendation CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        cursor.execute("USE food_recommendation")
        
        # 创建数据表
        create_tables_sql = [
            # 用户表
            '''CREATE TABLE IF NOT EXISTS users (
                user_id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                avatar_url TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                preferences TEXT
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4''',
            
            # 餐厅表
            '''CREATE TABLE IF NOT EXISTS restaurants (
                restaurant_id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                cuisine_type VARCHAR(20),
                rating DECIMAL(3,2),
                review_count INT DEFAULT 0,
                address TEXT,
                phone VARCHAR(20),
                business_hours VARCHAR(50),
                price_range VARCHAR(30),
                features TEXT,
                image_url TEXT,
                monthly_orders INT DEFAULT 0,
                is_open BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4''',
            
            # 菜品表
            '''CREATE TABLE IF NOT EXISTS dishes (
                dish_id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                cuisine_type VARCHAR(20),
                price DECIMAL(8,2),
                rating DECIMAL(3,2),
                review_count INT DEFAULT 0,
                description TEXT,
                image_url TEXT,
                restaurant_name VARCHAR(100),
                region VARCHAR(20),
                tags TEXT,
                ingredients TEXT,
                nutrition TEXT,
                spicy_level INT DEFAULT 0,
                monthly_sales INT DEFAULT 0,
                is_available BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                data_source VARCHAR(50)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4''',
            
            # 收藏表
            '''CREATE TABLE IF NOT EXISTS favorites (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT,
                dish_id INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE KEY unique_favorite (user_id, dish_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4''',
            
            # 评价表
            '''CREATE TABLE IF NOT EXISTS reviews (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                dish_id INT NOT NULL,
                rating INT NOT NULL,
                title VARCHAR(100),
                content TEXT,
                taste_rating INT,
                price_rating INT,
                service_rating INT,
                tags TEXT,
                is_recommended BOOLEAN DEFAULT TRUE,
                is_verified BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_user_id (user_id),
                INDEX idx_dish_id (dish_id),
                INDEX idx_rating (rating)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4'''
        ]
        
        for sql in create_tables_sql:
            cursor.execute(sql)
        
        conn.commit()
        cursor.close()
        conn.close()
        
        print("数据库初始化完成")
        return True
        
    except Exception as e:
        print(f"数据库初始化失败: {e}")
        return False

def init_sample_data():
    """初始化示例数据"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            
            # 检查是否已有数据
            cursor.execute("SELECT COUNT(*) as count FROM dishes")
            dish_count = cursor.fetchone()['count']
            
            if dish_count > 0:
                print(f"数据库已有 {dish_count} 道菜品数据")
                return True
            
            print("开始生成示例数据...")
            
            # 生成菜品数据
            cuisine_data = {
                "川菜": {
                    "dishes": [
                        {"name": "麻婆豆腐", "price": 28, "desc": "四川传统名菜，豆腐嫩滑，麻辣鲜香", "spicy": 4},
                        {"name": "回锅肉", "price": 35, "desc": "川菜经典，肥瘦相间，香辣下饭", "spicy": 3},
                        {"name": "宫保鸡丁", "price": 32, "desc": "鸡肉嫩滑，花生香脆，酸甜微辣", "spicy": 2},
                        {"name": "水煮鱼", "price": 58, "desc": "鱼片鲜嫩，汤汁麻辣，配菜丰富", "spicy": 5},
                        {"name": "担担面", "price": 18, "desc": "四川特色面条，芝麻香浓，辣而不燥", "spicy": 3}
                    ],
                    "restaurants": ["川香园", "蜀味轩", "巴蜀人家"]
                },
                "粤菜": {
                    "dishes": [
                        {"name": "白切鸡", "price": 45, "desc": "粤菜经典，鸡肉鲜嫩，配蘸料食用", "spicy": 0},
                        {"name": "蒜蓉蒸扇贝", "price": 68, "desc": "扇贝鲜美，蒜蓉香浓，营养丰富", "spicy": 0},
                        {"name": "广式烧鹅", "price": 78, "desc": "皮脆肉嫩，香味浓郁，粤菜精品", "spicy": 0},
                        {"name": "虾饺", "price": 32, "desc": "广式点心，皮薄馅鲜，晶莹剔透", "spicy": 0},
                        {"name": "叉烧包", "price": 25, "desc": "广式茶点，包子松软，叉烧香甜", "spicy": 0}
                    ],
                    "restaurants": ["粤香楼", "广府茶餐厅", "岭南食府"]
                },
                "湘菜": {
                    "dishes": [
                        {"name": "剁椒鱼头", "price": 68, "desc": "湘菜代表，鱼头鲜嫩，剁椒香辣", "spicy": 4},
                        {"name": "麻辣子鸡", "price": 48, "desc": "鸡肉香嫩，麻辣过瘾，湘菜经典", "spicy": 5},
                        {"name": "口味虾", "price": 78, "desc": "小龙虾鲜美，香辣开胃，夏日必点", "spicy": 4},
                        {"name": "毛氏红烧肉", "price": 45, "desc": "肥而不腻，入口即化，历史名菜", "spicy": 1},
                        {"name": "辣椒炒肉", "price": 35, "desc": "简单家常菜，香辣下饭，湘菜精髓", "spicy": 3}
                    ],
                    "restaurants": ["湘味轩", "毛家饭店", "辣妹子"]
                }
            }
            
            # 插入餐厅数据
            restaurant_data = []
            restaurant_id = 1
            for cuisine, data in cuisine_data.items():
                for restaurant_name in data["restaurants"]:
                    restaurant_data.append((
                        restaurant_name,
                        cuisine,
                        round(random.uniform(4.0, 4.8), 1),
                        random.randint(200, 5000),
                        f"{cuisine}地区某街道{random.randint(1, 999)}号",
                        f"0{random.randint(10, 99)}-{random.randint(10000000, 99999999)}",
                        "09:00-22:00",
                        f"{random.randint(30, 80)}-{random.randint(100, 200)}元",
                        json.dumps(["包间", "停车位", "WiFi"], ensure_ascii=False),
                        f"https://images.unsplash.com/photo-{random.randint(1500000000, 1700000000)}?w=600&h=400&fit=crop",
                        random.randint(500, 8000),
                        True,
                        datetime.datetime.now().isoformat()
                    ))
                    restaurant_id += 1
            
            cursor.executemany('''
                INSERT INTO restaurants (
                    name, cuisine_type, rating, review_count, address, phone,
                    business_hours, price_range, features, image_url, 
                    monthly_orders, is_open, created_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ''', restaurant_data)
            
            # 插入菜品数据
            dish_data = []
            dish_id = 1
            for cuisine, data in cuisine_data.items():
                for dish_info in data["dishes"]:
                    restaurant = random.choice(data["restaurants"])
                    dish_data.append((
                        dish_info["name"],
                        cuisine,
                        dish_info["price"],
                        round(random.uniform(3.8, 4.9), 1),
                        random.randint(50, 2000),
                        dish_info["desc"],
                        f"https://images.unsplash.com/photo-{random.randint(1500000000, 1700000000)}?w=400&h=300&fit=crop",
                        restaurant,
                        cuisine.replace("菜", ""),
                        json.dumps(["经典", "传统"], ensure_ascii=False),
                        json.dumps(["主料", "调料", "配菜"], ensure_ascii=False),
                        json.dumps({"calories": random.randint(200, 800), "protein": round(random.uniform(8, 35), 1)}, ensure_ascii=False),
                        dish_info["spicy"],
                        random.randint(100, 3000),
                        True,
                        datetime.datetime.now().isoformat(),
                        "system_generated"
                    ))
                    dish_id += 1
            
            cursor.executemany('''
                INSERT INTO dishes (
                    name, cuisine_type, price, rating, review_count, description,
                    image_url, restaurant_name, region, tags, ingredients, nutrition,
                    spicy_level, monthly_sales, is_available, created_at, data_source
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ''', dish_data)
            
            # 插入示例用户
            sample_users = [
                ('admin', '<EMAIL>', hash_password('123456'), '{"preferred_cuisines": ["川菜", "粤菜"]}'),
                ('user1', '<EMAIL>', hash_password('123456'), '{"preferred_cuisines": ["湘菜", "川菜"]}')
            ]
            
            cursor.executemany(
                "INSERT INTO users (username, email, password_hash, preferences) VALUES (%s, %s, %s, %s)",
                sample_users
            )
            
            conn.commit()
            
            print(f"示例数据生成完成: {len(dish_data)}道菜品, {len(restaurant_data)}家餐厅, {len(sample_users)}个用户")
            return True
            
    except Exception as e:
        print(f"示例数据生成失败: {e}")
        return False

# ================================
# 工具函数
# ================================

def hash_password(password):
    """密码哈希"""
    return hashlib.sha256(password.encode()).hexdigest()

def generate_token(user_data):
    """生成JWT令牌"""
    payload = {
        'user_id': user_data['user_id'],
        'username': user_data['username'],
        'exp': datetime.datetime.utcnow() + datetime.timedelta(days=30)
    }
    return jwt.encode(payload, app.config['JWT_SECRET_KEY'], algorithm='HS256')

def verify_token(token):
    """验证JWT令牌"""
    try:
        payload = jwt.decode(token, app.config['JWT_SECRET_KEY'], algorithms=['HS256'])
        return payload
    except (jwt.ExpiredSignatureError, jwt.InvalidTokenError):
        return None

# ================================
# API路由 - 认证
# ================================

@app.route('/api/auth/register', methods=['POST'])
def register():
    """用户注册"""
    try:
        data = request.get_json()
        username = data.get('username')
        email = data.get('email')
        password = data.get('password')
        
        if not all([username, email, password]):
            return jsonify({'success': False, 'message': '缺少必要参数'}), 400
        
        password_hash = hash_password(password)
        
        with get_db_connection() as conn:
            cursor = conn.cursor()
            
            # 检查用户名和邮箱是否已存在
            cursor.execute("SELECT user_id FROM users WHERE username = %s OR email = %s", (username, email))
            if cursor.fetchone():
                return jsonify({'success': False, 'message': '用户名或邮箱已存在'}), 400
            
            # 插入新用户
            cursor.execute("""
                INSERT INTO users (username, email, password_hash, preferences)
                VALUES (%s, %s, %s, %s)
            """, (username, email, password_hash, json.dumps({'preferred_cuisines': []})))
            
            user_id = cursor.lastrowid
            conn.commit()
            
            user_data = {'user_id': user_id, 'username': username, 'email': email}
            token = generate_token(user_data)
            
            return jsonify({
                'success': True,
                'message': '注册成功',
                'user': user_data,
                'token': token
            })
            
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/auth/login', methods=['POST'])
def login():
    """用户登录"""
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        
        if not all([username, password]):
            return jsonify({'success': False, 'message': '缺少用户名或密码'}), 400
        
        password_hash = hash_password(password)
        
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT user_id, username, email, preferences
                FROM users 
                WHERE username = %s AND password_hash = %s
            """, (username, password_hash))
            
            user = cursor.fetchone()
            if not user:
                return jsonify({'success': False, 'message': '用户名或密码错误'}), 401
            
            # 处理preferences字段
            if user.get('preferences'):
                try:
                    user['preferences'] = json.loads(user['preferences'])
                except:
                    user['preferences'] = {'preferred_cuisines': []}
            
            token = generate_token(user)
            
            return jsonify({
                'success': True,
                'message': '登录成功',
                'user': user,
                'token': token
            })
            
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

# ================================
# API路由 - 菜品
# ================================

@app.route('/api/dishes', methods=['GET'])
def get_dishes():
    """获取菜品列表"""
    try:
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 24))
        cuisine_type = request.args.get('cuisine_type')
        search = request.args.get('search')
        
        with get_db_connection() as conn:
            cursor = conn.cursor()
            
            # 构建查询条件
            where_conditions = ["is_available = TRUE"]
            params = []
            
            if cuisine_type:
                where_conditions.append("cuisine_type = %s")
                params.append(cuisine_type)
            
            if search:
                where_conditions.append("(name LIKE %s OR description LIKE %s)")
                params.extend([f"%{search}%", f"%{search}%"])
            
            where_clause = " AND ".join(where_conditions)
            
            # 查询总数
            count_sql = f"SELECT COUNT(*) as total FROM dishes WHERE {where_clause}"
            cursor.execute(count_sql, params)
            total = cursor.fetchone()['total']
            
            # 查询数据
            offset = (page - 1) * per_page
            data_sql = f"""
                SELECT * FROM dishes 
                WHERE {where_clause}
                ORDER BY rating DESC, monthly_sales DESC
                LIMIT %s OFFSET %s
            """
            params.extend([per_page, offset])
            cursor.execute(data_sql, params)
            dishes = cursor.fetchall()
            
            # 处理JSON字段
            for dish in dishes:
                for field in ['tags', 'ingredients', 'nutrition']:
                    if dish.get(field):
                        try:
                            dish[field] = json.loads(dish[field])
                        except:
                            dish[field] = []
            
            return jsonify({
                'success': True,
                'data': dishes,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': total,
                    'pages': (total + per_page - 1) // per_page
                }
            })
            
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/dishes/<int:dish_id>', methods=['GET'])
def get_dish_detail(dish_id):
    """获取菜品详情"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM dishes WHERE dish_id = %s", (dish_id,))
            dish = cursor.fetchone()
            
            if not dish:
                return jsonify({'success': False, 'message': '菜品不存在'}), 404
            
            # 处理JSON字段
            for field in ['tags', 'ingredients', 'nutrition']:
                if dish.get(field):
                    try:
                        dish[field] = json.loads(dish[field])
                    except:
                        dish[field] = []
            
            return jsonify({'success': True, 'data': dish})
            
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

# ================================
# API路由 - 收藏
# ================================

@app.route('/api/users/<int:user_id>/favorites', methods=['GET'])
def get_user_favorites(user_id):
    """获取用户收藏列表"""
    try:
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))
        
        with get_db_connection() as conn:
            cursor = conn.cursor()
            
            # 查询总数
            cursor.execute("SELECT COUNT(*) as total FROM favorites WHERE user_id = %s", (user_id,))
            total = cursor.fetchone()['total']
            
            # 查询数据
            offset = (page - 1) * per_page
            cursor.execute("""
                SELECT d.*, f.created_at as favorited_at
                FROM favorites f
                JOIN dishes d ON f.dish_id = d.dish_id
                WHERE f.user_id = %s
                ORDER BY f.created_at DESC
                LIMIT %s OFFSET %s
            """, (user_id, per_page, offset))
            
            dishes = cursor.fetchall()
            
            # 处理JSON字段
            for dish in dishes:
                for field in ['tags', 'ingredients', 'nutrition']:
                    if dish.get(field):
                        try:
                            dish[field] = json.loads(dish[field])
                        except:
                            dish[field] = []
            
            return jsonify({
                'success': True,
                'data': dishes,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': total,
                    'pages': (total + per_page - 1) // per_page
                }
            })
            
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/users/<int:user_id>/favorites', methods=['POST'])
def add_favorite(user_id):
    """添加收藏"""
    try:
        data = request.get_json()
        dish_id = data.get('dish_id')
        
        if not dish_id:
            return jsonify({'success': False, 'message': '缺少dish_id参数'}), 400
        
        with get_db_connection() as conn:
            cursor = conn.cursor()
            
            # 检查菜品是否存在
            cursor.execute("SELECT dish_id FROM dishes WHERE dish_id = %s", (dish_id,))
            if not cursor.fetchone():
                return jsonify({'success': False, 'message': '菜品不存在'}), 404
            
            # 检查是否已收藏
            cursor.execute("SELECT id FROM favorites WHERE user_id = %s AND dish_id = %s", (user_id, dish_id))
            if cursor.fetchone():
                return jsonify({'success': False, 'message': '已经收藏过了'}), 400
            
            # 添加收藏
            cursor.execute("INSERT INTO favorites (user_id, dish_id) VALUES (%s, %s)", (user_id, dish_id))
            conn.commit()
            
            return jsonify({'success': True, 'message': '收藏成功'})
            
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/users/<int:user_id>/favorites/<int:dish_id>', methods=['DELETE'])
def remove_favorite(user_id, dish_id):
    """取消收藏"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM favorites WHERE user_id = %s AND dish_id = %s", (user_id, dish_id))
            
            if cursor.rowcount == 0:
                return jsonify({'success': False, 'message': '收藏不存在'}), 404
            
            conn.commit()
            return jsonify({'success': True, 'message': '取消收藏成功'})
            
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/users/<int:user_id>/favorites/<int:dish_id>/check', methods=['GET'])
def check_favorite(user_id, dish_id):
    """检查是否已收藏"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT id FROM favorites WHERE user_id = %s AND dish_id = %s", (user_id, dish_id))
            is_favorited = cursor.fetchone() is not None
            
            return jsonify({'success': True, 'is_favorited': is_favorited})
            
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

# ================================
# API路由 - 用户
# ================================

@app.route('/api/users/<int:user_id>', methods=['GET'])
def get_user_profile(user_id):
    """获取用户信息"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT user_id, username, email, avatar_url, created_at, preferences
                FROM users WHERE user_id = %s
            """, (user_id,))
            
            user = cursor.fetchone()
            if not user:
                return jsonify({'success': False, 'message': '用户不存在'}), 404
            
            # 处理preferences字段
            if user.get('preferences'):
                try:
                    user['preferences'] = json.loads(user['preferences'])
                except:
                    user['preferences'] = {'preferred_cuisines': []}
            
            return jsonify({'success': True, 'data': user})
            
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/dishes/search', methods=['GET'])
def search_dishes():
    """搜索菜品 - 优化版本，支持数据库和mock数据"""
    try:
        query = request.args.get('query', '')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))
        cuisine_type = request.args.get('cuisine_type')
        price_min = request.args.get('price_min', type=float)
        price_max = request.args.get('price_max', type=float)
        rating_min = request.args.get('rating_min', type=float)
        sort_by = request.args.get('sort_by', 'relevance')
        
        # 定义mock数据用于补充搜索结果 - 扩展版本
        mock_data = [
            # 鸡肉类菜品
            {'dish_id': 9001, 'name': '白切鸡', 'cuisine_type': '粤菜', 'price': 35.0, 'rating': 4.5, 'review_count': 128, 'description': '传统粤式白切鸡，肉质鲜嫩，配以特制蘸料，口感清淡鲜美', 'image_url': 'https://images.unsplash.com/photo-1604503468506-a8da13d82791?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '粤味轩', 'region': '广东', 'tags': ['经典', '清淡', '营养'], 'data_source': 'mock'},
            {'dish_id': 9002, 'name': '海南鸡饭', 'cuisine_type': '琼菜', 'price': 30.0, 'rating': 4.3, 'review_count': 156, 'description': '海南风味鸡饭，香米配白切鸡，口感丰富，营养均衡', 'image_url': 'https://images.unsplash.com/photo-1512058564366-18510be2db19?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '海南风情', 'region': '海南', 'tags': ['经典', '营养', '香米'], 'data_source': 'mock'},
            {'dish_id': 9025, 'name': '海南黄牛', 'cuisine_type': '琼菜', 'price': 88.0, 'rating': 4.7, 'review_count': 89, 'description': '海南特色黄牛肉，肉质鲜嫩，口感醇厚，营养丰富', 'image_url': 'http://img4.taojindi.com/tc/W/201607/1467785364948.jpg', 'restaurant_name': '海南风情', 'region': '海南', 'tags': ['特色', '鲜嫩', '营养'], 'data_source': 'mock'},
            {'dish_id': 9003, 'name': '口水鸡', 'cuisine_type': '川菜', 'price': 32.0, 'rating': 4.4, 'review_count': 189, 'description': '四川经典凉菜，鸡肉嫩滑，麻辣鲜香，开胃下饭', 'image_url': 'https://images.unsplash.com/photo-1598515214211-89d3c73ae83b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '川香园', 'region': '四川', 'tags': ['麻辣', '凉菜', '开胃'], 'data_source': 'mock'},
            {'dish_id': 9004, 'name': '宫保鸡丁', 'cuisine_type': '川菜', 'price': 28.0, 'rating': 4.2, 'review_count': 234, 'description': '川菜经典，鸡肉嫩滑，花生香脆，酸甜微辣', 'image_url': 'https://images.unsplash.com/photo-1563379091339-03246963d51a?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '蜀味轩', 'region': '四川', 'tags': ['经典', '酸甜', '下饭'], 'data_source': 'mock'},
            {'dish_id': 9005, 'name': '辣子鸡', 'cuisine_type': '川菜', 'price': 36.0, 'rating': 4.6, 'review_count': 298, 'description': '重庆特色辣子鸡，鸡肉鲜嫩，干辣椒香脆，麻辣过瘾', 'image_url': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '重庆火锅城', 'region': '重庆', 'tags': ['麻辣', '香辣', '重庆特色'], 'data_source': 'mock'},
            {'dish_id': 9006, 'name': '左宗棠鸡', 'cuisine_type': '湘菜', 'price': 42.0, 'rating': 4.3, 'review_count': 176, 'description': '湖南名菜左宗棠鸡，外酥内嫩，酸甜适中，回味无穷', 'image_url': 'https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '湘味楼', 'region': '湖南', 'tags': ['酸甜', '外酥内嫩', '湘菜名品'], 'data_source': 'mock'},
            
            # 面条类菜品
            {'dish_id': 9007, 'name': '兰州拉面', 'cuisine_type': '西北菜', 'price': 22.0, 'rating': 4.1, 'review_count': 167, 'description': '西北特色面条，汤清味鲜，面条劲道，牛肉香嫩', 'image_url': 'https://images.unsplash.com/photo-1582204742900-72bb5e4cace3?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '兰州正宗面馆', 'region': '甘肃', 'tags': ['清汤', '劲道', '牛肉'], 'data_source': 'mock'},
            {'dish_id': 9008, 'name': '担担面', 'cuisine_type': '川菜', 'price': 18.0, 'rating': 4.3, 'review_count': 256, 'description': '四川特色面条，芝麻香浓，辣而不燥，口感丰富', 'image_url': 'https://images.unsplash.com/photo-1612929633738-8fe44f7ec841?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '川香面馆', 'region': '四川', 'tags': ['芝麻', '麻辣', '特色'], 'data_source': 'mock'},
            {'dish_id': 9009, 'name': '刀削面', 'cuisine_type': '晋菜', 'price': 20.0, 'rating': 4.2, 'review_count': 198, 'description': '山西特色刀削面，面条厚薄不一，口感独特，配菜丰富', 'image_url': 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '山西面庄', 'region': '山西', 'tags': ['手工', '特色', '口感独特'], 'data_source': 'mock'},
            {'dish_id': 9010, 'name': '热干面', 'cuisine_type': '鄂菜', 'price': 15.0, 'rating': 4.4, 'review_count': 342, 'description': '武汉特色早餐，面条劲道，芝麻酱香浓，简单美味', 'image_url': 'https://images.unsplash.com/photo-1571091718767-18b5b1457add?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '武汉小吃店', 'region': '湖北', 'tags': ['早餐', '芝麻酱', '武汉特色'], 'data_source': 'mock'},
            {'dish_id': 9011, 'name': '炸酱面', 'cuisine_type': '鲁菜', 'price': 24.0, 'rating': 4.0, 'review_count': 123, 'description': '北京传统炸酱面，酱香浓郁，配菜清爽，老北京味道', 'image_url': 'https://images.unsplash.com/photo-1569718212165-3a8278d5f624?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '老北京面馆', 'region': '北京', 'tags': ['传统', '酱香', '北京特色'], 'data_source': 'mock'},
            
            # 米粉粉类
            {'dish_id': 9012, 'name': '桂林米粉', 'cuisine_type': '桂菜', 'price': 25.0, 'rating': 4.2, 'review_count': 89, 'description': '桂林特色米粉，汤鲜味美，配菜丰富，是当地人的最爱', 'image_url': 'https://images.unsplash.com/photo-1569718212165-3a8278d5f624?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '桂林人家', 'region': '广西', 'tags': ['特色', '汤粉', '实惠'], 'data_source': 'mock'},
            {'dish_id': 9013, 'name': '螺蛳粉', 'cuisine_type': '桂菜', 'price': 18.0, 'rating': 4.5, 'review_count': 478, 'description': '柳州特色螺蛳粉，酸辣鲜香，汤汁浓郁，配菜丰富', 'image_url': 'https://images.unsplash.com/photo-1569718319928-1de4ba1d69f2?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '柳州螺蛳粉', 'region': '广西', 'tags': ['酸辣', '特色', '网红'], 'data_source': 'mock'},
            {'dish_id': 9014, 'name': '过桥米线', 'cuisine_type': '滇菜', 'price': 32.0, 'rating': 4.3, 'review_count': 267, 'description': '云南特色过桥米线，汤汁鲜美，配菜精致，营养丰富', 'image_url': 'https://5b0988e595225.cdn.sohucs.com/images/20171212/d0e6885a2d324589979bbcd03253cbf8.jpeg', 'restaurant_name': '云南米线馆', 'region': '云南', 'tags': ['鲜美', '精致', '云南特色'], 'data_source': 'mock'},
            
            # 火锅类
            {'dish_id': 9015, 'name': '麻辣火锅', 'cuisine_type': '川菜', 'price': 88.0, 'rating': 4.7, 'review_count': 456, 'description': '正宗四川火锅，麻辣鲜香，涮菜丰富，适合聚餐', 'image_url': 'https://images.unsplash.com/photo-1582878826629-29b7ad1cdc43?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '重庆老火锅', 'region': '四川', 'tags': ['麻辣', '聚餐', '正宗'], 'data_source': 'mock'},
            {'dish_id': 9016, 'name': '清汤火锅', 'cuisine_type': '粤菜', 'price': 78.0, 'rating': 4.2, 'review_count': 234, 'description': '广式清汤火锅，汤底清淡，食材新鲜，注重原味', 'image_url': 'https://images.unsplash.com/photo-1544963150-1c7ebe1fe982?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '粤港火锅', 'region': '广东', 'tags': ['清淡', '新鲜', '原味'], 'data_source': 'mock'},
            {'dish_id': 9017, 'name': '羊蝎子火锅', 'cuisine_type': '京菜', 'price': 98.0, 'rating': 4.4, 'review_count': 189, 'description': '北京特色羊蝎子火锅，羊肉鲜美，汤底浓郁，冬日暖身', 'image_url': 'https://images.unsplash.com/photo-1544963150-1c7ebe1fe982?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '老北京羊蝎子', 'region': '北京', 'tags': ['羊肉', '浓郁', '暖身'], 'data_source': 'mock'},
            
            # 鱼类菜品
            {'dish_id': 9018, 'name': '水煮鱼', 'cuisine_type': '川菜', 'price': 58.0, 'rating': 4.4, 'review_count': 278, 'description': '川菜经典，鱼片鲜嫩，汤汁麻辣，配菜丰富', 'image_url': 'https://images.unsplash.com/photo-1544963150-1c7ebe1fe982?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '川味鱼庄', 'region': '四川', 'tags': ['鲜嫩', '麻辣', '经典'], 'data_source': 'mock'},
            {'dish_id': 9019, 'name': '清蒸鲈鱼', 'cuisine_type': '粤菜', 'price': 68.0, 'rating': 4.5, 'review_count': 156, 'description': '粤菜经典，鱼肉鲜嫩，清香淡雅，营养丰富', 'image_url': 'https://images.unsplash.com/photo-1571026073021-9e3e30d63641?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '海鲜酒家', 'region': '广东', 'tags': ['清蒸', '鲜嫩', '营养'], 'data_source': 'mock'},
            {'dish_id': 9020, 'name': '酸菜鱼', 'cuisine_type': '川菜', 'price': 48.0, 'rating': 4.6, 'review_count': 389, 'description': '川菜新贵，鱼肉滑嫩，酸菜爽脆，酸辣开胃', 'image_url': 'https://images.unsplash.com/photo-1544963150-1c7ebe1fe982?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '鱼你在一起', 'region': '四川', 'tags': ['酸辣', '开胃', '新贵'], 'data_source': 'mock'},
            {'dish_id': 9021, 'name': '糖醋鱼', 'cuisine_type': '浙菜', 'price': 52.0, 'rating': 4.1, 'review_count': 145, 'description': '江浙名菜，鱼肉鲜美，糖醋味浓，老少皆宜', 'image_url': 'https://images.unsplash.com/photo-1544963150-1c7ebe1fe982?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '江南鱼馆', 'region': '浙江', 'tags': ['糖醋', '鲜美', '老少皆宜'], 'data_source': 'mock'},
            
            # 点心小食类
            {'dish_id': 9022, 'name': '叉烧包', 'cuisine_type': '粤菜', 'price': 18.0, 'rating': 4.1, 'review_count': 203, 'description': '广式茶点经典，叉烧馅料丰富，包子皮松软香甜', 'image_url': 'https://images.unsplash.com/photo-1496116218417-1a781b1c416c?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '港式茶餐厅', 'region': '广东', 'tags': ['茶点', '经典', '甜味'], 'data_source': 'mock'},
            {'dish_id': 9023, 'name': '小笼包', 'cuisine_type': '苏菜', 'price': 26.0, 'rating': 4.6, 'review_count': 312, 'description': '江南名点，皮薄汁多，鲜美可口，轻咬一口满嘴香', 'image_url': 'https://images.unsplash.com/photo-1601314002957-3f1df6b4da90?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '江南小笼', 'region': '江苏', 'tags': ['皮薄', '汁多', '精致'], 'data_source': 'mock'},
            {'dish_id': 9024, 'name': '煎饺', 'cuisine_type': '鲁菜', 'price': 16.0, 'rating': 4.3, 'review_count': 178, 'description': '山东特色煎饺，底部金黄酥脆，馅料丰富，一口一个', 'image_url': 'https://images.unsplash.com/photo-1496116218417-1a781b1c416c?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '山东饺子馆', 'region': '山东', 'tags': ['酥脆', '丰富', '小巧'], 'data_source': 'mock'},
            {'dish_id': 9025, 'name': '生煎包', 'cuisine_type': '沪菜', 'price': 20.0, 'rating': 4.4, 'review_count': 256, 'description': '上海特色生煎包，底部焦脆，汁水丰富，香气扑鼻', 'image_url': 'https://images.unsplash.com/photo-1496116218417-1a781b1c416c?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '上海小笼', 'region': '上海', 'tags': ['焦脆', '汁水', '香气'], 'data_source': 'mock'},
            
            # 肉类菜品
            {'dish_id': 9026, 'name': '红烧肉', 'cuisine_type': '苏菜', 'price': 45.0, 'rating': 4.5, 'review_count': 298, 'description': '江南名菜红烧肉，肥瘦相间，入口即化，甜而不腻', 'image_url': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '江南菜馆', 'region': '江苏', 'tags': ['入口即化', '甜而不腻', '名菜'], 'data_source': 'mock'},
            {'dish_id': 9027, 'name': '东坡肉', 'cuisine_type': '浙菜', 'price': 48.0, 'rating': 4.3, 'review_count': 189, 'description': '杭州名菜东坡肉，肉质酥烂，汁浓味醇，色泽红亮', 'image_url': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '杭帮菜', 'region': '浙江', 'tags': ['酥烂', '汁浓', '红亮'], 'data_source': 'mock'},
            {'dish_id': 9028, 'name': '回锅肉', 'cuisine_type': '川菜', 'price': 35.0, 'rating': 4.4, 'review_count': 345, 'description': '川菜经典回锅肉，肥瘦搭配，豆瓣酱香，下饭神器', 'image_url': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '川香居', 'region': '四川', 'tags': ['豆瓣酱', '下饭', '经典'], 'data_source': 'mock'},
            {'dish_id': 9029, 'name': '糖醋里脊', 'cuisine_type': '鲁菜', 'price': 38.0, 'rating': 4.2, 'review_count': 234, 'description': '山东名菜糖醋里脊，外酥内嫩，酸甜适口，色泽金黄', 'image_url': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '鲁菜馆', 'region': '山东', 'tags': ['外酥内嫩', '酸甜', '金黄'], 'data_source': 'mock'},
            
            # 汤类
            {'dish_id': 9030, 'name': '酸辣汤', 'cuisine_type': '川菜', 'price': 16.0, 'rating': 4.0, 'review_count': 156, 'description': '川式酸辣汤，酸辣开胃，配菜丰富，汤汁浓郁', 'image_url': 'https://images.unsplash.com/photo-1547592166-23ac45744acd?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '川味小馆', 'region': '四川', 'tags': ['酸辣', '开胃', '浓郁'], 'data_source': 'mock'},
            {'dish_id': 9031, 'name': '冬瓜排骨汤', 'cuisine_type': '粤菜', 'price': 28.0, 'rating': 4.2, 'review_count': 123, 'description': '广式靓汤，冬瓜清香，排骨鲜美，清热去火', 'image_url': 'https://images.unsplash.com/photo-1547592166-23ac45744acd?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '广式汤品', 'region': '广东', 'tags': ['清香', '鲜美', '清热'], 'data_source': 'mock'},
            {'dish_id': 9032, 'name': '紫菜蛋花汤', 'cuisine_type': '家常菜', 'price': 12.0, 'rating': 3.9, 'review_count': 89, 'description': '家常紫菜蛋花汤，简单清淡，营养丰富，老少皆宜', 'image_url': 'https://images.unsplash.com/photo-1547592166-23ac45744acd?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '家常小厨', 'region': '全国', 'tags': ['简单', '清淡', '营养'], 'data_source': 'mock'},
            
            # 素食类
            {'dish_id': 9033, 'name': '麻婆豆腐', 'cuisine_type': '川菜', 'price': 22.0, 'rating': 4.3, 'review_count': 267, 'description': '川菜经典麻婆豆腐，豆腐嫩滑，麻辣鲜香，下饭佳品', 'image_url': 'https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '麻辣诱惑', 'region': '四川', 'tags': ['嫩滑', '麻辣', '下饭'], 'data_source': 'mock'},
            {'dish_id': 9034, 'name': '地三鲜', 'cuisine_type': '东北菜', 'price': 26.0, 'rating': 4.1, 'review_count': 178, 'description': '东北名菜地三鲜，土豆茄子青椒，软糯香甜，家常美味', 'image_url': 'https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '东北人家', 'region': '东北', 'tags': ['软糯', '香甜', '家常'], 'data_source': 'mock'},
            {'dish_id': 9035, 'name': '干煸四季豆', 'cuisine_type': '川菜', 'price': 24.0, 'rating': 4.2, 'review_count': 145, 'description': '川式干煸四季豆，外皮起皱，内里鲜嫩，香辣下饭', 'image_url': 'https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '川菜世家', 'region': '四川', 'tags': ['起皱', '鲜嫩', '香辣'], 'data_source': 'mock'},
            
            # 饺子馄饨类
            {'dish_id': 9036, 'name': '猪肉韭菜饺子', 'cuisine_type': '北方菜', 'price': 20.0, 'rating': 4.4, 'review_count': 312, 'description': '经典猪肉韭菜馅饺子，皮薄馅大，鲜美多汁', 'image_url': 'https://images.unsplash.com/photo-1496116218417-1a781b1c416c?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '北方饺子馆', 'region': '北京', 'tags': ['经典', '多汁', '家常'], 'data_source': 'mock'},
            {'dish_id': 9037, 'name': '三鲜饺子', 'cuisine_type': '北方菜', 'price': 24.0, 'rating': 4.3, 'review_count': 256, 'description': '虾仁、韭黄、鸡蛋三鲜馅，口感丰富，营养均衡', 'image_url': 'https://images.unsplash.com/photo-1496116218417-1a781b1c416c?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '老字号饺子', 'region': '天津', 'tags': ['三鲜', '营养', '鲜美'], 'data_source': 'mock'},
            {'dish_id': 9038, 'name': '鲜肉小馄饨', 'cuisine_type': '苏菜', 'price': 16.0, 'rating': 4.2, 'review_count': 189, 'description': '江南小馄饨，皮薄如纸，汤清肉嫩，早餐首选', 'image_url': 'https://images.unsplash.com/photo-1496116218417-1a781b1c416c?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '江南小吃', 'region': '江苏', 'tags': ['皮薄', '清汤', '早餐'], 'data_source': 'mock'},
            {'dish_id': 9039, 'name': '大馄饨', 'cuisine_type': '沪菜', 'price': 18.0, 'rating': 4.1, 'review_count': 234, 'description': '上海大馄饨，馅料丰富，汤汁鲜美，分量十足', 'image_url': 'https://images.unsplash.com/photo-1496116218417-1a781b1c416c?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '上海小厨', 'region': '上海', 'tags': ['丰富', '鲜美', '分量足'], 'data_source': 'mock'},
            
            # 粥类
            {'dish_id': 9040, 'name': '白粥', 'cuisine_type': '粤菜', 'price': 8.0, 'rating': 3.8, 'review_count': 123, 'description': '广式白粥，米香清淡，养胃健脾，搭配咸菜最佳', 'image_url': 'https://images.unsplash.com/photo-1547592166-23ac45744acd?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '粥粉面世家', 'region': '广东', 'tags': ['清淡', '养胃', '经典'], 'data_source': 'mock'},
            {'dish_id': 9041, 'name': '瘦肉粥', 'cuisine_type': '粤菜', 'price': 12.0, 'rating': 4.0, 'review_count': 189, 'description': '广式瘦肉粥，肉丝嫩滑，粥底绵密，营养丰富', 'image_url': 'https://images.unsplash.com/photo-1547592166-23ac45744acd?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '港式茶餐厅', 'region': '广东', 'tags': ['嫩滑', '绵密', '营养'], 'data_source': 'mock'},
            {'dish_id': 9042, 'name': '皮蛋瘦肉粥', 'cuisine_type': '粤菜', 'price': 15.0, 'rating': 4.3, 'review_count': 298, 'description': '经典皮蛋瘦肉粥，咸鲜适口，口感层次丰富', 'image_url': 'https://images.unsplash.com/photo-1547592166-23ac45744acd?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '粥品天下', 'region': '广东', 'tags': ['咸鲜', '层次', '经典'], 'data_source': 'mock'},
            {'dish_id': 9043, 'name': '青菜粥', 'cuisine_type': '家常菜', 'price': 10.0, 'rating': 3.9, 'review_count': 145, 'description': '清淡青菜粥，蔬菜清香，粥质绵软，健康养生', 'image_url': 'https://images.unsplash.com/photo-1547592166-23ac45744acd?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '健康粥铺', 'region': '全国', 'tags': ['清香', '绵软', '养生'], 'data_source': 'mock'},
            
            # 烧烤类
            {'dish_id': 9044, 'name': '羊肉串', 'cuisine_type': '新疆菜', 'price': 3.0, 'rating': 4.5, 'review_count': 567, 'description': '新疆正宗羊肉串，肉质鲜嫩，孜然香浓，外焦内嫩', 'image_url': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '新疆烧烤', 'region': '新疆', 'tags': ['鲜嫩', '孜然', '正宗'], 'data_source': 'mock'},
            {'dish_id': 9045, 'name': '烤鸡翅', 'cuisine_type': '烧烤', 'price': 5.0, 'rating': 4.2, 'review_count': 423, 'description': '蜜汁烤鸡翅，外皮焦糖，内里多汁，甜咸适中', 'image_url': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '夜市烧烤', 'region': '全国', 'tags': ['焦糖', '多汁', '甜咸'], 'data_source': 'mock'},
            {'dish_id': 9046, 'name': '烤茄子', 'cuisine_type': '烧烤', 'price': 8.0, 'rating': 4.0, 'review_count': 234, 'description': '蒜蓉烤茄子，茄子软糯，蒜香浓郁，素食最爱', 'image_url': 'https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '街边烧烤', 'region': '全国', 'tags': ['软糯', '蒜香', '素食'], 'data_source': 'mock'},
            {'dish_id': 9047, 'name': '烤韭菜', 'cuisine_type': '烧烤', 'price': 6.0, 'rating': 3.9, 'review_count': 189, 'description': '炭火烤韭菜，韭菜香味浓郁，微焦脆嫩，配酒佳品', 'image_url': 'https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '深夜烧烤', 'region': '全国', 'tags': ['香味', '脆嫩', '配酒'], 'data_source': 'mock'},
            
            # 海鲜类
            {'dish_id': 9048, 'name': '白灼虾', 'cuisine_type': '粤菜', 'price': 58.0, 'rating': 4.6, 'review_count': 345, 'description': '粤式白灼虾，虾肉Q弹，原汁原味，蘸酱油食用', 'image_url': 'https://images.unsplash.com/photo-1571026073021-9e3e30d63641?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '海鲜大排档', 'region': '广东', 'tags': ['Q弹', '原味', '新鲜'], 'data_source': 'mock'},
            {'dish_id': 9049, 'name': '蒜蓉粉丝蒸扇贝', 'cuisine_type': '粤菜', 'price': 68.0, 'rating': 4.4, 'review_count': 267, 'description': '蒜蓉粉丝蒸扇贝，扇贝鲜甜，粉丝吸汁，蒜香四溢', 'image_url': 'https://images.unsplash.com/photo-1571026073021-9e3e30d63641?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '海上鲜', 'region': '广东', 'tags': ['鲜甜', '吸汁', '蒜香'], 'data_source': 'mock'},
            {'dish_id': 9050, 'name': '麻辣小龙虾', 'cuisine_type': '湘菜', 'price': 78.0, 'rating': 4.7, 'review_count': 589, 'description': '湖南麻辣小龙虾，虾肉紧实，麻辣鲜香，夏日必备', 'image_url': 'https://images.unsplash.com/photo-1571026073021-9e3e30d63641?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '麻辣诱惑', 'region': '湖南', 'tags': ['紧实', '麻辣', '夏日'], 'data_source': 'mock'},
            {'dish_id': 9051, 'name': '蒸蛋', 'cuisine_type': '粤菜', 'price': 38.0, 'rating': 4.2, 'review_count': 198, 'description': '蟹黄蒸蛋，蛋羹嫩滑，蟹黄鲜美，营养丰富', 'image_url': 'https://images.unsplash.com/photo-1571026073021-9e3e30d63641?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '粤港茶楼', 'region': '广东', 'tags': ['嫩滑', '鲜美', '营养'], 'data_source': 'mock'},
            
            # 凉菜类
            {'dish_id': 9052, 'name': '拍黄瓜', 'cuisine_type': '家常菜', 'price': 12.0, 'rating': 4.0, 'review_count': 234, 'description': '经典拍黄瓜，爽脆开胃，蒜香清淡，夏日消暑', 'image_url': 'https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '家常小馆', 'region': '全国', 'tags': ['爽脆', '开胃', '消暑'], 'data_source': 'mock'},
            {'dish_id': 9053, 'name': '凉拌海带丝', 'cuisine_type': '家常菜', 'price': 14.0, 'rating': 3.9, 'review_count': 156, 'description': '凉拌海带丝，口感脆嫩，醋香开胃，低脂健康', 'image_url': 'https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '清爽凉菜', 'region': '全国', 'tags': ['脆嫩', '醋香', '健康'], 'data_source': 'mock'},
            {'dish_id': 9054, 'name': '夫妻肺片', 'cuisine_type': '川菜', 'price': 32.0, 'rating': 4.5, 'review_count': 345, 'description': '川菜经典夫妻肺片，麻辣鲜香，口感丰富，下酒菜', 'image_url': 'https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '川味小馆', 'region': '四川', 'tags': ['麻辣', '丰富', '下酒'], 'data_source': 'mock'},
            {'dish_id': 9055, 'name': '蒜泥白肉', 'cuisine_type': '川菜', 'price': 28.0, 'rating': 4.3, 'review_count': 267, 'description': '川式蒜泥白肉，肉片薄嫩，蒜泥香浓，清爽不腻', 'image_url': 'https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '川香园', 'region': '四川', 'tags': ['薄嫩', '蒜香', '清爽'], 'data_source': 'mock'},
            
            # 炒菜类
            {'dish_id': 9056, 'name': '西红柿炒鸡蛋', 'cuisine_type': '家常菜', 'price': 18.0, 'rating': 4.2, 'review_count': 456, 'description': '经典西红柿炒鸡蛋，酸甜开胃，营养丰富，家常必备', 'image_url': 'https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '家常菜馆', 'region': '全国', 'tags': ['酸甜', '营养', '家常'], 'data_source': 'mock'},
            {'dish_id': 9057, 'name': '青椒肉丝', 'cuisine_type': '家常菜', 'price': 22.0, 'rating': 4.1, 'review_count': 298, 'description': '青椒肉丝，肉丝嫩滑，青椒脆嫩，下饭经典', 'image_url': 'https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '百姓食堂', 'region': '全国', 'tags': ['嫩滑', '脆嫩', '下饭'], 'data_source': 'mock'},
            {'dish_id': 9058, 'name': '鱼香肉丝', 'cuisine_type': '川菜', 'price': 26.0, 'rating': 4.4, 'review_count': 378, 'description': '川菜经典鱼香肉丝，酸甜微辣，色泽红亮，开胃下饭', 'image_url': 'https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '川菜馆', 'region': '四川', 'tags': ['酸甜', '红亮', '开胃'], 'data_source': 'mock'},
            {'dish_id': 9026, 'name': '京酱肉丝', 'cuisine_type': '京菜', 'price': 32.0, 'rating': 4.5, 'review_count': 156, 'description': '北京传统名菜，肉丝嫩滑，甜面酱香浓，配豆腐皮卷食', 'image_url': 'https://cp1.douguo.com/upload/caiku/b/5/1/690x390_b5c92f9747ea9ebcc5b01062b0f886b1.jpeg', 'restaurant_name': '京味楼', 'region': '北京', 'tags': ['传统', '嫩滑', '香浓'], 'data_source': 'mock'},
            {'dish_id': 9059, 'name': '土豆丝', 'cuisine_type': '家常菜', 'price': 15.0, 'rating': 3.8, 'review_count': 189, 'description': '醋溜土豆丝，土豆丝脆嫩，酸香开胃，简单美味', 'image_url': 'https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '简单食堂', 'region': '全国', 'tags': ['脆嫩', '酸香', '简单'], 'data_source': 'mock'},
            
            # 河南菜
            {'dish_id': 9060, 'name': '胡辣汤', 'cuisine_type': '豫菜', 'price': 8.0, 'rating': 4.1, 'review_count': 267, 'description': '河南特色胡辣汤，胡椒味浓，辣中带香，暖胃醒神', 'image_url': 'https://images.unsplash.com/photo-1547592166-23ac45744acd?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '河南小吃', 'region': '河南', 'tags': ['胡椒', '暖胃', '特色'], 'data_source': 'mock'},
            {'dish_id': 9061, 'name': '烩面', 'cuisine_type': '豫菜', 'price': 18.0, 'rating': 4.3, 'review_count': 234, 'description': '河南烩面，面条筋道，汤汁浓郁，配菜丰富', 'image_url': 'https://images.unsplash.com/photo-1569718212165-3a8278d5f624?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '豫菜馆', 'region': '河南', 'tags': ['筋道', '浓郁', '丰富'], 'data_source': 'mock'},
            
            # 陕西菜
            {'dish_id': 9062, 'name': '肉夹馍', 'cuisine_type': '陕菜', 'price': 12.0, 'rating': 4.4, 'review_count': 456, 'description': '陕西肉夹馍，馍香肉嫩，汁水丰富，西北特色', 'image_url': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '西安小吃', 'region': '陕西', 'tags': ['馍香', '肉嫩', '特色'], 'data_source': 'mock'},
            {'dish_id': 9063, 'name': '凉皮', 'cuisine_type': '陕菜', 'price': 10.0, 'rating': 4.2, 'review_count': 345, 'description': '陕西凉皮，皮子筋道，调料丰富，酸辣爽口', 'image_url': 'https://images.unsplash.com/photo-1569718212165-3a8278d5f624?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '关中面食', 'region': '陕西', 'tags': ['筋道', '酸辣', '爽口'], 'data_source': 'mock'},
            {'dish_id': 9064, 'name': '羊肉泡馍', 'cuisine_type': '陕菜', 'price': 32.0, 'rating': 4.5, 'review_count': 298, 'description': '西安羊肉泡馍，羊肉鲜美，汤汁浓郁，馍块吸汁', 'image_url': 'https://images.unsplash.com/photo-1547592166-23ac45744acd?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '老西安', 'region': '陕西', 'tags': ['鲜美', '浓郁', '吸汁'], 'data_source': 'mock'},
            
            # 甜品类
            {'dish_id': 9065, 'name': '红豆沙', 'cuisine_type': '甜品', 'price': 15.0, 'rating': 4.0, 'review_count': 189, 'description': '传统红豆沙，豆香浓郁，甜而不腻，温润滋补', 'image_url': 'https://images.unsplash.com/photo-1551024506-0bccd828d307?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '港式甜品', 'region': '香港', 'tags': ['豆香', '甜腻', '滋补'], 'data_source': 'mock'},
            {'dish_id': 9066, 'name': '芒果布丁', 'cuisine_type': '甜品', 'price': 18.0, 'rating': 4.3, 'review_count': 234, 'description': '芒果布丁，果香浓郁，口感顺滑，颜值很高', 'image_url': 'https://images.unsplash.com/photo-1551024506-0bccd828d307?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '时尚甜品', 'region': '全国', 'tags': ['果香', '顺滑', '颜值'], 'data_source': 'mock'},
            {'dish_id': 9067, 'name': '双皮奶', 'cuisine_type': '粤菜', 'price': 16.0, 'rating': 4.2, 'review_count': 267, 'description': '广式双皮奶，奶香浓郁，口感嫩滑，经典甜品', 'image_url': 'https://images.unsplash.com/photo-1551024506-0bccd828d307?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '广式茶楼', 'region': '广东', 'tags': ['奶香', '嫩滑', '经典'], 'data_source': 'mock'},
            {'dish_id': 9068, 'name': '绿豆汤', 'cuisine_type': '甜品', 'price': 8.0, 'rating': 3.9, 'review_count': 145, 'description': '清热绿豆汤，清香甘甜，消暑解渴，夏日必备', 'image_url': 'https://images.unsplash.com/photo-1551024506-0bccd828d307?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '清凉甜品', 'region': '全国', 'tags': ['清香', '消暑', '解渴'], 'data_source': 'mock'},
            
            # 饮品类
            {'dish_id': 9069, 'name': '珍珠奶茶', 'cuisine_type': '饮品', 'price': 12.0, 'rating': 4.4, 'review_count': 567, 'description': '经典珍珠奶茶，奶香茶香，珍珠Q弹，年轻人最爱', 'image_url': 'https://images.unsplash.com/photo-1551024506-0bccd828d307?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '奶茶店', 'region': '台湾', 'tags': ['奶香', 'Q弹', '年轻'], 'data_source': 'mock'},
            {'dish_id': 9070, 'name': '柠檬茶', 'cuisine_type': '饮品', 'price': 10.0, 'rating': 4.1, 'review_count': 298, 'description': '清新柠檬茶，酸甜开胃，维C丰富，清热解腻', 'image_url': 'https://images.unsplash.com/photo-1551024506-0bccd828d307?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '茶饮店', 'region': '全国', 'tags': ['清新', '酸甜', '维C'], 'data_source': 'mock'},
            {'dish_id': 9071, 'name': '鲜榨橙汁', 'cuisine_type': '饮品', 'price': 15.0, 'rating': 4.2, 'review_count': 234, 'description': '新鲜橙汁，果香浓郁，维生素丰富，健康天然', 'image_url': 'https://images.unsplash.com/photo-1551024506-0bccd828d307?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '鲜果汁吧', 'region': '全国', 'tags': ['果香', '维生素', '天然'], 'data_source': 'mock'},
            {'dish_id': 9072, 'name': '豆浆', 'cuisine_type': '饮品', 'price': 6.0, 'rating': 4.0, 'review_count': 345, 'description': '现磨豆浆，豆香浓郁，营养丰富，早餐必备', 'image_url': 'https://images.unsplash.com/photo-1551024506-0bccd828d307?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '早餐店', 'region': '全国', 'tags': ['豆香', '营养', '早餐'], 'data_source': 'mock'},
            
            # 小吃类
            {'dish_id': 9073, 'name': '煎饼果子', 'cuisine_type': '津菜', 'price': 8.0, 'rating': 4.3, 'review_count': 456, 'description': '天津煎饼果子，薄脆香酥，鸡蛋嫩滑，街头美食', 'image_url': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '天津小吃', 'region': '天津', 'tags': ['薄脆', '嫩滑', '街头'], 'data_source': 'mock'},
            {'dish_id': 9074, 'name': '驴打滚', 'cuisine_type': '京菜', 'price': 12.0, 'rating': 4.1, 'review_count': 189, 'description': '北京驴打滚，外皮软糯，豆沙香甜，传统小吃', 'image_url': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '老北京小吃', 'region': '北京', 'tags': ['软糯', '香甜', '传统'], 'data_source': 'mock'},
            {'dish_id': 9075, 'name': '糖葫芦', 'cuisine_type': '小吃', 'price': 5.0, 'rating': 4.0, 'review_count': 298, 'description': '冰糖葫芦，外脆内酸，酸甜开胃，童年回忆', 'image_url': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '街边小摊', 'region': '全国', 'tags': ['外脆', '酸甜', '童年'], 'data_source': 'mock'},
            {'dish_id': 9076, 'name': '臭豆腐', 'cuisine_type': '湘菜', 'price': 10.0, 'rating': 4.2, 'review_count': 345, 'description': '长沙臭豆腐，外焦内嫩，臭香独特，越吃越香', 'image_url': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '湘味小吃', 'region': '湖南', 'tags': ['外焦', '独特', '越吃越香'], 'data_source': 'mock'},
            
            # 东北菜
            {'dish_id': 9077, 'name': '锅包肉', 'cuisine_type': '东北菜', 'price': 38.0, 'rating': 4.5, 'review_count': 423, 'description': '东北锅包肉，外酥内嫩，酸甜适口，色泽金黄', 'image_url': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '东北菜馆', 'region': '东北', 'tags': ['外酥', '酸甜', '金黄'], 'data_source': 'mock'},
            {'dish_id': 9078, 'name': '白肉血肠', 'cuisine_type': '东北菜', 'price': 42.0, 'rating': 4.2, 'review_count': 234, 'description': '东北白肉血肠，肉质鲜嫩，血肠香浓，东北特色', 'image_url': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '老东北', 'region': '东北', 'tags': ['鲜嫩', '香浓', '特色'], 'data_source': 'mock'},
            {'dish_id': 9079, 'name': '小鸡炖蘑菇', 'cuisine_type': '东北菜', 'price': 48.0, 'rating': 4.4, 'review_count': 298, 'description': '东北小鸡炖蘑菇，鸡肉鲜美，蘑菇香滑，汤汁浓郁', 'image_url': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '东北农家', 'region': '东北', 'tags': ['鲜美', '香滑', '浓郁'], 'data_source': 'mock'},
            
            # 新疆菜
            {'dish_id': 9080, 'name': '大盘鸡', 'cuisine_type': '新疆菜', 'price': 68.0, 'rating': 4.6, 'review_count': 456, 'description': '新疆大盘鸡，鸡肉嫩滑，土豆软糯，辣而不燥', 'image_url': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '新疆风味', 'region': '新疆', 'tags': ['嫩滑', '软糯', '辣而不燥'], 'data_source': 'mock'},
            {'dish_id': 9081, 'name': '抓饭', 'cuisine_type': '新疆菜', 'price': 35.0, 'rating': 4.3, 'review_count': 267, 'description': '新疆抓饭，米粒香甜，羊肉鲜美，营养丰富', 'image_url': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '天山饭庄', 'region': '新疆', 'tags': ['香甜', '鲜美', '营养'], 'data_source': 'mock'},
            {'dish_id': 9082, 'name': '手抓羊肉', 'cuisine_type': '新疆菜', 'price': 88.0, 'rating': 4.5, 'review_count': 345, 'description': '新疆手抓羊肉，羊肉鲜嫩，原汁原味，豪迈粗犷', 'image_url': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '草原人家', 'region': '新疆', 'tags': ['鲜嫩', '原味', '豪迈'], 'data_source': 'mock'},
            
            # 云南菜
            {'dish_id': 9083, 'name': '汽锅鸡', 'cuisine_type': '滇菜', 'price': 78.0, 'rating': 4.4, 'review_count': 234, 'description': '云南汽锅鸡，鸡肉鲜嫩，汤汁清香，营养丰富', 'image_url': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '云南菜馆', 'region': '云南', 'tags': ['鲜嫩', '清香', '营养'], 'data_source': 'mock'},
            {'dish_id': 9084, 'name': '宜良烤鸭', 'cuisine_type': '滇菜', 'price': 88.0, 'rating': 4.3, 'review_count': 189, 'description': '宜良烤鸭，皮脆肉嫩，香味浓郁，云南名菜', 'image_url': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '滇味楼', 'region': '云南', 'tags': ['皮脆', '浓郁', '名菜'], 'data_source': 'mock'},
            
            # 贵州菜
            {'dish_id': 9085, 'name': '酸汤鱼', 'cuisine_type': '黔菜', 'price': 68.0, 'rating': 4.5, 'review_count': 298, 'description': '贵州酸汤鱼，汤汁酸辣，鱼肉鲜嫩，开胃爽口', 'image_url': 'https://images.unsplash.com/photo-1544963150-1c7ebe1fe982?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '黔菜馆', 'region': '贵州', 'tags': ['酸辣', '鲜嫩', '爽口'], 'data_source': 'mock'},
            {'dish_id': 9086, 'name': '丝娃娃', 'cuisine_type': '黔菜', 'price': 25.0, 'rating': 4.2, 'review_count': 167, 'description': '贵阳丝娃娃，蔬菜丰富，口感清脆，蘸料香辣', 'image_url': 'https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '贵阳小吃', 'region': '贵州', 'tags': ['丰富', '清脆', '香辣'], 'data_source': 'mock'},
            
            # 内蒙古菜
            {'dish_id': 9087, 'name': '烤全羊', 'cuisine_type': '蒙菜', 'price': 688.0, 'rating': 4.7, 'review_count': 123, 'description': '内蒙烤全羊，羊肉鲜美，外焦内嫩，草原风味', 'image_url': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '草原人家', 'region': '内蒙古', 'tags': ['鲜美', '外焦', '草原'], 'data_source': 'mock'},
            {'dish_id': 9088, 'name': '奶茶', 'cuisine_type': '蒙菜', 'price': 15.0, 'rating': 4.1, 'review_count': 234, 'description': '蒙古奶茶，奶香浓郁，茶味醇厚，传统饮品', 'image_url': 'https://images.unsplash.com/photo-1551024506-0bccd828d307?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '蒙古包', 'region': '内蒙古', 'tags': ['奶香', '醇厚', '传统'], 'data_source': 'mock'},
            
            # 西藏菜
            {'dish_id': 9089, 'name': '青稞酒', 'cuisine_type': '藏菜', 'price': 28.0, 'rating': 4.0, 'review_count': 89, 'description': '西藏青稞酒，酒香清淡，回味甘甜，高原特色', 'image_url': 'https://images.unsplash.com/photo-1551024506-0bccd828d307?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '雪域藏餐', 'region': '西藏', 'tags': ['清淡', '甘甜', '高原'], 'data_source': 'mock'},
            {'dish_id': 9090, 'name': '糌粑', 'cuisine_type': '藏菜', 'price': 18.0, 'rating': 3.9, 'review_count': 67, 'description': '藏式糌粑，青稞香味，营养丰富，藏族主食', 'image_url': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '藏香阁', 'region': '西藏', 'tags': ['青稞', '营养', '主食'], 'data_source': 'mock'},
            
            # 福建菜
            {'dish_id': 9091, 'name': '佛跳墙', 'cuisine_type': '闽菜', 'price': 188.0, 'rating': 4.8, 'review_count': 156, 'description': '福建佛跳墙，海味丰富，汤汁醇厚，闽菜之王', 'image_url': 'https://images.unsplash.com/photo-1547592166-23ac45744acd?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '闽南菜馆', 'region': '福建', 'tags': ['海味', '醇厚', '之王'], 'data_source': 'mock'},
            {'dish_id': 9092, 'name': '沙茶面', 'cuisine_type': '闽菜', 'price': 22.0, 'rating': 4.3, 'review_count': 298, 'description': '厦门沙茶面，汤汁香浓，配菜丰富，闽南特色', 'image_url': 'https://images.unsplash.com/photo-1569718212165-3a8278d5f624?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '厦门小吃', 'region': '福建', 'tags': ['香浓', '丰富', '特色'], 'data_source': 'mock'},
            
            # 江西菜
            {'dish_id': 9093, 'name': '白糖糕', 'cuisine_type': '赣菜', 'price': 8.0, 'rating': 4.1, 'review_count': 167, 'description': '江西白糖糕，甜而不腻，口感软糯，传统小吃', 'image_url': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '江西小吃', 'region': '江西', 'tags': ['甜腻', '软糯', '传统'], 'data_source': 'mock'},
            {'dish_id': 9094, 'name': '瓦罐汤', 'cuisine_type': '赣菜', 'price': 25.0, 'rating': 4.2, 'review_count': 234, 'description': '南昌瓦罐汤，汤汁醇厚，营养丰富，文火慢炖', 'image_url': 'https://images.unsplash.com/photo-1547592166-23ac45744acd?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '瓦罐汤馆', 'region': '江西', 'tags': ['醇厚', '营养', '慢炖'], 'data_source': 'mock'},
            
            # 安徽菜
            {'dish_id': 9095, 'name': '毛豆腐', 'cuisine_type': '徽菜', 'price': 20.0, 'rating': 4.0, 'review_count': 145, 'description': '徽州毛豆腐，外焦内嫩，口感独特，徽菜经典', 'image_url': 'https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '徽菜馆', 'region': '安徽', 'tags': ['外焦', '独特', '经典'], 'data_source': 'mock'},
            {'dish_id': 9096, 'name': '臭鳜鱼', 'cuisine_type': '徽菜', 'price': 78.0, 'rating': 4.4, 'review_count': 189, 'description': '徽州臭鳜鱼，鱼肉鲜美，臭香独特，徽菜名品', 'image_url': 'https://images.unsplash.com/photo-1544963150-1c7ebe1fe982?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '徽州人家', 'region': '安徽', 'tags': ['鲜美', '臭香', '名品'], 'data_source': 'mock'},
            
            # 台湾菜
            {'dish_id': 9097, 'name': '牛肉面', 'cuisine_type': '台菜', 'price': 35.0, 'rating': 4.5, 'review_count': 456, 'description': '台湾牛肉面，牛肉软烂，汤汁浓郁，台湾国菜', 'image_url': 'https://images.unsplash.com/photo-1569718212165-3a8278d5f624?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '台湾小吃', 'region': '台湾', 'tags': ['软烂', '浓郁', '国菜'], 'data_source': 'mock'},
            {'dish_id': 9098, 'name': '卤肉饭', 'cuisine_type': '台菜', 'price': 18.0, 'rating': 4.3, 'review_count': 378, 'description': '台式卤肉饭，肥瘦相间，香甜可口，平民美食', 'image_url': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '台湾便当', 'region': '台湾', 'tags': ['肥瘦', '香甜', '平民'], 'data_source': 'mock'},
            {'dish_id': 9099, 'name': '蚵仔煎', 'cuisine_type': '台菜', 'price': 28.0, 'rating': 4.2, 'review_count': 267, 'description': '台湾蚵仔煎，海蛎鲜美，煎蛋香嫩，夜市必吃', 'image_url': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '夜市小摊', 'region': '台湾', 'tags': ['鲜美', '香嫩', '夜市'], 'data_source': 'mock'},
            
            # 香港菜
            {'dish_id': 9100, 'name': '港式茶餐厅奶茶', 'cuisine_type': '港菜', 'price': 12.0, 'rating': 4.4, 'review_count': 567, 'description': '港式奶茶，茶香浓郁，奶味香滑，港人最爱', 'image_url': 'https://images.unsplash.com/photo-1551024506-0bccd828d307?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', 'restaurant_name': '港式茶餐厅', 'region': '香港', 'tags': ['茶香', '香滑', '港人'], 'data_source': 'mock'}
        ]
        
        # 数据库搜索结果 - 暂时禁用数据库查询
        database_dishes = []
        database_total = 0
        
        # TODO: 修复数据库查询的tuple错误后恢复
        # try:
        #     with get_db_connection() as conn:
        #         cursor = conn.cursor()
        #         if query:
        #             search_sql = "SELECT * FROM dishes WHERE is_available = 1 AND name LIKE %s LIMIT %s"
        #             cursor.execute(search_sql, [f"%{query}%", per_page])
        #             database_dishes = cursor.fetchall()
        #             database_total = len(database_dishes)
        # except Exception as db_error:
        #     print(f"数据库查询错误: {db_error}")
        
        # Mock数据搜索 - 支持空查询返回所有数据
        mock_dishes = []
        if query:
            query = query.strip()
            if query:  # 有查询词时进行搜索
                # 通用搜索逻辑 - 搜索所有相关菜品
                query_lower = query.lower()
                for mock_dish in mock_data:
                    # 检查查询词是否在各个文本字段中
                    name_match = query_lower in mock_dish['name'].lower()
                    desc_match = query_lower in mock_dish['description'].lower()
                    cuisine_match = query_lower in mock_dish['cuisine_type'].lower()
                    restaurant_match = query_lower in mock_dish['restaurant_name'].lower()
                    tag_match = any(query_lower in tag.lower() for tag in mock_dish['tags'])
                    
                    if name_match or desc_match or cuisine_match or restaurant_match or tag_match:
                        # 应用筛选条件
                        if cuisine_type and mock_dish['cuisine_type'] != cuisine_type:
                            continue
                        if price_min is not None and mock_dish['price'] < price_min:
                            continue  
                        if price_max is not None and mock_dish['price'] > price_max:
                            continue
                        if rating_min is not None and mock_dish['rating'] < rating_min:
                            continue
                            
                        mock_dishes.append(mock_dish)
            else:
                # 空查询时返回前20个菜品作为示例
                mock_dishes = mock_data[:20]
        else:
            # 无查询参数时返回前20个菜品作为示例
            mock_dishes = mock_data[:20]
        
        # 合并结果
        all_dishes = database_dishes + mock_dishes
        total_combined = database_total + len(mock_dishes)
        
        # 应用排序
        if sort_by == 'rating':
            all_dishes.sort(key=lambda x: x['rating'], reverse=True)
        elif sort_by == 'price_low':
            all_dishes.sort(key=lambda x: float(x['price']))
        elif sort_by == 'price_high':
            all_dishes.sort(key=lambda x: float(x['price']), reverse=True)
        elif sort_by == 'popularity':
            all_dishes.sort(key=lambda x: x.get('monthly_sales', x.get('review_count', 0)), reverse=True)
        
        # 分页处理
        start_idx = (page - 1) * per_page
        end_idx = start_idx + per_page
        final_dishes = all_dishes[start_idx:end_idx]
        
        return jsonify({
            'success': True,
            'items': final_dishes,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total_combined,
                'pages': (total_combined + per_page - 1) // per_page
            },
            'data_sources': {
                'database_count': len(database_dishes),
                'mock_count': len(mock_dishes)
            }
        })
        
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/dishes/categories', methods=['GET'])
def get_dish_categories():
    """获取菜系分类列表"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT DISTINCT cuisine_type FROM dishes WHERE cuisine_type IS NOT NULL ORDER BY cuisine_type")
            categories = cursor.fetchall()
            
            # 提取菜系名称列表
            category_list = [category['cuisine_type'] for category in categories if category['cuisine_type']]
            
            # 如果数据库没有数据，返回默认菜系列表
            if not category_list:
                category_list = ['粤菜', '川菜', '湘菜', '苏菜', '琼菜', '桂菜', '兰菜', '鲁菜', '浙菜', '闽菜', '徽菜']
            
            return jsonify({
                'success': True,
                'data': category_list
            })
            
    except Exception as e:
        # 如果发生错误，返回默认菜系列表
        default_categories = ['粤菜', '川菜', '湘菜', '苏菜', '琼菜', '桂菜', '兰菜', '鲁菜', '浙菜', '闽菜', '徽菜']
        return jsonify({
            'success': True,
            'data': default_categories
        })

# ================================
# API路由 - 评价
# ================================

@app.route('/api/dishes/<int:dish_id>/reviews', methods=['GET'])
def get_dish_reviews(dish_id):
    """获取菜品评价列表"""
    try:
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 10))
        
        with get_db_connection() as conn:
            cursor = conn.cursor()
            
            # 查询总数
            cursor.execute("SELECT COUNT(*) as total FROM reviews WHERE dish_id = %s", (dish_id,))
            total = cursor.fetchone()['total']
            
            # 查询评价数据
            offset = (page - 1) * per_page
            cursor.execute("""
                SELECT r.*, u.username, u.avatar_url
                FROM reviews r
                JOIN users u ON r.user_id = u.user_id
                WHERE r.dish_id = %s
                ORDER BY r.created_at DESC
                LIMIT %s OFFSET %s
            """, (dish_id, per_page, offset))
            
            reviews = cursor.fetchall()
            
            # 处理JSON字段和数据格式
            for review in reviews:
                if review.get('tags'):
                    try:
                        review['tags'] = json.loads(review['tags'])
                    except:
                        review['tags'] = []
                
                # 转换为前端期望的格式
                review['review_id'] = review['id']
                review['user_name'] = review['username']
                review['user_avatar'] = review['avatar_url'] or '/placeholder.svg?height=40&width=40'
                review['comment'] = review['content']
                review['helpful_count'] = 0  # 暂时设为0，后续可以实现点赞功能
            
            return jsonify({
                'success': True,
                'data': reviews,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': total,
                    'pages': (total + per_page - 1) // per_page
                }
            })
            
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/dishes/<int:dish_id>/reviews', methods=['POST'])
def add_dish_review(dish_id):
    """添加菜品评价"""
    try:
        data = request.get_json()
        user_id = data.get('user_id')
        rating = data.get('rating')
        content = data.get('content', '')
        title = data.get('title', '')
        
        if not all([user_id, rating]):
            return jsonify({'success': False, 'message': '缺少必要参数'}), 400
        
        if not (1 <= rating <= 5):
            return jsonify({'success': False, 'message': '评分必须在1-5之间'}), 400
        
        with get_db_connection() as conn:
            cursor = conn.cursor()
            
            # 检查菜品是否存在
            cursor.execute("SELECT dish_id FROM dishes WHERE dish_id = %s", (dish_id,))
            if not cursor.fetchone():
                return jsonify({'success': False, 'message': '菜品不存在'}), 404
            
            # 检查用户是否存在
            cursor.execute("SELECT user_id FROM users WHERE user_id = %s", (user_id,))
            if not cursor.fetchone():
                return jsonify({'success': False, 'message': '用户不存在'}), 404
            
            # 允许用户多次评价同一道菜（移除单次评价限制）
            # 用户可以在不同时间对同一道菜品发表不同的评价体验
            
            # 插入评价
            cursor.execute("""
                INSERT INTO reviews (user_id, dish_id, rating, title, content)
                VALUES (%s, %s, %s, %s, %s)
            """, (user_id, dish_id, rating, title, content))
            
            review_id = cursor.lastrowid
            
            # 更新菜品的平均评分和评价数量
            cursor.execute("""
                UPDATE dishes 
                SET rating = (
                    SELECT AVG(rating) FROM reviews WHERE dish_id = %s
                ),
                review_count = (
                    SELECT COUNT(*) FROM reviews WHERE dish_id = %s
                )
                WHERE dish_id = %s
            """, (dish_id, dish_id, dish_id))
            
            conn.commit()
            
            # 返回新添加的评价信息
            cursor.execute("""
                SELECT r.*, u.username, u.avatar_url
                FROM reviews r
                JOIN users u ON r.user_id = u.user_id
                WHERE r.id = %s
            """, (review_id,))
            
            new_review = cursor.fetchone()
            if new_review:
                new_review['review_id'] = new_review['id']
                new_review['user_name'] = new_review['username']
                new_review['user_avatar'] = new_review['avatar_url'] or '/placeholder.svg?height=40&width=40'
                new_review['comment'] = new_review['content']
                new_review['helpful_count'] = 0
            
            return jsonify({
                'success': True, 
                'message': '评价添加成功',
                'data': new_review
            })
            
    except Exception as e:
        logger.error(f"添加评价失败: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/dishes/<int:dish_id>/reviews/user/<int:user_id>', methods=['GET'])
def get_user_reviews_for_dish(dish_id, user_id):
    """获取用户对某道菜的所有评价"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            
            # 查询用户对该菜品的所有评价，按时间倒序
            cursor.execute("""
                SELECT r.*, u.username, u.avatar_url
                FROM reviews r
                JOIN users u ON r.user_id = u.user_id
                WHERE r.user_id = %s AND r.dish_id = %s
                ORDER BY r.created_at DESC
            """, (user_id, dish_id))
            
            reviews = cursor.fetchall()
            
            # 转换为前端期望的格式
            formatted_reviews = []
            for review in reviews:
                formatted_review = {
                    'review_id': review['id'],
                    'user_name': review['username'],
                    'user_avatar': review['avatar_url'] or '/placeholder.svg?height=40&width=40',
                    'rating': review['rating'],
                    'title': review['title'],
                    'comment': review['content'],
                    'created_at': review['created_at'].isoformat() if review['created_at'] else '',
                    'updated_at': review['updated_at'].isoformat() if review['updated_at'] else '',
                    'helpful_count': 0
                }
                formatted_reviews.append(formatted_review)
                
            return jsonify({
                'success': True,
                'count': len(formatted_reviews),
                'data': formatted_reviews
            })
            
    except Exception as e:
        logger.error(f"获取用户评价失败: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/dishes/<int:dish_id>/reviews/check', methods=['GET'])
def check_user_review(dish_id):
    """检查用户对某道菜的评价情况（兼容旧版本）"""
    try:
        user_id = request.args.get('user_id')
        if not user_id:
            return jsonify({'success': False, 'message': '缺少用户ID'}), 400
        
        with get_db_connection() as conn:
            cursor = conn.cursor()
            
            # 查询用户对该菜品的所有评价
            cursor.execute("""
                SELECT r.*, u.username, u.avatar_url
                FROM reviews r
                JOIN users u ON r.user_id = u.user_id
                WHERE r.user_id = %s AND r.dish_id = %s
                ORDER BY r.created_at DESC
            """, (user_id, dish_id))
            
            reviews = cursor.fetchall()
            
            if reviews:
                # 返回最新的评价作为主要评价
                latest_review = reviews[0]
                latest_review['review_id'] = latest_review['id']
                latest_review['user_name'] = latest_review['username']
                latest_review['user_avatar'] = latest_review['avatar_url'] or '/placeholder.svg?height=40&width=40'
                latest_review['comment'] = latest_review['content']
                latest_review['helpful_count'] = 0
                
                return jsonify({
                    'success': True,
                    'has_reviewed': True,
                    'review_count': len(reviews),
                    'data': latest_review
                })
            else:
                return jsonify({
                    'success': True,
                    'has_reviewed': False,
                    'review_count': 0,
                    'data': None
                })
            
    except Exception as e:
        logger.error(f"检查用户评价失败: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/reviews/<int:review_id>', methods=['PUT'])
def update_review(review_id):
    """更新评价"""
    try:
        data = request.get_json()
        user_id = data.get('user_id')
        rating = data.get('rating')
        content = data.get('content', '')
        title = data.get('title', '')
        
        if not all([user_id, rating]):
            return jsonify({'success': False, 'message': '缺少必要参数'}), 400
        
        if not (1 <= rating <= 5):
            return jsonify({'success': False, 'message': '评分必须在1-5之间'}), 400
        
        with get_db_connection() as conn:
            cursor = conn.cursor()
            
            # 检查评价是否存在且属于当前用户
            cursor.execute("SELECT dish_id FROM reviews WHERE id = %s AND user_id = %s", (review_id, user_id))
            review = cursor.fetchone()
            if not review:
                return jsonify({'success': False, 'message': '评价不存在或无权限修改'}), 404
            
            dish_id = review['dish_id']
            
            # 更新评价
            cursor.execute("""
                UPDATE reviews 
                SET rating = %s, title = %s, content = %s, updated_at = CURRENT_TIMESTAMP
                WHERE id = %s
            """, (rating, title, content, review_id))
            
            # 更新菜品的平均评分
            cursor.execute("""
                UPDATE dishes 
                SET rating = (
                    SELECT AVG(rating) FROM reviews WHERE dish_id = %s
                )
                WHERE dish_id = %s
            """, (dish_id, dish_id))
            
            conn.commit()
            
            # 返回更新后的评价信息
            cursor.execute("""
                SELECT r.*, u.username, u.avatar_url
                FROM reviews r
                JOIN users u ON r.user_id = u.user_id
                WHERE r.id = %s
            """, (review_id,))
            
            updated_review = cursor.fetchone()
            if updated_review:
                updated_review['review_id'] = updated_review['id']
                updated_review['user_name'] = updated_review['username']
                updated_review['user_avatar'] = updated_review['avatar_url'] or '/placeholder.svg?height=40&width=40'
                updated_review['comment'] = updated_review['content']
                updated_review['helpful_count'] = 0
            
            return jsonify({
                'success': True, 
                'message': '评价更新成功',
                'data': updated_review
            })
            
    except Exception as e:
        logger.error(f"更新评价失败: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/reviews/<int:review_id>', methods=['DELETE'])
def delete_review(review_id):
    """删除评价"""
    try:
        user_id = request.args.get('user_id')
        if not user_id:
            return jsonify({'success': False, 'message': '缺少用户ID'}), 400
        
        with get_db_connection() as conn:
            cursor = conn.cursor()
            
            # 检查评价是否存在且属于当前用户
            cursor.execute("SELECT dish_id FROM reviews WHERE id = %s AND user_id = %s", (review_id, user_id))
            review = cursor.fetchone()
            if not review:
                return jsonify({'success': False, 'message': '评价不存在或无权限删除'}), 404
            
            dish_id = review['dish_id']
            
            # 删除评价
            cursor.execute("DELETE FROM reviews WHERE id = %s", (review_id,))
            
            # 更新菜品的平均评分和评价数量
            cursor.execute("""
                UPDATE dishes 
                SET rating = COALESCE((
                    SELECT AVG(rating) FROM reviews WHERE dish_id = %s
                ), 0),
                review_count = (
                    SELECT COUNT(*) FROM reviews WHERE dish_id = %s
                )
                WHERE dish_id = %s
            """, (dish_id, dish_id, dish_id))
            
            conn.commit()
            
            return jsonify({'success': True, 'message': '评价删除成功'})
            
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

# ================================
# API路由 - 餐厅
# ================================

@app.route('/api/restaurants/<restaurant_name>', methods=['GET'])
def get_restaurant_detail(restaurant_name):
    """获取餐厅详情"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM restaurants WHERE name = %s", (restaurant_name,))
            restaurant = cursor.fetchone()
            
            if not restaurant:
                return jsonify({'success': False, 'message': '餐厅不存在'}), 404
            
            # 处理JSON字段
            if restaurant.get('features'):
                try:
                    restaurant['features'] = json.loads(restaurant['features'])
                except:
                    restaurant['features'] = []
            
            # 获取餐厅的菜品
            cursor.execute("""
                SELECT dish_id, name, price, rating, description, image_url, monthly_sales
                FROM dishes 
                WHERE restaurant_name = %s AND is_available = TRUE
                ORDER BY monthly_sales DESC
                LIMIT 20
            """, (restaurant_name,))
            dishes = cursor.fetchall()
            
            restaurant['dishes'] = dishes
            
            return jsonify({'success': True, 'data': restaurant})
            
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/restaurants', methods=['GET'])
def get_restaurants():
    """获取餐厅列表"""
    try:
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))
        cuisine_type = request.args.get('cuisine_type')
        
        with get_db_connection() as conn:
            cursor = conn.cursor()
            
            # 构建查询条件
            where_conditions = ["is_open = TRUE"]
            params = []
            
            if cuisine_type:
                where_conditions.append("cuisine_type = %s")
                params.append(cuisine_type)
            
            where_clause = " AND ".join(where_conditions)
            
            # 查询总数
            count_sql = f"SELECT COUNT(*) as total FROM restaurants WHERE {where_clause}"
            cursor.execute(count_sql, params)
            total = cursor.fetchone()['total']
            
            # 查询数据
            offset = (page - 1) * per_page
            data_sql = f"""
                SELECT * FROM restaurants 
                WHERE {where_clause}
                ORDER BY rating DESC, monthly_orders DESC
                LIMIT %s OFFSET %s
            """
            params.extend([per_page, offset])
            cursor.execute(data_sql, params)
            restaurants = cursor.fetchall()
            
            # 处理JSON字段
            for restaurant in restaurants:
                if restaurant.get('features'):
                    try:
                        restaurant['features'] = json.loads(restaurant['features'])
                    except:
                        restaurant['features'] = []
            
            return jsonify({
                'success': True,
                'data': restaurants,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': total,
                    'pages': (total + per_page - 1) // per_page
                }
            })
            
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

# ================================
# API路由 - 推荐功能
# ================================

@app.route('/api/dishes/<int:dish_id>/similar', methods=['GET'])
def get_similar_dishes(dish_id):
    """获取相似菜品推荐"""
    try:
        limit = int(request.args.get('limit', 6))
        
        with get_db_connection() as conn:
            cursor = conn.cursor()
            
            # 先获取当前菜品信息
            cursor.execute("SELECT * FROM dishes WHERE dish_id = %s", (dish_id,))
            current_dish = cursor.fetchone()
            if not current_dish:
                return jsonify({'success': False, 'message': '菜品不存在'}), 404
            
            # 获取其他菜品计算相似度
            cursor.execute("""
                SELECT dish_id, name, cuisine_type, price, rating, image_url, 
                       restaurant_name, region, tags, description
                FROM dishes 
                WHERE dish_id != %s
                LIMIT 100
            """, (dish_id,))
            
            other_dishes = cursor.fetchall()
            similar_dishes = []
            
            for dish in other_dishes:
                similarity_score = calculate_detailed_similarity(current_dish, dish)
                
                if similarity_score > 0.3:  # 相似度阈值
                    similar_dishes.append({
                        'dish_id': dish['dish_id'],
                        'name': dish['name'],
                        'cuisine_type': dish['cuisine_type'],
                        'price': float(dish['price']) if dish['price'] else 0,
                        'rating': float(dish['rating']) if dish['rating'] else 0,
                        'image_url': dish['image_url'],
                        'restaurant_name': dish['restaurant_name'],
                        'region': dish['region'],
                        'description': dish['description'] or '',
                        'similarity_score': similarity_score
                    })
            
            # 按相似度排序并限制数量
            similar_dishes.sort(key=lambda x: x['similarity_score'], reverse=True)
            similar_dishes = similar_dishes[:limit]
            
            return jsonify({
                'success': True,
                'data': similar_dishes,
                'total': len(similar_dishes)
            })
            
    except Exception as e:
        logger.error(f"获取相似菜品失败: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/dishes/<int:dish_id>/same-restaurant', methods=['GET'])
def get_same_restaurant_dishes(dish_id):
    """获取同店其他菜品推荐"""
    try:
        limit = int(request.args.get('limit', 6))
        
        with get_db_connection() as conn:
            cursor = conn.cursor()
            
            # 先获取当前菜品的餐厅信息
            cursor.execute("SELECT restaurant_name FROM dishes WHERE dish_id = %s", (dish_id,))
            current_dish = cursor.fetchone()
            if not current_dish:
                return jsonify({'success': False, 'message': '菜品不存在'}), 404
            
            restaurant_name = current_dish['restaurant_name']
            
            # 获取同餐厅的其他菜品
            cursor.execute("""
                SELECT dish_id, name, cuisine_type, price, rating, image_url, 
                       restaurant_name, region, tags, description
                FROM dishes 
                WHERE restaurant_name = %s AND dish_id != %s
                ORDER BY rating DESC, price ASC
                LIMIT %s
            """, (restaurant_name, dish_id, limit))
            
            same_restaurant_dishes = cursor.fetchall()
            
            # 格式化返回数据
            formatted_dishes = []
            for dish in same_restaurant_dishes:
                formatted_dishes.append({
                    'dish_id': dish['dish_id'],
                    'name': dish['name'],
                    'cuisine_type': dish['cuisine_type'],
                    'price': float(dish['price']) if dish['price'] else 0,
                    'rating': float(dish['rating']) if dish['rating'] else 0,
                    'image_url': dish['image_url'],
                    'restaurant_name': dish['restaurant_name'],
                    'region': dish['region'],
                    'description': dish['description'] or '',
                    'tags': dish['tags'].split(',') if dish['tags'] else []
                })
            
            return jsonify({
                'success': True,
                'data': formatted_dishes,
                'total': len(formatted_dishes),
                'restaurant_name': restaurant_name
            })
            
    except Exception as e:
        logger.error(f"获取同店菜品失败: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

def calculate_detailed_similarity(dish1, dish2):
    """计算两道菜的详细相似度"""
    try:
        score = 0.0
        
        # 菜系类型相似度 (权重: 30%)
        if dish1['cuisine_type'] == dish2['cuisine_type']:
            score += 0.3
        
        # 价格相似度 (权重: 20%)
        price1 = float(dish1['price']) if dish1['price'] else 0
        price2 = float(dish2['price']) if dish2['price'] else 0
        if price1 > 0 and price2 > 0:
            price_diff = abs(price1 - price2) / max(price1, price2)
            price_similarity = max(0, 1 - price_diff)
            score += 0.2 * price_similarity
        
        # 评分相似度 (权重: 20%)
        rating1 = float(dish1['rating']) if dish1['rating'] else 0
        rating2 = float(dish2['rating']) if dish2['rating'] else 0
        if rating1 > 0 and rating2 > 0:
            rating_diff = abs(rating1 - rating2) / 5.0  # 评分范围1-5
            rating_similarity = max(0, 1 - rating_diff)
            score += 0.2 * rating_similarity
        
        # 标签相似度 (权重: 20%)
        tags1 = set(dish1.get('tags', '').split(',')) if dish1.get('tags') else set()
        tags2 = set(dish2.get('tags', '').split(',')) if dish2.get('tags') else set()
        if tags1 and tags2:
            tag_intersection = len(tags1.intersection(tags2))
            tag_union = len(tags1.union(tags2))
            if tag_union > 0:
                tag_similarity = tag_intersection / tag_union
                score += 0.2 * tag_similarity
        
        # 地区相似度 (权重: 10%)
        if dish1.get('region') == dish2.get('region'):
            score += 0.1
        
        return min(score, 1.0)  # 确保不超过1.0
        
    except Exception as e:
        logger.error(f"计算相似度失败: {e}")
        return 0.0

# ================================
# API路由 - 系统
# ================================

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) as dishes FROM dishes")
            dish_count = cursor.fetchone()['dishes']
            
            cursor.execute("SELECT COUNT(*) as users FROM users")
            user_count = cursor.fetchone()['users']
            
        return jsonify({
            'success': True,
            'message': 'API服务正常',
            'database': 'MySQL连接正常',
            'statistics': {
                'dishes': dish_count,
                'users': user_count
            },
            'timestamp': datetime.datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': 'API服务异常',
            'error': str(e),
            'timestamp': datetime.datetime.now().isoformat()
        }), 500

@app.route('/api/init-data', methods=['POST'])
def init_data():
    """初始化示例数据"""
    try:
        success = init_sample_data()
        if success:
            return jsonify({'success': True, 'message': '示例数据初始化成功'})
        else:
            return jsonify({'success': False, 'message': '示例数据初始化失败'}), 500
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

# ================================
# 错误处理
# ================================

@app.errorhandler(404)
def not_found(error):
    return jsonify({'success': False, 'message': '接口不存在'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'success': False, 'message': '服务器内部错误'}), 500

# ================================
# 启动应用
# ================================

if __name__ == '__main__':
    print("=" * 50)
    print("美食推荐系统 - 统一后端服务")
    print("=" * 50)
    
    # 初始化数据库
    print("正在初始化数据库...")
    if init_database():
        print("数据库初始化完成")
        
        # 初始化示例数据
        print("正在检查示例数据...")
        init_sample_data()
        
        print("\n服务信息:")
        print("- 数据库: MySQL")
        print("- 访问地址: http://localhost:5000")
        print("- 健康检查: http://localhost:5000/api/health")
        print("- 管理员账号: admin / 123456")
        print("- 普通用户: user1 / 123456")
        print("\n启动服务...")
        
        app.run(debug=True, host='0.0.0.0', port=5000)
    else:
        print("数据库初始化失败，请检查MySQL服务是否启动")
        print("请确保MySQL服务正在运行，用户名: root, 密码: 123456789")