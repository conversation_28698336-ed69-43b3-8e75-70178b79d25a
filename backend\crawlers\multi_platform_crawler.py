#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多平台美食数据爬取器
支持美团、大众点评、下厨房等多个平台
"""

import asyncio
import aiohttp
import requests
import json
import time
import random
import re
import os
from datetime import datetime
from typing import List, Dict, Any, Optional
from urllib.parse import urljoin, quote
import pymysql
import logging
from concurrent.futures import ThreadPoolExecutor
from fake_useragent import UserAgent
import threading
from queue import Queue

from food_crawler import BaseCrawler, DishInfo, RestaurantInfo, DatabaseManager

logger = logging.getLogger(__name__)

class MeituanCrawler(BaseCrawler):
    """美团爬虫"""
    
    def __init__(self):
        super().__init__("美团")
        self.base_url = "https://www.meituan.com"
        self.api_base = "https://apimobile.meituan.com"
        
    def search_restaurants(self, city: str, keyword: str = "", page: int = 0) -> List[Dict]:
        """搜索餐厅"""
        # 美团搜索API (需要适配实际API)
        search_url = f"{self.api_base}/group/v1/poi/pcsearch"
        
        params = {
            'cityName': city,
            'cateId': 1,  # 美食分类
            'areaId': 0,
            'sort': '',
            'keyword': keyword,
            'page': page
        }
        
        headers = {
            **self.default_headers,
            'Referer': f'{self.base_url}/{city}/meishi/'
        }
        
        response = self.get_with_retry(search_url, params=params, headers=headers)
        if not response:
            return []
        
        try:
            data = response.json()
            if data.get('code') == 0:
                return data.get('data', {}).get('poilist', [])
        except Exception as e:
            logger.error(f"解析美团搜索结果失败: {e}")
        
        return []
    
    def get_restaurant_detail(self, poi_id: str) -> Optional[Dict]:
        """获取餐厅详细信息"""
        detail_url = f"{self.api_base}/group/v1/poi/detail"
        params = {'poiId': poi_id}
        
        response = self.get_with_retry(detail_url, params=params)
        if not response:
            return None
        
        try:
            data = response.json()
            if data.get('code') == 0:
                return data.get('data', {})
        except Exception as e:
            logger.error(f"获取餐厅详情失败: {e}")
        
        return None
    
    def extract_restaurant_info(self, poi_data: Dict, region: str, city: str) -> Optional[RestaurantInfo]:
        """提取餐厅信息"""
        try:
            return RestaurantInfo(
                name=poi_data.get('title', ''),
                region=region,
                city=city,
                address=poi_data.get('address', ''),
                phone=poi_data.get('phone', ''),
                latitude=float(poi_data.get('lat', 0)),
                longitude=float(poi_data.get('lng', 0)),
                avg_price=float(poi_data.get('avgprice', 0)),
                rating=float(poi_data.get('avgscore', 0)),
                review_count=int(poi_data.get('allcommentnum', 0)),
                restaurant_type=poi_data.get('categoryname', ''),
                source_platform=self.platform_name,
                source_url=f"{self.base_url}/shop/{poi_data.get('poiid', '')}"
            )
        except Exception as e:
            logger.error(f"提取餐厅信息失败: {e}")
            return None
    
    def crawl_region_food(self, province: str, city: str, max_pages: int = 10) -> List[DishInfo]:
        """爬取指定地区的美食数据"""
        dishes = []
        restaurants = []
        
        # 搜索餐厅
        for page in range(max_pages):
            pois = self.search_restaurants(city, "", page)
            if not pois:
                break
            
            for poi in pois:
                # 提取餐厅信息
                restaurant = self.extract_restaurant_info(poi, province, city)
                if restaurant:
                    restaurants.append(restaurant)
                    self.db.save_restaurant(restaurant)
                
                # 从餐厅信息推断菜品
                inferred_dishes = self.infer_dishes_from_restaurant(poi, province, city)
                dishes.extend(inferred_dishes)
                
                for dish in inferred_dishes:
                    self.db.save_dish(dish)
            
            time.sleep(random.uniform(1, 2))
        
        return dishes
    
    def infer_dishes_from_restaurant(self, poi_data: Dict, province: str, city: str) -> List[DishInfo]:
        """从餐厅信息推断可能的菜品"""
        dishes = []
        restaurant_name = poi_data.get('title', '')
        category = poi_data.get('categoryname', '')
        
        # 根据餐厅类型推断常见菜品
        dish_mappings = {
            '川菜': ['宫保鸡丁', '麻婆豆腐', '水煮鱼', '回锅肉', '口水鸡'],
            '粤菜': ['白切鸡', '叉烧包', '虾饺', '烧鹅', '清蒸鲈鱼'],
            '湘菜': ['剁椒鱼头', '口味虾', '麻辣小龙虾', '湘式小炒肉'],
            '火锅': ['麻辣火锅', '鸳鸯火锅', '清汤火锅'],
            '烧烤': ['羊肉串', '烤鸡翅', '烤茄子', '烤韭菜'],
            '海鲜': ['白灼虾', '蒸扇贝', '麻辣小龙虾', '清蒸石斑鱼']
        }
        
        possible_dishes = []
        for cuisine_type, dish_list in dish_mappings.items():
            if cuisine_type in category:
                possible_dishes = dish_list
                break
        
        # 创建菜品信息
        for dish_name in possible_dishes:
            dish = DishInfo(
                name=dish_name,
                restaurant_name=restaurant_name,
                region=province,
                city=city,
                cuisine_type=category,
                rating=float(poi_data.get('avgscore', 0)),
                source_platform=self.platform_name,
                source_url=f"{self.base_url}/shop/{poi_data.get('poiid', '')}"
            )
            dishes.append(dish)
        
        return dishes

class DianpingCrawler(BaseCrawler):
    """大众点评爬虫"""
    
    def __init__(self):
        super().__init__("大众点评")
        self.base_url = "https://www.dianping.com"
        
    def search_shops(self, city: str, category: str = "美食", page: int = 1) -> List[Dict]:
        """搜索商户"""
        # 构建搜索URL
        search_url = f"{self.base_url}/{city}/ch10"  # 美食频道
        
        params = {
            'categoryid': 10,  # 美食分类ID
            'regionid': -1,
            'page': page
        }
        
        headers = {
            **self.default_headers,
            'Referer': f'{self.base_url}/{city}/'
        }
        
        response = self.get_with_retry(search_url, params=params, headers=headers)
        if not response:
            return []
        
        # 解析HTML页面获取商户信息
        return self.parse_shop_list(response.text)
    
    def parse_shop_list(self, html: str) -> List[Dict]:
        """解析商户列表页面"""
        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html, 'html.parser')
            
            shops = []
            shop_items = soup.find_all('div', class_='shop-item')
            
            for item in shop_items:
                shop_data = self.parse_shop_item(item)
                if shop_data:
                    shops.append(shop_data)
            
            return shops
        except Exception as e:
            logger.error(f"解析大众点评商户列表失败: {e}")
            return []
    
    def parse_shop_item(self, item) -> Optional[Dict]:
        """解析单个商户项目"""
        try:
            # 商户名称
            name_elem = item.find('h4')
            name = name_elem.get_text().strip() if name_elem else ''
            
            # 商户链接
            link_elem = item.find('a')
            shop_url = urljoin(self.base_url, link_elem.get('href', '')) if link_elem else ''
            
            # 评分
            rating_elem = item.find('span', class_='comment-score')
            rating = 0.0
            if rating_elem:
                rating_text = rating_elem.get_text().strip()
                rating_match = re.search(r'(\d+\.?\d*)', rating_text)
                if rating_match:
                    rating = float(rating_match.group(1))
            
            # 评论数
            review_elem = item.find('a', class_='review-num')
            review_count = 0
            if review_elem:
                review_text = review_elem.get_text()
                review_match = re.search(r'(\d+)', review_text)
                if review_match:
                    review_count = int(review_match.group(1))
            
            # 地址
            address_elem = item.find('span', class_='addr')
            address = address_elem.get_text().strip() if address_elem else ''
            
            # 菜系类型
            category_elem = item.find('span', class_='tag')
            category = category_elem.get_text().strip() if category_elem else ''
            
            return {
                'name': name,
                'url': shop_url,
                'rating': rating,
                'review_count': review_count,
                'address': address,
                'category': category
            }
        except Exception as e:
            logger.error(f"解析商户项目失败: {e}")
            return None
    
    def extract_restaurant_info(self, shop_data: Dict, region: str, city: str) -> Optional[RestaurantInfo]:
        """提取餐厅信息"""
        try:
            return RestaurantInfo(
                name=shop_data.get('name', ''),
                region=region,
                city=city,
                address=shop_data.get('address', ''),
                rating=shop_data.get('rating', 0.0),
                review_count=shop_data.get('review_count', 0),
                restaurant_type=shop_data.get('category', ''),
                source_platform=self.platform_name,
                source_url=shop_data.get('url', '')
            )
        except Exception as e:
            logger.error(f"提取餐厅信息失败: {e}")
            return None
    
    def crawl_region_food(self, province: str, city: str, max_pages: int = 10) -> List[DishInfo]:
        """爬取指定地区的美食数据"""
        dishes = []
        
        for page in range(1, max_pages + 1):
            shops = self.search_shops(city, "美食", page)
            if not shops:
                break
            
            for shop in shops:
                # 保存餐厅信息
                restaurant = self.extract_restaurant_info(shop, province, city)
                if restaurant:
                    self.db.save_restaurant(restaurant)
                
                # 从餐厅推断菜品
                inferred_dishes = self.infer_dishes_from_shop(shop, province, city)
                dishes.extend(inferred_dishes)
                
                for dish in inferred_dishes:
                    self.db.save_dish(dish)
            
            time.sleep(random.uniform(1, 2))
        
        return dishes
    
    def infer_dishes_from_shop(self, shop_data: Dict, province: str, city: str) -> List[DishInfo]:
        """从商户信息推断菜品"""
        dishes = []
        category = shop_data.get('category', '')
        restaurant_name = shop_data.get('name', '')
        
        # 根据不同菜系推断常见菜品
        regional_dishes = {
            '广东': ['白切鸡', '叉烧包', '虾饺', '烧鹅', '清蒸鲈鱼', '广式腊肠', '艇仔粥'],
            '广西': ['桂林米粉', '螺蛳粉', '老友粉', '酸野', '柠檬鸭', '白切鸡', '竹筒饭'],
            '海南': ['海南鸡饭', '文昌鸡', '加积鸭', '东山羊', '和乐蟹', '椰子饭', '清补凉']
        }
        
        cuisine_dishes = {
            '川菜': ['宫保鸡丁', '麻婆豆腐', '水煮鱼', '回锅肉', '辣子鸡'],
            '粤菜': ['白切鸡', '叉烧包', '虾饺', '烧鹅', '清蒸鲈鱼'],
            '湘菜': ['剁椒鱼头', '口味虾', '麻辣小龙虾', '湘式小炒肉'],
            '火锅': ['麻辣火锅', '鸳鸯火锅', '清汤火锅', '菌汤火锅'],
            '烧烤': ['羊肉串', '烤鸡翅', '烤茄子', '烤韭菜', '烤生蚝'],
            '海鲜': ['白灼虾', '蒸扇贝', '麻辣小龙虾', '清蒸石斑鱼', '椒盐皮皮虾']
        }
        
        # 优先使用地区特色菜
        possible_dishes = regional_dishes.get(province, [])
        
        # 然后根据菜系类型添加菜品
        for cuisine_type, dish_list in cuisine_dishes.items():
            if cuisine_type in category:
                possible_dishes.extend(dish_list)
                break
        
        # 如果没有匹配到，使用默认菜品
        if not possible_dishes:
            possible_dishes = ['招牌菜', '特色菜', '人气菜品']
        
        for dish_name in possible_dishes[:5]:  # 限制每个餐厅最多5道菜
            dish = DishInfo(
                name=dish_name,
                restaurant_name=restaurant_name,
                region=province,
                city=city,
                cuisine_type=category,
                rating=shop_data.get('rating', 0.0),
                review_count=shop_data.get('review_count', 0),
                description=f"{restaurant_name}的{dish_name}，{category}经典菜品",
                source_platform=self.platform_name,
                source_url=shop_data.get('url', ''),
                cultural_background=self.get_cultural_background(province, dish_name)
            )
            dishes.append(dish)
        
        return dishes
    
    def get_cultural_background(self, province: str, dish_name: str) -> str:
        """获取菜品文化背景"""
        backgrounds = {
            '广东': f"{dish_name}体现了粤菜文化的精致和对食材原味的追求，是岭南饮食文化的重要组成部分。",
            '广西': f"{dish_name}融合了汉族和壮族等少数民族的饮食文化，具有浓郁的桂菜特色。",
            '海南': f"{dish_name}体现了海南独特的海岛饮食文化，融合了热带风味和海鲜特色。"
        }
        return backgrounds.get(province, f"{dish_name}体现了中华饮食文化的丰富多样性。")

class EnhancedFoodCrawlerManager:
    """增强版美食爬虫管理器"""
    
    def __init__(self):
        self.crawlers = {
            '美团': MeituanCrawler(),
            '大众点评': DianpingCrawler(),
            # '下厨房': XiaChuFangCrawler(),  # 从之前的模块导入
        }
        self.db = DatabaseManager()
        
        # 确保数据库表存在
        try:
            sql_file = os.path.join(os.path.dirname(__file__), 'create_food_tables.sql')
            if os.path.exists(sql_file):
                self.db.execute_sql_file(sql_file)
        except Exception as e:
            logger.error(f"初始化数据库失败: {e}")
    
    def crawl_all_target_regions(self, max_pages_per_platform: int = 5):
        """爬取所有目标地区的美食数据"""
        # 目标地区 - 广东、广西、海南的主要城市
        target_regions = [
            # 广东省
            ('广东', '广州'), ('广东', '深圳'), ('广东', '珠海'), 
            ('广东', '佛山'), ('广东', '东莞'), ('广东', '中山'),
            ('广东', '江门'), ('广东', '湛江'), ('广东', '茂名'),
            ('广东', '肇庆'), ('广东', '惠州'), ('广东', '梅州'),
            ('广东', '汕头'), ('广东', '潮州'), ('广东', '揭阳'),
            
            # 广西壮族自治区
            ('广西', '南宁'), ('广西', '柳州'), ('广西', '桂林'),
            ('广西', '梧州'), ('广西', '北海'), ('广西', '防城港'),
            ('广西', '钦州'), ('广西', '贵港'), ('广西', '玉林'),
            ('广西', '百色'), ('广西', '贺州'), ('广西', '河池'),
            
            # 海南省
            ('海南', '海口'), ('海南', '三亚'), ('海南', '儋州'),
            ('海南', '琼海'), ('海南', '文昌'), ('海南', '万宁'),
            ('海南', '五指山'), ('海南', '东方')
        ]
        
        total_dishes = 0
        total_restaurants = 0
        
        for province, city in target_regions:
            logger.info(f"开始爬取 {province} {city} 的美食数据")
            
            region_dishes = 0
            region_restaurants = 0
            
            for platform_name, crawler in self.crawlers.items():
                try:
                    logger.info(f"使用 {platform_name} 爬取 {province} {city}")
                    dishes = crawler.crawl_region_food(province, city, max_pages_per_platform)
                    
                    region_dishes += len(dishes)
                    total_dishes += len(dishes)
                    
                    logger.info(f"{platform_name} - {province} {city}: 爬取到 {len(dishes)} 道菜品")
                    
                    # 添加延迟避免被封
                    time.sleep(random.uniform(2, 5))
                    
                except Exception as e:
                    logger.error(f"爬取 {platform_name} - {province} {city} 失败: {e}")
                    continue
            
            logger.info(f"{province} {city} 总计: {region_dishes} 道菜品")
            
            # 每个城市之间的延迟
            time.sleep(random.uniform(3, 8))
        
        logger.info(f"爬取完成！总计: {total_dishes} 道菜品, {total_restaurants} 家餐厅")
        return total_dishes, total_restaurants
    
    def get_crawl_statistics(self) -> Dict[str, Any]:
        """获取爬取统计信息"""
        try:
            cursor = self.db.connection.cursor()
            
            # 菜品统计
            cursor.execute("SELECT COUNT(*) FROM dishes_extended")
            total_dishes = cursor.fetchone()[0]
            
            cursor.execute("SELECT source_platform, COUNT(*) FROM dishes_extended GROUP BY source_platform")
            platform_dishes = dict(cursor.fetchall())
            
            cursor.execute("SELECT region_id, COUNT(*) FROM dishes_extended WHERE region_id IS NOT NULL GROUP BY region_id")
            region_dishes = cursor.fetchall()
            
            # 餐厅统计
            cursor.execute("SELECT COUNT(*) FROM restaurants")
            total_restaurants = cursor.fetchone()[0]
            
            cursor.close()
            
            return {
                'total_dishes': total_dishes,
                'total_restaurants': total_restaurants,
                'platform_distribution': platform_dishes,
                'region_distribution': dict(region_dishes)
            }
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {}

def main():
    """主函数"""
    try:
        # 安装必要依赖
        logger.info("检查并安装必要依赖...")
        os.system("pip install beautifulsoup4 fake-useragent pymysql aiohttp")
        
        # 创建爬虫管理器
        manager = EnhancedFoodCrawlerManager()
        
        logger.info("=" * 60)
        logger.info("多平台美食数据爬取系统启动")
        logger.info("目标区域: 广东、广西、海南各主要城市")
        logger.info("数据源: 美团、大众点评等多平台")
        logger.info("=" * 60)
        
        # 开始爬取
        start_time = datetime.now()
        total_dishes, total_restaurants = manager.crawl_all_target_regions(max_pages_per_platform=3)
        end_time = datetime.now()
        
        # 输出统计信息
        duration = end_time - start_time
        logger.info("=" * 60)
        logger.info("爬取完成！")
        logger.info(f"耗时: {duration}")
        logger.info(f"总计爬取: {total_dishes} 道菜品, {total_restaurants} 家餐厅")
        logger.info("=" * 60)
        
        # 详细统计
        stats = manager.get_crawl_statistics()
        if stats:
            logger.info("详细统计信息:")
            logger.info(f"数据库中菜品总数: {stats.get('total_dishes', 0)}")
            logger.info(f"数据库中餐厅总数: {stats.get('total_restaurants', 0)}")
            logger.info("平台分布:")
            for platform, count in stats.get('platform_distribution', {}).items():
                logger.info(f"  {platform}: {count} 道菜品")
        
    except Exception as e:
        logger.error(f"爬取过程出错: {e}", exc_info=True)

if __name__ == "__main__":
    main()