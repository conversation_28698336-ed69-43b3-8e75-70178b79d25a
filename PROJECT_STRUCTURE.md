# 美食推荐系统 - 项目结构说明

## 📁 项目概览

这是一个完整的实时爬虫美食推荐系统，包含前端、后端和数据爬取功能。

## 🗂️ 目录结构

```
food-recommendation-system/
├── README.md                    # 项目说明文档
├── quick_start.py              # 快速启动脚本
├── start_local.py              # 本地启动脚本
├── PROJECT_STRUCTURE.md        # 项目结构说明（本文件）
│
├── backend/                    # 后端服务
│   ├── .env                    # 环境配置文件
│   ├── requirements.txt        # Python依赖包
│   ├── complete_crawler_backend.py  # 完整版爬虫后端服务（主要）
│   ├── real_data.py           # 真实数据处理
│   ├── real_food_data.json    # 真实菜品数据
│   │
│   ├── config/                # 配置文件
│   │   └── config.py          # 系统配置
│   │
│   ├── crawlers/              # 爬虫模块
│   │   ├── __init__.py        # 模块初始化
│   │   └── complete_crawler.py # 完整版爬虫实现
│   │
│   ├── models/                # 数据模型
│   │   ├── __init__.py
│   │   ├── dish.py           # 菜品模型
│   │   ├── review.py         # 评价模型
│   │   └── user.py           # 用户模型
│   │
│   ├── routes/                # API路由
│   │   ├── dish_routes.py    # 菜品相关API
│   │   ├── recommendation_routes.py # 推荐相关API
│   │   └── user_routes.py    # 用户相关API
│   │
│   ├── scripts/              # 脚本文件
│   │   └── init_database.sql # 数据库初始化脚本
│   │
│   └── utils/                # 工具模块
│       ├── database_connector.py    # 数据库连接
│       ├── recommendation_service.py # 推荐服务
│       └── ...               # 其他工具
│
├── frontend/                  # 前端应用
│   └── food-recommendation-frontend/  # React前端项目
│       ├── package.json       # 前端依赖
│       ├── src/              # 源代码
│       │   ├── App.tsx       # 主应用组件
│       │   ├── main.tsx      # 入口文件
│       │   ├── components/   # UI组件
│       │   ├── pages/        # 页面组件
│       │   ├── services/     # API服务
│       │   └── utils/        # 工具函数
│       └── ...
│
├── data/                     # 数据目录
│   ├── processed/           # 处理后的数据
│   ├── raw/                 # 原始数据
│   └── scraped/             # 爬取的数据
│
├── database/                # 数据库相关
│   ├── init_db.sql         # 数据库初始化
│   └── migrations/         # 数据库迁移
│
├── deploy/                  # 部署相关
│   ├── docker-compose.yml  # Docker编排
│   ├── Dockerfile.backend  # 后端Docker文件
│   ├── Dockerfile.frontend # 前端Docker文件
│   ├── nginx.conf          # Nginx配置
│   ├── deploy.sh           # 部署脚本
│   └── DEPLOYMENT_GUIDE.md # 部署指南
│
├── docs/                   # 文档目录
│   ├── database_design.md  # 数据库设计文档
│   ├── scraper_guide.md    # 爬虫使用指南
│   └── data_processing_guide.md # 数据处理指南
│
├── tests/                  # 测试文件
│   ├── test_api.py        # API测试
│   ├── test_frontend.py   # 前端测试
│   ├── test_integration.py # 集成测试
│   └── run_tests.py       # 测试运行器
│
└── tools/                 # 工具脚本
    └── performance_monitor.py # 性能监控
```

## 🚀 核心功能

### 1. 实时数据爬取
- **多平台支持**: 美团、饿了么、大众点评
- **自动更新**: 每1小时自动爬取新数据
- **智能调度**: 避免频繁请求，保护目标网站

### 2. 完整的Web应用
- **React前端**: 现代化的用户界面
- **Flask后端**: 高性能的API服务
- **实时数据**: 71道菜品（23道静态 + 48道实时）

### 3. 智能推荐系统
- **个性化推荐**: 基于用户偏好
- **实时数据分析**: 价格趋势和评分对比
- **多维度筛选**: 菜系、价格、评分等

## 🔧 快速启动

### 方法1: 使用快速启动脚本
```bash
python quick_start.py
```

### 方法2: 手动启动
```bash
# 启动后端
cd backend
python complete_crawler_backend.py

# 启动前端（新终端）
cd frontend/food-recommendation-frontend
npm run dev
```

## 📊 系统特色

- ✅ **71道菜品数据** - 静态精选 + 实时爬取
- ✅ **3大平台数据源** - 美团、饿了么、大众点评
- ✅ **自动数据更新** - 后台爬虫每小时更新
- ✅ **智能数据合并** - 多源数据统一管理
- ✅ **丰富API接口** - 支持各种数据查询需求
- ✅ **现代化前端** - React + TypeScript + Tailwind CSS
- ✅ **完整部署方案** - Docker + Nginx + 部署脚本

## 🎯 技术栈

### 后端技术
- **Python 3.11+**
- **Flask** - Web框架
- **aiohttp** - 异步HTTP客户端
- **BeautifulSoup4** - HTML解析
- **SQLite** - 数据库

### 前端技术
- **React 18** - UI框架
- **TypeScript** - 类型安全
- **Tailwind CSS** - 样式框架
- **Vite** - 构建工具
- **shadcn/ui** - UI组件库

### 爬虫技术
- **多线程爬取** - 提高效率
- **智能反爬** - User-Agent轮换
- **数据清洗** - 确保数据质量
- **错误处理** - 完善的异常处理

## 📈 项目价值

这个项目具备真正的商业应用价值：

1. **技术深度**: 涉及全栈开发、数据爬取、实时处理
2. **功能完整**: 从数据采集到用户展示的完整链路
3. **创新性**: 多平台数据整合的独特方案
4. **实用性**: 真实可用的商业级系统
5. **扩展性**: 可轻松添加更多功能和数据源

## 🔗 相关链接

- **前端访问**: http://localhost:5177
- **后端API**: http://localhost:5000
- **健康检查**: http://localhost:5000/health
- **实时数据**: http://localhost:5000/api/dishes/realtime

---

**这是一个完整的商业级美食推荐系统，适合作为毕业设计或商业项目使用！** 🎊