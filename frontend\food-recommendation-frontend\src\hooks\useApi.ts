import { useState, useEffect } from 'react';

interface Dish {
  dish_id: number;
  name: string;
  cuisine_type: string;
  price: number;
  rating: number;
  review_count: number;
  description: string;
  image_url: string;
  restaurant_name: string;
  region: string;
  recommendation_score?: number;
  recommendation_reason?: string;
}

interface ApiResponse<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
}

// 获取热门菜品
export const usePopularDishes = (
  limit?: number,
  cuisine?: string,
  maxItems: number = 12
): ApiResponse<Dish[]> => {
  const [data, setData] = useState<Dish[] | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPopularDishes = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const response = await fetch('http://localhost:5000/api/dishes');
        if (response.ok) {
          const result = await response.json();
          let dishes = result.dishes || result || [];
          
          // 应用菜系筛选
          if (cuisine) {
            dishes = dishes.filter((dish: any) => dish.cuisine === cuisine || dish.cuisine_type === cuisine);
          }
          
          // 限制数量
          dishes = dishes.slice(0, maxItems);
          
          // 转换数据格式
          const formattedDishes = dishes.map((dish: any) => ({
            dish_id: dish.id || dish.dish_id,
            name: dish.name,
            cuisine_type: dish.cuisine || dish.cuisine_type,
            price: dish.price,
            rating: dish.rating,
            review_count: dish.reviews?.length || 0,
            description: dish.description,
            image_url: dish.image || dish.image_url,
            restaurant_name: dish.restaurant_name || '美食餐厅',
            region: dish.region
          }));
          
          setData(formattedDishes);
        } else {
          throw new Error('获取菜品数据失败');
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : '未知错误');
        // 使用模拟数据作为后备
        setData([
          {
            dish_id: 1,
            name: '白切鸡',
            cuisine_type: '粤菜',
            price: 35.0,
            rating: 4.5,
            review_count: 128,
            description: '传统粤式白切鸡，肉质鲜嫩，配以特制蘸料',
            image_url: 'https://images.unsplash.com/photo-1604503468506-a8da13d82791?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
            restaurant_name: '粤味轩',
            region: '广东'
          },
          {
            dish_id: 2,
            name: '桂林米粉',
            cuisine_type: '桂菜',
            price: 25.0,
            rating: 4.2,
            review_count: 89,
            description: '桂林特色米粉，汤鲜味美，配菜丰富',
            image_url: 'https://images.unsplash.com/photo-1569718212165-3a8278d5f624?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
            restaurant_name: '桂林人家',
            region: '广西'
          },
          {
            dish_id: 3,
            name: '海南鸡饭',
            cuisine_type: '琼菜',
            price: 30.0,
            rating: 4.3,
            review_count: 156,
            description: '海南风味鸡饭，香米配白切鸡，口感丰富',
            image_url: 'https://images.unsplash.com/photo-1512058564366-18510be2db19?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
            restaurant_name: '海南风情',
            region: '海南'
          }
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchPopularDishes();
  }, [cuisine, maxItems]);

  return { data, loading, error };
};

// 获取用户推荐
export const useUserRecommendations = (
  userId: number,
  limit: number = 6
): ApiResponse<Dish[]> => {
  const [data, setData] = useState<Dish[] | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchRecommendations = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const response = await fetch(`http://localhost:5000/api/recommendations?user_id=${userId}&limit=${limit}`);
        if (response.ok) {
          const result = await response.json();
          let recommendations = result.dishes || result || [];
          
          // 转换数据格式并添加推荐原因
          const formattedRecommendations = recommendations.slice(0, limit).map((dish: any, index: number) => ({
            dish_id: dish.id || dish.dish_id,
            name: dish.name,
            cuisine_type: dish.cuisine || dish.cuisine_type,
            price: dish.price,
            rating: dish.rating,
            review_count: dish.reviews?.length || 0,
            description: dish.description,
            image_url: dish.image || dish.image_url,
            restaurant_name: dish.restaurant_name || '美食餐厅',
            region: dish.region,
            recommendation_score: dish.recommendation_score || 0.9,
            recommendation_reason: dish.recommendation_reason || ['高评分推荐', '热门选择', '地方特色'][index % 3]
          }));
          
          setData(formattedRecommendations);
        } else {
          throw new Error('获取推荐数据失败');
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : '未知错误');
        // 使用模拟推荐数据作为后备
        setData([
          {
            dish_id: 1,
            name: '白切鸡',
            cuisine_type: '粤菜',
            price: 35.0,
            rating: 4.5,
            review_count: 128,
            description: '传统粤式白切鸡，肉质鲜嫩，配以特制蘸料',
            image_url: 'https://images.unsplash.com/photo-1604503468506-a8da13d82791?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
            restaurant_name: '粤味轩',
            region: '广东',
            recommendation_reason: '高评分推荐'
          },
          {
            dish_id: 2,
            name: '桂林米粉',
            cuisine_type: '桂菜',
            price: 25.0,
            rating: 4.2,
            review_count: 89,
            description: '桂林特色米粉，汤鲜味美，配菜丰富',
            image_url: 'https://images.unsplash.com/photo-1569718212165-3a8278d5f624?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
            restaurant_name: '桂林人家',
            region: '广西',
            recommendation_reason: '地方特色'
          },
          {
            dish_id: 3,
            name: '海南鸡饭',
            cuisine_type: '琼菜',
            price: 30.0,
            rating: 4.3,
            review_count: 156,
            description: '海南风味鸡饭，香米配白切鸡，口感丰富',
            image_url: 'https://images.unsplash.com/photo-1512058564366-18510be2db19?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
            restaurant_name: '海南风情',
            region: '海南',
            recommendation_reason: '热门选择'
          }
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchRecommendations();
  }, [userId, limit]);

  return { data, loading, error };
};

// 菜品操作相关
export const useDishActions = () => {
  const updateInteraction = async (interaction: {
    user_id: number;
    dish_id: number;
    interaction_type: string;
  }) => {
    try {
      const response = await fetch('http://localhost:5000/api/interactions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(interaction),
      });
      
      if (!response.ok) {
        throw new Error('记录交互失败');
      }
      
      return await response.json();
    } catch (error) {
      console.error('记录用户交互失败:', error);
      // 静默失败，不影响用户体验
    }
  };

  return { updateInteraction };
};