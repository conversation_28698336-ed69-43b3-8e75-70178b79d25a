import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { getDishImageUrl, handleImageError } from '@/utils/imageUtils';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Star, Heart, MapPin, Clock, Users, ThumbsUp, Share2, ArrowLeft } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { userApi, dishApi } from '@/services/api';
import LoginModal from '@/components/LoginModal';

interface Dish {
  id?: number;
  dish_id?: number;
  name: string;
  cuisine?: string;
  cuisine_type?: string;
  price: number;
  rating: number;
  review_count?: number;
  description: string;
  image?: string;
  image_url?: string;
  restaurant_name?: string;
  region: string;
  tags: string[];
  ingredients: string[];
  nutrition: {
    calories: number;
    protein: number;
    fat: number;
    carbs: number;
  };
  preparation_time?: string;
  spice_level?: string;
  reviews?: any[];
}

interface Review {
  review_id: number;
  user_name: string;
  user_avatar: string;
  rating: number;
  comment: string;
  created_at: string;
  helpful_count: number;
  images?: string[];
}

interface SimilarDish {
  dish_id: number;
  name: string;
  price: number;
  rating: number;
  image_url: string;
  similarity_score: number;
}

const DishDetailPage: React.FC = () => {
  const navigate = useNavigate();
  const { dishId } = useParams<{ dishId: string }>();
  const { user, isAuthenticated, logout } = useAuth();
  const [dish, setDish] = useState<Dish | null>(null);
  const [reviews, setReviews] = useState<Review[]>([]);
  const [similarDishes, setSimilarDishes] = useState<SimilarDish[]>([]);
  const [loading, setLoading] = useState(true);
  const [isFavorited, setIsFavorited] = useState(false);
  const [favoriteLoading, setFavoriteLoading] = useState(false);
  const [newReview, setNewReview] = useState('');
  const [newRating, setNewRating] = useState(5);
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [userReviews, setUserReviews] = useState<any[]>([]);
  const [isEditingReview, setIsEditingReview] = useState<number | null>(null);
  const [showAllUserReviews, setShowAllUserReviews] = useState<boolean>(false);
  const [sameRestaurantDishes, setSameRestaurantDishes] = useState<any[]>([]);

  // 返回按钮处理函数
  const handleGoBack = () => {
    navigate(-1); // 返回上一页
  };

  // 模拟菜品详情数据
  const mockDish: Dish = {
    dish_id: 1,
    name: '白切鸡',
    cuisine_type: '粤菜',
    price: 35.0,
    rating: 4.5,
    review_count: 128,
    description: '白切鸡是粤菜系鸡肴中最普通的一种，属浸鸡类，以其制作简易，刚熟不烂，不加配料且保持原味为特点。白切鸡皮爽肉滑，清淡鲜美，配以蘸料食用，口感层次丰富。',
    image_url: 'https://images.unsplash.com/photo-1604503468506-a8da13d82791?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
    restaurant_name: '粤味轩',
    region: '广东',
    tags: ['经典', '清淡', '营养', '下饭'],
    ingredients: ['土鸡', '生姜', '葱白', '料酒', '盐'],
    nutrition: {
      calories: 165,
      protein: 31.0,
      fat: 3.6,
      carbs: 0
    },
    preparation_time: '45分钟',
    spice_level: '不辣'
  };

  const mockReviews: Review[] = [
    {
      review_id: 1,
      user_name: '美食达人小王',
      user_avatar: '/placeholder.svg?height=40&width=40',
      rating: 5,
      comment: '非常正宗的白切鸡！鸡肉嫩滑，蘸料调得很好，特别是姜蓉蘸料，很香很开胃。服务也很好，推荐！',
      created_at: '2024-01-20 18:30:00',
      helpful_count: 12,
      images: ['/placeholder.svg?height=100&width=100', '/placeholder.svg?height=100&width=100']
    },
    {
      review_id: 2,
      user_name: '广州吃货',
      user_avatar: '/placeholder.svg?height=40&width=40',
      rating: 4,
      comment: '味道不错，鸡肉很新鲜，就是价格稍微贵了一点。整体来说还是值得推荐的。',
      created_at: '2024-01-18 12:15:00',
      helpful_count: 8
    },
    {
      review_id: 3,
      user_name: '粤菜爱好者',
      user_avatar: '/placeholder.svg?height=40&width=40',
      rating: 5,
      comment: '这家的白切鸡做得很地道，鸡皮Q弹，肉质鲜美。蘸料也很棒，有传统的姜蓉和现代的蒜蓉两种选择。',
      created_at: '2024-01-15 20:45:00',
      helpful_count: 15
    }
  ];

  const mockSimilarDishes: SimilarDish[] = [
    {
      dish_id: 2,
      name: '口水鸡',
      price: 32.0,
      rating: 4.3,
      image_url: 'https://images.unsplash.com/photo-1598515214211-89d3c73ae83b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      similarity_score: 0.85
    },
    {
      dish_id: 3,
      name: '盐焗鸡',
      price: 38.0,
      rating: 4.4,
      image_url: 'https://images.unsplash.com/photo-1604503468506-a8da13d82791?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      similarity_score: 0.78
    },
    {
      dish_id: 4,
      name: '叉烧',
      price: 42.0,
      rating: 4.2,
      image_url: 'https://images.unsplash.com/photo-1563379091339-03246963d51a?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      similarity_score: 0.72
    }
  ];

  // 检查收藏状态
  useEffect(() => {
    const checkFavoriteStatus = async () => {
      if (isAuthenticated && user && dishId) {
        try {
          const favoriteStatus = await userApi.checkFavorite(user.user_id, parseInt(dishId));
          setIsFavorited(favoriteStatus);
        } catch (error) {
          console.error('检查收藏状态失败:', error);
          setIsFavorited(false);
        }
      } else {
        setIsFavorited(false);
      }
    };

    checkFavoriteStatus();
  }, [isAuthenticated, user, dishId]);

  useEffect(() => {
    const fetchDishDetail = async () => {
      setLoading(true);
      try {
        // 获取菜品详情
        const response = await fetch(`http://localhost:5000/api/dishes/${dishId}`);
        if (response.ok) {
          const data = await response.json();
          
          // 处理后端返回的数据结构
          console.log('API返回的菜品数据:', data); // 调试日志
          
          let dishData = null;
          if (data.success && data.data) {
            dishData = data.data;
          } else if (data.dish) {
            dishData = data.dish;
          } else if (data.id || data.dish_id) {
            dishData = data;
          } else {
            console.error('无法解析菜品数据:', data);
            dishData = mockDish;
          }
          
          const processedDish = {
            ...mockDish, // 使用模拟数据作为基础
            ...dishData, // 覆盖真实数据
            dish_id: dishData.id || dishData.dish_id || mockDish.dish_id,
            cuisine_type: dishData.cuisine_type || dishData.cuisine || mockDish.cuisine_type,
            image_url: dishData.image_url || dishData.image || mockDish.image_url,
            review_count: dishData.review_count || mockDish.review_count,
            restaurant_name: dishData.restaurant_name || mockDish.restaurant_name,
            preparation_time: dishData.preparation_time || mockDish.preparation_time,
            spice_level: dishData.spice_level || mockDish.spice_level,
            tags: dishData.tags || mockDish.tags,
            ingredients: dishData.ingredients || mockDish.ingredients,
            nutrition: dishData.nutrition || mockDish.nutrition
          };
          
          setDish(processedDish);
          
          // 获取真实的评价数据
          try {
            const reviewsResponse = await dishApi.getDishReviews(parseInt(dishId!), 1, 10);
            if (reviewsResponse && reviewsResponse.items) {
              setReviews(reviewsResponse.items);
            } else {
              setReviews(mockReviews);
            }
          } catch (error) {
            console.error('获取评价失败:', error);
            setReviews(mockReviews);
          }

          // 获取用户对这道菜的所有评价
          if (isAuthenticated && user) {
            try {
              const userReviewsResponse = await dishApi.getUserReviewsForDish(parseInt(dishId!), user.user_id);
              if (userReviewsResponse.success && userReviewsResponse.data) {
                setUserReviews(userReviewsResponse.data);
              } else {
                setUserReviews([]);
              }
              // 重置表单
              setNewRating(5);
              setNewReview('');
              setIsEditingReview(null);
            } catch (error) {
              console.error('获取用户评价失败:', error);
              setUserReviews([]);
            }
          }
          
          // 获取相似菜品推荐
          try {
            const similarResponse = await fetch(`http://localhost:5000/api/dishes/${dishId}/similar?limit=3`);
            if (similarResponse.ok) {
              const similarData = await similarResponse.json();
              if (similarData.success && similarData.data && Array.isArray(similarData.data)) {
                const processedSimilar = similarData.data.map((item: any) => ({
                  dish_id: item.dish_id,
                  name: item.name,
                  price: item.price,
                  rating: item.rating,
                  image_url: item.image_url || '',
                  similarity_score: item.similarity_score || 0.8
                }));
                setSimilarDishes(processedSimilar);
              } else {
                // 如果API返回格式不正确，使用模拟数据
                setSimilarDishes(mockSimilarDishes);
              }
            } else {
              // 如果API请求失败，使用模拟数据
              setSimilarDishes(mockSimilarDishes);
            }
          } catch (error) {
            console.error('获取相似菜品失败:', error);
            // 出现错误时使用模拟数据作为后备
            setSimilarDishes(mockSimilarDishes);
          }

          // 获取同店推荐
          try {
            const sameRestaurantResponse = await dishApi.getSameRestaurantDishes(parseInt(dishId!), 6);
            if (sameRestaurantResponse && sameRestaurantResponse.success && sameRestaurantResponse.data) {
              setSameRestaurantDishes(sameRestaurantResponse.data);
            } else {
              setSameRestaurantDishes([]);
            }
          } catch (error) {
            console.error('获取同店推荐失败:', error);
            setSameRestaurantDishes([]);
          }
        } else {
          // 如果API失败，使用模拟数据
          setDish(mockDish);
          setReviews(mockReviews);
          setSimilarDishes(mockSimilarDishes);
        }
      } catch (error) {
        console.error('获取菜品详情失败:', error);
        // 使用模拟数据作为后备
        setDish(mockDish);
        setReviews(mockReviews);
        setSimilarDishes(mockSimilarDishes);
      } finally {
        setLoading(false);
      }
    };

    fetchDishDetail();
  }, [dishId]);

  const handleFavorite = async () => {
    if (!isAuthenticated || !user) {
      // 如果用户未登录，显示登录模态框
      setShowLoginModal(true);
      return;
    }

    if (!dish) return;

    setFavoriteLoading(true);
    try {
      const currentDishId = dish.dish_id || dish.id || parseInt(dishId!);
      
      if (isFavorited) {
        // 取消收藏
        await userApi.removeFavorite(user.user_id, currentDishId);
        setIsFavorited(false);
      } else {
        // 添加收藏
        await userApi.addFavorite(user.user_id, currentDishId);
        setIsFavorited(true);
      }
      
      // 操作成功后重新检查收藏状态以确保同步
      setTimeout(async () => {
        try {
          const actualStatus = await userApi.checkFavorite(user.user_id, currentDishId);
          setIsFavorited(actualStatus);
        } catch (error) {
          console.error('重新检查收藏状态失败:', error);
        }
      }, 100);
      
    } catch (error) {
      console.error('收藏操作失败:', error);
      // 显示更详细的错误信息
      if (error instanceof Error) {
        alert(`收藏操作失败: ${error.message}`);
      } else {
        alert('收藏操作失败，请稍后重试');
      }
    } finally {
      setFavoriteLoading(false);
    }
  };

  const handleSubmitReview = async () => {
    if (!isAuthenticated || !user) {
      setShowLoginModal(true);
      return;
    }

    if (!newReview.trim()) {
      alert('请输入评价内容');
      return;
    }

    if (!dishId) {
      alert('菜品信息异常，请刷新页面重试');
      return;
    }
    
    try {
      let response;
      
      if (isEditingReview) {
        // 更新现有评价
        response = await dishApi.updateReview(isEditingReview, {
          user_id: user.user_id,
          rating: newRating,
          content: newReview
        });
        
        if (response && response.success) {
          // 更新用户评价列表中的该评价
          setUserReviews(prev => prev.map(review => 
            review.review_id === isEditingReview 
              ? { ...review, rating: newRating, comment: newReview, updated_at: new Date().toISOString() }
              : review
          ));
          
          // 更新所有评价列表中的该评价
          setReviews(prev => prev.map(review => 
            review.review_id === isEditingReview 
              ? { ...review, rating: newRating, comment: newReview, created_at: new Date().toLocaleString() }
              : review
          ));
          
          setIsEditingReview(null);
          setNewReview('');
          setNewRating(5);
          
          alert('评价更新成功！');
        }
      } else {
        // 添加新评价
        response = await dishApi.addReview(parseInt(dishId!), {
          user_id: user.user_id,
          rating: newRating,
          content: newReview
        });
        
        if (response && response.success && response.data) {
          // 添加新评价到列表顶部
          const newReviewData = {
            review_id: response.data.review_id,
            user_name: user.username,
            user_avatar: response.data.user_avatar || '/placeholder.svg?height=40&width=40',
            rating: newRating,
            comment: newReview,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            helpful_count: 0
          };
          
          // 添加到用户评价列表顶部
          setUserReviews(prev => [newReviewData, ...prev]);
          
          // 添加到所有评价列表顶部
          setReviews(prev => [newReviewData, ...prev]);
          
          setNewReview('');
          setNewRating(5);
          
          // 重新获取菜品信息以更新评分和评价数量
          const dishResponse = await fetch(`http://localhost:5000/api/dishes/${dishId}`);
          if (dishResponse.ok) {
            const dishData = await dishResponse.json();
            if (dishData.success && dishData.data) {
              setDish(prev => prev ? { 
                ...prev, 
                rating: dishData.data.rating, 
                review_count: (prev.review_count || 0) + 1 
              } : null);
            }
          }
          
          alert('评价提交成功！您可以继续添加更多评价。');
        }
      }
    } catch (error: any) {
      console.error('提交评价失败:', error);
      
      // 检查是否是菜品不存在的错误
      if (error.message && error.message.includes('菜品不存在')) {
        alert('该菜品不存在，请返回重新选择菜品');
        navigate(-1); // 返回上一页
      } else if (error.message && error.message.includes('用户不存在')) {
        alert('用户信息异常，请重新登录');
        setShowLoginModal(true);
      } else {
        alert('评价提交失败，请稍后重试: ' + (error.message || '网络错误'));
      }
    }
  };

  const handleEditReview = (reviewId: number) => {
    const review = userReviews.find(r => r.review_id === reviewId);
    if (review) {
      setNewRating(review.rating);
      setNewReview(review.comment || review.content);
      setIsEditingReview(reviewId);
    }
  };

  const handleCancelEdit = () => {
    setIsEditingReview(null);
    setNewRating(5);
    setNewReview('');
  };

  const handleDeleteReview = async (reviewId: number) => {
    if (!user) return;
    
    if (!confirm('确定要删除这条评价吗？')) return;
    
    try {
      const response = await dishApi.deleteReview(reviewId, user.user_id);
      if (response && response.success) {
        // 从用户评价列表中移除
        setUserReviews(prev => prev.filter(review => review.review_id !== reviewId));
        
        // 从所有评价列表中移除
        setReviews(prev => prev.filter(review => review.review_id !== reviewId));
        
        // 如果正在编辑这条评价，清除编辑状态
        if (isEditingReview === reviewId) {
          setIsEditingReview(null);
          setNewRating(5);
          setNewReview('');
        }
        
        // 重新获取菜品信息以更新评分和评价数量
        const dishResponse = await fetch(`http://localhost:5000/api/dishes/${dishId}`);
        if (dishResponse.ok) {
          const dishData = await dishResponse.json();
          if (dishData.success && dishData.data) {
            setDish(prev => prev ? { 
              ...prev, 
              rating: dishData.data.rating, 
              review_count: Math.max((prev.review_count || 1) - 1, 0)
            } : null);
          }
        }
        
        alert('评价删除成功！');
      }
    } catch (error) {
      console.error('删除评价失败:', error);
      alert('删除评价失败，请稍后重试');
    }
  };

  const StarRating: React.FC<{ rating: number; size?: 'sm' | 'md' | 'lg'; interactive?: boolean; onRatingChange?: (rating: number) => void }> = ({ 
    rating, 
    size = 'md', 
    interactive = false, 
    onRatingChange 
  }) => {
    const sizeClasses = {
      sm: 'h-4 w-4',
      md: 'h-5 w-5',
      lg: 'h-6 w-6'
    };

    return (
      <div className="flex gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`${sizeClasses[size]} ${
              star <= rating 
                ? 'fill-yellow-400 text-yellow-400' 
                : 'text-gray-300'
            } ${interactive ? 'cursor-pointer hover:text-yellow-400' : ''}`}
            onClick={interactive && onRatingChange ? () => onRatingChange(star) : undefined}
          />
        ))}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-orange-50 to-green-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
          <p className="text-gray-600">正在加载菜品详情...</p>
        </div>
      </div>
    );
  }

  if (!dish) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-orange-50 to-green-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">菜品不存在</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-green-50">
      {/* 头部导航 */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center gap-4">
              <Button variant="ghost" size="sm" onClick={handleGoBack}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回
              </Button>
              <h1 className="text-2xl font-bold text-orange-600">美食推荐</h1>
            </div>
            <nav className="hidden md:flex space-x-8">
              <button onClick={() => navigate('/')} className="text-gray-500 hover:text-orange-600">首页</button>
              <button onClick={() => navigate('/')} className="text-gray-500 hover:text-orange-600">推荐</button>
              <button onClick={() => navigate('/search')} className="text-gray-500 hover:text-orange-600">搜索</button>
              <button onClick={() => navigate('/profile')} className="text-gray-500 hover:text-orange-600">我的</button>
            </nav>
            {isAuthenticated && user ? (
              <div className="flex items-center gap-3">
                <span className="text-gray-700">欢迎，{user.username}</span>
                <Button 
                  variant="outline" 
                  onClick={() => {
                    logout();
                    navigate('/');
                  }}
                >
                  退出登录
                </Button>
              </div>
            ) : (
              <Button 
                className="bg-orange-500 hover:bg-orange-600"
                onClick={() => setShowLoginModal(true)}
              >
                登录
              </Button>
            )}
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 左侧主要内容 */}
          <div className="lg:col-span-2 space-y-6">
            {/* 菜品主要信息 */}
            <Card className="overflow-hidden">
              <div className="relative">
                <img
                  src={getDishImageUrl(dish.name, dish.image_url || dish.image)}
                  alt={dish.name}
                  className="w-full h-64 md:h-80 object-cover"
                  onError={handleImageError}
                />
                <div className="absolute top-4 right-4 flex gap-2">
                  <Button
                    size="sm"
                    variant="ghost"
                    className="bg-white/80 hover:bg-white"
                    onClick={handleFavorite}
                    disabled={favoriteLoading}
                  >
                    <Heart className={`h-4 w-4 ${isFavorited ? 'fill-red-500 text-red-500' : 'text-gray-600'} ${favoriteLoading ? 'animate-pulse' : ''}`} />
                  </Button>
                  <Button size="sm" variant="ghost" className="bg-white/80 hover:bg-white">
                    <Share2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h1 className="text-3xl font-bold text-gray-900 mb-2">{dish.name}</h1>
                    <div className="flex items-center gap-4 mb-3">
                    <Badge className="bg-orange-100 text-orange-800 hover:bg-orange-200">
                        {dish.cuisine_type || dish.cuisine}
                      </Badge>
                      <div className="flex items-center gap-1">
                        <MapPin className="h-4 w-4 text-gray-500" />
                        <span className="text-gray-600">{dish.region}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4 text-gray-500" />
                        <span className="text-gray-600">{dish.preparation_time}</span>
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-3xl font-bold text-orange-600 mb-1">¥{dish.price}</div>
                    <div className="flex items-center gap-2">
                      <StarRating rating={dish.rating} />
                      <span className="text-sm text-gray-600">({dish.review_count}条评价)</span>
                    </div>
                  </div>
                </div>

                <p className="text-gray-700 leading-relaxed mb-4">{dish.description}</p>

                <div className="flex flex-wrap gap-2 mb-4">
                  {dish.tags.map((tag, index) => (
                    <Badge key={index} variant="outline">
                      {tag}
                    </Badge>
                  ))}
                </div>

                <div className="flex gap-4">
                  <Button 
                    className={`flex-1 ${isFavorited ? 'bg-red-500 hover:bg-red-600' : 'bg-orange-500 hover:bg-orange-600'}`}
                    onClick={handleFavorite}
                    disabled={favoriteLoading}
                  >
                    <Heart className={`h-4 w-4 mr-2 ${isFavorited ? 'fill-white' : ''} ${favoriteLoading ? 'animate-pulse' : ''}`} />
                    {favoriteLoading ? '处理中...' : (isFavorited ? '已收藏' : '收藏')}
                  </Button>
                  <Button variant="outline" className="flex-1" onClick={() => navigate(`/restaurant/${encodeURIComponent(dish.restaurant_name || '')}`)}>
                    <Users className="h-4 w-4 mr-2" />
                    查看餐厅
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* 详细信息标签页 */}
            <Card>
              <CardContent className="p-6">
                <Tabs defaultValue="ingredients" className="w-full">
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="ingredients">食材配料</TabsTrigger>
                    <TabsTrigger value="nutrition">营养信息</TabsTrigger>
                    <TabsTrigger value="restaurant">餐厅信息</TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="ingredients" className="mt-4">
                    <div>
                      <h3 className="font-semibold mb-3">主要食材</h3>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                        {dish.ingredients.map((ingredient, index) => (
                          <Badge key={index} variant="secondary" className="justify-center py-2">
                            {ingredient}
                          </Badge>
                        ))}
                      </div>
                      <div className="mt-4 p-4 bg-orange-50 rounded-lg">
                        <p className="text-sm text-orange-800">
                          <strong>辣度等级:</strong> {dish.spice_level}
                        </p>
                      </div>
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="nutrition" className="mt-4">
                    <div>
                      <h3 className="font-semibold mb-3">营养成分 (每100g)</h3>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div className="text-center p-3 bg-gray-50 rounded-lg">
                          <div className="text-2xl font-bold text-orange-600">{dish.nutrition.calories}</div>
                          <div className="text-sm text-gray-600">卡路里</div>
                        </div>
                        <div className="text-center p-3 bg-gray-50 rounded-lg">
                          <div className="text-2xl font-bold text-green-600">{dish.nutrition.protein}g</div>
                          <div className="text-sm text-gray-600">蛋白质</div>
                        </div>
                        <div className="text-center p-3 bg-gray-50 rounded-lg">
                          <div className="text-2xl font-bold text-blue-600">{dish.nutrition.fat}g</div>
                          <div className="text-sm text-gray-600">脂肪</div>
                        </div>
                        <div className="text-center p-3 bg-gray-50 rounded-lg">
                          <div className="text-2xl font-bold text-purple-600">{dish.nutrition.carbs}g</div>
                          <div className="text-sm text-gray-600">碳水化合物</div>
                        </div>
                      </div>
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="restaurant" className="mt-4">
                    <div>
                      <h3 className="font-semibold mb-3">餐厅信息</h3>
                      <div className="space-y-3">
                        <div className="flex items-center gap-3">
                          <MapPin className="h-5 w-5 text-gray-500" />
                          <div>
                            <div className="font-medium">{dish.restaurant_name}</div>
                            <div className="text-sm text-gray-600">{dish.region}地区特色餐厅</div>
                          </div>
                        </div>
                        <div className="flex items-center gap-3">
                          <Clock className="h-5 w-5 text-gray-500" />
                          <div>
                            <div className="font-medium">营业时间</div>
                            <div className="text-sm text-gray-600">10:00 - 22:00</div>
                          </div>
                        </div>
                        <div className="flex items-center gap-3">
                          <Star className="h-5 w-5 text-gray-500" />
                          <div>
                            <div className="font-medium">餐厅评分</div>
                            <div className="text-sm text-gray-600">4.6分 (1,234条评价)</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>

            {/* 用户评价 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Star className="h-5 w-5 text-orange-500" />
                  用户评价 ({reviews.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                {/* 用户评价管理区域 */}
                {isAuthenticated && user ? (
                  <div className="mb-6 space-y-4">
                    {/* 用户的评价历史 */}
                    {userReviews.length > 0 && (
                      <div className="p-4 bg-blue-50 rounded-lg">
                        <div className="flex items-center justify-between mb-3">
                          <h4 className="font-medium text-blue-900">
                            您的评价历史 ({userReviews.length}条)
                          </h4>
                          <Button 
                            size="sm"
                            variant="outline"
                            onClick={() => setShowAllUserReviews(!showAllUserReviews)}
                          >
                            {showAllUserReviews ? '收起' : '展开'}
                          </Button>
                        </div>
                        
                        {/* 评价时间轴 */}
                        <div className="space-y-3">
                          {(showAllUserReviews ? userReviews : userReviews.slice(0, 1)).map((review, index) => (
                            <div key={review.review_id} className="bg-white p-3 rounded border">
                              <div className="flex items-center justify-between mb-2">
                                <div className="flex items-center gap-2">
                                  <StarRating rating={review.rating} size="sm" />
                                  <span className="text-sm text-gray-600">({review.rating}星)</span>
                                  <span className="text-sm text-gray-500">
                                    {new Date(review.created_at).toLocaleDateString('zh-CN')}
                                  </span>
                                  {review.updated_at !== review.created_at && (
                                    <span className="text-xs text-blue-600">[已编辑]</span>
                                  )}
                                </div>
                                <div className="flex gap-2">
                                  <Button 
                                    size="sm" 
                                    variant="outline"
                                    onClick={() => handleEditReview(review.review_id)}
                                    disabled={isEditingReview === review.review_id}
                                  >
                                    编辑
                                  </Button>
                                  <Button 
                                    size="sm" 
                                    variant="outline"
                                    onClick={() => handleDeleteReview(review.review_id)}
                                    className="text-red-600 hover:text-red-700"
                                  >
                                    删除
                                  </Button>
                                </div>
                              </div>
                              <p className="text-gray-700 text-sm leading-relaxed">
                                {review.comment || review.content}
                              </p>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* 写新评价或编辑评价 */}
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium">
                          {isEditingReview ? '编辑评价' : '写下您的新评价'}
                        </h4>
                        {isEditingReview && (
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={handleCancelEdit}
                          >
                            取消编辑
                          </Button>
                        )}
                      </div>
                      <div className="flex items-center gap-2 mb-3">
                        <span className="text-sm">评分:</span>
                        <StarRating 
                          rating={newRating} 
                          interactive={true} 
                          onRatingChange={setNewRating}
                        />
                        <span className="text-sm text-gray-600">({newRating}星)</span>
                      </div>
                      <Textarea
                        placeholder={isEditingReview ? "修改您的评价内容..." : "分享您的用餐体验..."}
                        value={newReview}
                        onChange={(e) => setNewReview(e.target.value)}
                        className="mb-3"
                        rows={4}
                      />
                      <div className="flex gap-2">
                        <Button 
                          onClick={handleSubmitReview} 
                          className="bg-orange-500 hover:bg-orange-600"
                          disabled={!newReview.trim()}
                        >
                          {isEditingReview ? '更新评价' : '发布新评价'}
                        </Button>
                        {!isEditingReview && userReviews.length > 0 && (
                          <span className="text-sm text-gray-500 flex items-center">
                            💡 您可以对同一道菜发表多次评价
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="mb-6 p-4 bg-gray-50 rounded-lg text-center">
                    <p className="text-gray-600 mb-3">登录后即可发表评价</p>
                    <Button 
                      onClick={() => setShowLoginModal(true)}
                      className="bg-orange-500 hover:bg-orange-600"
                    >
                      立即登录
                    </Button>
                  </div>
                )}

                {/* 评价列表 */}
                <div className="space-y-4">
                  {reviews.length > 0 ? (
                    reviews.map((review) => (
                      <div key={review.review_id} className="border-b pb-4 last:border-b-0">
                        <div className="flex items-start gap-3">
                          <Avatar>
                            <AvatarImage src={review.user_avatar} />
                            <AvatarFallback>{review.user_name?.[0] || 'U'}</AvatarFallback>
                          </Avatar>
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <span className="font-medium">{review.user_name}</span>
                              <StarRating rating={review.rating} size="sm" />
                              <span className="text-sm text-gray-500">
                                {new Date(review.created_at).toLocaleDateString('zh-CN')}
                              </span>
                            </div>
                            <p className="text-gray-700 mb-2 leading-relaxed">{review.comment}</p>
                            {review.images && review.images.length > 0 && (
                              <div className="flex gap-2 mb-2">
                                {review.images.map((image, index) => (
                                  <img
                                    key={index}
                                    src={getDishImageUrl(`评价图片${index}`, image)}
                                    alt="评价图片"
                                    className="w-16 h-16 object-cover rounded cursor-pointer hover:opacity-80 transition-opacity"
                                    onError={handleImageError}
                                  />
                                ))}
                              </div>
                            )}
                            <div className="flex items-center gap-4 text-sm text-gray-500">
                              <button className="flex items-center gap-1 hover:text-orange-600 transition-colors">
                                <ThumbsUp className="h-4 w-4" />
                                有用 ({review.helpful_count || 0})
                              </button>
                              {isAuthenticated && user && user.username === review.user_name && (
                                <button className="text-red-500 hover:text-red-600 transition-colors">
                                  删除
                                </button>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <Star className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">暂无评价，快来写下第一条评价吧！</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 右侧相关推荐 */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">相似推荐</CardTitle>
                <CardDescription>您可能也会喜欢这些菜品</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {similarDishes.length > 0 ? (
                    similarDishes.map((similarDish) => (
                      <div 
                        key={similarDish.dish_id} 
                        className="flex gap-3 p-3 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                        onClick={() => navigate(`/dish/${similarDish.dish_id}`)}
                      >
                        <img
                          src={getDishImageUrl(similarDish.name, similarDish.image_url)}
                          alt={similarDish.name}
                          className="w-16 h-16 object-cover rounded"
                          onError={handleImageError}
                        />
                        <div className="flex-1">
                          <h4 className="font-medium text-sm mb-1 hover:text-orange-600 transition-colors">{similarDish.name}</h4>
                          <div className="flex items-center gap-1 mb-1">
                            <StarRating rating={similarDish.rating} size="sm" />
                            <span className="text-xs text-gray-500">{similarDish.rating}</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-orange-600 font-medium">¥{similarDish.price}</span>
                            <Badge variant="outline" className="text-xs">
                              {Math.round(similarDish.similarity_score * 100)}%相似
                            </Badge>
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <Heart className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">暂时没有找到相似的菜品</p>
                      <p className="text-xs mt-1">试试浏览其他菜品吧～</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* 餐厅其他菜品 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">同店推荐</CardTitle>
                <CardDescription>来自 {dish.restaurant_name} 的其他菜品</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {sameRestaurantDishes.length > 0 ? (
                    sameRestaurantDishes.map((sameDish) => (
                      <div 
                        key={sameDish.dish_id}
                        className="flex gap-3 cursor-pointer hover:bg-gray-50 p-2 rounded transition-colors"
                        onClick={() => navigate(`/dish/${sameDish.dish_id}`)}
                      >
                        <img
                          src={getDishImageUrl(sameDish.name, sameDish.image_url)}
                          alt={sameDish.name}
                          className="w-12 h-12 object-cover rounded"
                          onError={handleImageError}
                        />
                        <div className="flex-1">
                          <h4 className="font-medium text-sm mb-1 hover:text-orange-600 transition-colors">{sameDish.name}</h4>
                          <div className="flex justify-between items-center">
                            <span className="text-orange-600 font-medium text-sm">¥{sameDish.price}</span>
                            <div className="flex items-center gap-1">
                              <StarRating rating={sameDish.rating} size="sm" />
                              <span className="text-xs text-gray-500">{sameDish.rating}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <Users className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">暂无同店其他菜品</p>
                      <p className="text-xs mt-1">这家餐厅可能只有这一道菜～</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* 登录模态框 */}
      <LoginModal 
        open={showLoginModal} 
        onOpenChange={setShowLoginModal} 
      />
    </div>
  );
};

export default DishDetailPage;
