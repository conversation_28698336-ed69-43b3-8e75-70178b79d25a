# 数据爬虫使用指南

## 概述

美食推荐系统的数据爬虫模块负责从主流美食平台采集广西、广东、海南三省的公开菜品数据。本模块严格遵守各平台的robots.txt协议，仅采集公开可获取的信息。

## 支持平台

- **美团外卖** (meituan)
- **饿了么** (eleme)

## 功能特性

- 🕷️ **多平台支持**: 同时支持美团外卖和饿了么平台
- 🌍 **地区覆盖**: 覆盖广西、广东、海南三省主要城市
- 🔄 **并发爬取**: 支持多线程并发提升效率
- 📊 **数据验证**: 自动验证和清理爬取数据
- 📈 **统计报告**: 生成详细的爬取统计报告
- ⚙️ **配置灵活**: 支持自定义爬取参数

## 快速开始

### 1. 环境准备

```bash
# 进入后端目录
cd backend

# 安装依赖
pip install -r requirements.txt

# 创建必要目录
mkdir -p logs data/scraped
```

### 2. 基本使用

```bash
# 爬取所有平台数据（测试模式）
python scrape_data.py --test

# 爬取指定平台
python scrape_data.py --platform meituan --limit 30

# 爬取指定地区
python scrape_data.py --region 广州 --limit 50

# 完整爬取（生产模式）
python scrape_data.py --platform all --limit 100 --workers 3
```

### 3. 命令行参数

| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|------|
| `--platform` | 选择平台 (meituan/eleme/all) | all | `--platform meituan` |
| `--region` | 指定地区 | 所有目标地区 | `--region 广州` |
| `--limit` | 每地区爬取数量 | 50 | `--limit 100` |
| `--workers` | 并发线程数 | 3 | `--workers 5` |
| `--log-level` | 日志级别 | INFO | `--log-level DEBUG` |
| `--test` | 测试模式 | False | `--test` |

## 配置文件

### scraper_config.json

```json
{
  "scraper_settings": {
    "default_delay_range": [2, 4],
    "max_retries": 3,
    "timeout": 30
  },
  "platforms": {
    "meituan": {
      "enabled": true,
      "rate_limit": 2
    },
    "eleme": {
      "enabled": true,
      "rate_limit": 2
    }
  }
}
```

## 数据结构

### 菜品数据字段

```json
{
  "name": "白切鸡",
  "description": "经典粤菜，鸡肉鲜嫩",
  "price": 45.0,
  "price_range": "40-50",
  "cuisine_type": "粤菜",
  "region": "广东",
  "image_url": "https://example.com/image.jpg",
  "ingredients": "土鸡,生抽,香葱",
  "cooking_method": "白切",
  "spicy_level": 0,
  "rating": 4.5,
  "review_count": 128,
  "order_count": 256,
  "source_platform": "meituan",
  "source_url": "https://example.com/dish",
  "restaurant_name": "XX餐厅",
  "scraped_at": "2024-01-01T12:00:00",
  "platform": "meituan"
}
```

## 使用示例

### 1. 单平台爬取

```python
from utils.meituan_scraper import MeituanScraper

# 创建美团爬虫
scraper = MeituanScraper()

# 爬取广州地区数据
dishes = scraper.scrape_dishes_by_region('广州', limit=50)
print(f"获得 {len(dishes)} 道菜品")
```

### 2. 多平台并发爬取

```python
from utils.scraper_manager import ScraperManager

# 创建管理器
manager = ScraperManager(max_workers=3)

# 爬取所有平台数据
results = manager.scrape_all_platforms(
    regions=['广州', '南宁', '海口'],
    limit_per_region=30
)
```

### 3. 数据验证

```python
from utils.scraper_base import DataValidator

# 验证单个菜品数据
is_valid, message = DataValidator.validate_dish_data(dish_data)

# 清理数据
cleaned_data = DataValidator.clean_dish_data(dish_data)
```

## 输出文件

### 数据文件

- `data/scraped/{platform}_{region}_dishes_{timestamp}.json` - 单平台单地区数据
- `data/scraped/all_platforms_data_{timestamp}.json` - 汇总数据
- `data/scraped/scrape_report_{timestamp}.json` - 统计报告

### 日志文件

- `logs/scraper_{date}.log` - 详细运行日志

## 数据质量控制

### 1. 数据验证规则

- **必需字段**: name, platform
- **数值验证**: price (0-1000), rating (0-5)
- **文本清理**: 去除特殊字符和多余空白

### 2. 质量指标

- 包含名称比例
- 包含价格比例
- 包含描述比例
- 包含图片比例
- 完整记录比例

## 注意事项

### 1. 合规性

- ✅ 遵守robots.txt协议
- ✅ 设置合理的请求间隔
- ✅ 仅采集公开信息
- ✅ 不进行恶意爬取

### 2. 性能优化

- 使用连接池复用连接
- 设置合理的并发数
- 实现请求重试机制
- 添加随机延迟避免封禁

### 3. 错误处理

- 网络异常自动重试
- 数据解析错误跳过
- 详细错误日志记录
- 优雅的程序中断处理

## 故障排除

### 常见问题

1. **网络连接失败**
   ```
   解决方案: 检查网络连接，调整超时设置
   ```

2. **数据解析错误**
   ```
   解决方案: 检查页面结构变化，更新选择器
   ```

3. **访问被限制**
   ```
   解决方案: 增加请求间隔，更换User-Agent
   ```

4. **Chrome驱动问题**
   ```
   解决方案: 安装对应版本的ChromeDriver
   ```

### 调试模式

```bash
# 启用详细日志
python scrape_data.py --log-level DEBUG --test

# 单线程运行便于调试
python scrape_data.py --workers 1 --test
```

## 扩展开发

### 添加新平台

1. 继承 `ScraperBase` 类
2. 实现必要的解析方法
3. 在 `ScraperManager` 中注册
4. 更新配置文件

### 自定义数据处理

1. 扩展 `DataValidator` 类
2. 添加新的验证规则
3. 实现自定义清理逻辑

## 性能监控

### 统计指标

- 爬取速度 (菜品/分钟)
- 成功率
- 数据质量分数
- 平台响应时间

### 监控命令

```bash
# 查看实时日志
tail -f logs/scraper_$(date +%Y%m%d).log

# 统计爬取结果
grep "爬取完成" logs/scraper_*.log | wc -l
```

## 最佳实践

1. **定期更新**: 定期检查平台页面结构变化
2. **数据备份**: 及时备份重要的爬取数据
3. **监控告警**: 设置爬取失败的告警机制
4. **资源控制**: 合理控制并发数避免过载
5. **日志分析**: 定期分析日志优化爬取策略

---

如有问题，请查看详细日志或联系开发团队。