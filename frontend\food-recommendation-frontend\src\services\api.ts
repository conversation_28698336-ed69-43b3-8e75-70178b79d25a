/**
 * API服务层 - 处理前后端数据交互
 */

// API基础配置
const API_BASE_URL = 'http://localhost:5000';

// 请求配置
const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
};

// API响应接口
interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message: string;
  error_code?: number;
}

// 分页响应接口
interface PaginatedResponse<T> {
  items: T[];
  pagination: {
    page: number;
    per_page: number;
    total: number;
    pages: number;
  };
}

// 数据类型定义
export interface Dish {
  dish_id: number;
  name: string;
  cuisine_type: string;
  price: number;
  rating: number;
  review_count: number;
  description: string;
  image_url: string;
  restaurant_name: string;
  region: string;
  tags?: string[];
  ingredients?: string[];
  nutrition?: {
    calories: number;
    protein: number;
    fat: number;
    carbs: number;
  };
  preparation_time?: string;
  spice_level?: string;
}

export interface User {
  user_id: number;
  username: string;
  email: string;
  phone?: string;
  avatar_url?: string;
  created_at: string;
  last_login?: string;
  bio?: string;
  location?: string;
  preferences?: {
    preferred_cuisines: string[];
    dietary_restrictions: string[];
    price_range: string;
    spice_level: string;
  };
  statistics?: {
    total_orders: number;
    total_reviews: number;
    favorite_dishes: number;
    points: number;
    level: string;
  };
}

export interface Review {
  review_id: number;
  user_name: string;
  user_avatar: string;
  rating: number;
  comment: string;
  created_at: string;
  helpful_count: number;
  images?: string[];
}

export interface Recommendation {
  dish_id: number;
  name: string;
  cuisine_type: string;
  price: number;
  rating: number;
  image_url: string;
  restaurant_name: string;
  recommendation_score: number;
  recommendation_reason: string;
}

// HTTP请求工具函数
class ApiClient {
  private baseURL: string;
  private token: string | null = null;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
    this.token = localStorage.getItem('auth_token');
  }

  private getHeaders(): HeadersInit {
    const headers: HeadersInit = { ...DEFAULT_HEADERS };
    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`;
    }
    return headers;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    const config: RequestInit = {
      headers: this.getHeaders(),
      ...options,
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error('API请求失败:', error);
      throw error;
    }
  }

  async get<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'GET' });
  }

  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }

  setToken(token: string) {
    this.token = token;
    localStorage.setItem('auth_token', token);
  }

  clearToken() {
    this.token = null;
    localStorage.removeItem('auth_token');
  }
}

// 创建API客户端实例
const apiClient = new ApiClient(API_BASE_URL);

// 推荐相关API
export const recommendationApi = {
  // 获取用户个性化推荐
  getUserRecommendations: async (userId: number, limit: number = 10): Promise<Recommendation[]> => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/recommendations/user/${userId}?limit=${limit}`);
      const data = await response.json();
      
      // 转换后端数据格式为前端期望格式
      if (data.dishes && Array.isArray(data.dishes)) {
        return data.dishes.map((dish: any) => ({
          dish_id: dish.id,
          name: dish.name,
          cuisine_type: dish.cuisine,
          price: dish.price,
          rating: dish.rating,
          image_url: dish.image,
          restaurant_name: dish.region,
          recommendation_score: dish.rating,
          recommendation_reason: dish.tags?.[0] || '推荐'
        }));
      }
      return [];
    } catch (error) {
      console.error('获取用户推荐失败:', error);
      return [];
    }
  },

  // 获取相似菜品推荐
  getSimilarDishes: async (dishId: number, limit: number = 6): Promise<Recommendation[]> => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/recommendations/similar/${dishId}?limit=${limit}`);
      const data = await response.json();
      
      if (data.dishes && Array.isArray(data.dishes)) {
        return data.dishes.map((dish: any) => ({
          dish_id: dish.id,
          name: dish.name,
          cuisine_type: dish.cuisine,
          price: dish.price,
          rating: dish.rating,
          image_url: dish.image,
          restaurant_name: dish.region,
          recommendation_score: dish.rating,
          recommendation_reason: '相似推荐'
        }));
      }
      return [];
    } catch (error) {
      console.error('获取相似菜品失败:', error);
      return [];
    }
  },

  // 获取热门菜品
  getPopularDishes: async (region?: string, cuisine?: string, limit: number = 20): Promise<Dish[]> => {
    try {
      const params = new URLSearchParams();
      if (region) params.append('region', region);
      if (cuisine) params.append('cuisine', cuisine);
      params.append('limit', limit.toString());
      
      const response = await fetch(`${API_BASE_URL}/api/recommendations/popular?${params.toString()}`);
      const data = await response.json();
      
      // 转换后端数据格式为前端期望格式
      if (data.dishes && Array.isArray(data.dishes)) {
        return data.dishes.map((dish: any) => ({
          dish_id: dish.id,
          name: dish.name,
          cuisine_type: dish.cuisine,
          price: dish.price,
          rating: dish.rating,
          review_count: dish.reviews?.length || 0,
          description: dish.description,
          image_url: dish.image,
          restaurant_name: dish.region,
          region: dish.region,
          tags: dish.tags || [],
          ingredients: dish.ingredients || [],
          nutrition: dish.nutrition || { calories: 0, protein: 0, fat: 0, carbs: 0 }
        }));
      }
      return [];
    } catch (error) {
      console.error('获取热门菜品失败:', error);
      return [];
    }
  },

  // 获取用户行为洞察
  getUserInsights: async (userId: number) => {
    const response = await apiClient.get(`/api/recommendations/insights/${userId}`);
    return response.data;
  },

  // 更新用户交互记录
  updateInteraction: async (data: {
    user_id: number;
    dish_id: number;
    interaction_type: 'view' | 'like' | 'order' | 'review';
    rating?: number;
  }) => {
    const response = await apiClient.post('/api/recommendations/interaction', data);
    return response.data;
  },
};

// 菜品相关API
export const dishApi = {
  // 获取菜品列表
  getDishes: async (params: {
    page?: number;
    per_page?: number;
    cuisine_type?: string;
    region?: string;
    price_min?: number;
    price_max?: number;
    rating_min?: number;
    sort_by?: string;
  } = {}): Promise<PaginatedResponse<Dish>> => {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        searchParams.append(key, value.toString());
      }
    });

    const response = await apiClient.get<PaginatedResponse<Dish>>(`/api/dishes?${searchParams.toString()}`);
    return response.data || { items: [], pagination: { page: 1, per_page: 20, total: 0, pages: 0 } };
  },

  // 获取菜品详情
  getDishDetail: async (dishId: number): Promise<Dish | null> => {
    const response = await apiClient.get<Dish>(`/api/dishes/${dishId}`);
    return response.data || null;
  },

  // 搜索菜品 - 增强版支持多平台数据
  searchDishes: async (params: {
    query?: string;
    page?: number;
    per_page?: number;
    cuisine_type?: string;
    province?: string;
    city?: string;
    category?: string;
    min_price?: number;
    max_price?: number;
    min_rating?: number;
    source_platform?: string;
  }): Promise<PaginatedResponse<Dish>> => {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        searchParams.append(key, value.toString());
      }
    });

    try {
      console.log('发起搜索请求:', `${API_BASE_URL}/api/dishes/search?${searchParams.toString()}`);
      
      // 使用fetch直接调用，添加超时控制
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时
      
      const response = await fetch(`${API_BASE_URL}/api/dishes/search?${searchParams.toString()}`, {
        method: 'GET',
        headers: DEFAULT_HEADERS,
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const backendResponse = await response.json();
      console.log('搜索响应:', backendResponse);
      
      if (backendResponse.success && backendResponse.items) {
        return {
          items: backendResponse.items,
          pagination: backendResponse.pagination || { page: 1, per_page: 20, total: 0, pages: 0 }
        };
      } else {
        console.warn('后端返回无效数据:', backendResponse);
        return { items: [], pagination: { page: 1, per_page: 20, total: 0, pages: 0 } };
      }
    } catch (error) {
      console.error('搜索菜品失败:', error);
      if (error instanceof Error && error.name === 'AbortError') {
        console.error('搜索请求超时');
      }
      return { items: [], pagination: { page: 1, per_page: 20, total: 0, pages: 0 } };
    }
  },

  // 获取所有地区信息
  getRegions: async (): Promise<Array<{province: string, city: string}>> => {
    try {
      const response = await apiClient.get<{success: boolean, data: Array<{province: string, city: string}>}>('/api/regions');
      return response.data?.data || [];
    } catch (error) {
      console.error('获取地区信息失败:', error);
      return [];
    }
  },

  // 获取数据统计信息
  getStatistics: async (): Promise<any> => {
    try {
      const response = await apiClient.get<{success: boolean, data: any}>('/api/statistics');
      return response.data?.data || {};
    } catch (error) {
      console.error('获取统计信息失败:', error);
      return {};
    }
  },

  // 获取随机推荐菜品
  getRandomDishes: async (params?: {limit?: number, province?: string}): Promise<Dish[]> => {
    try {
      const searchParams = new URLSearchParams();
      if (params?.limit) {
        searchParams.append('limit', params.limit.toString());
      }
      if (params?.province) {
        searchParams.append('province', params.province);
      }
      
      const response = await apiClient.get<{success: boolean, data: Dish[]}>(`/api/dishes/random?${searchParams.toString()}`);
      return response.data?.data || [];
    } catch (error) {
      console.error('获取随机推荐失败:', error);
      return [];
    }
  },

  // 获取菜系分类
  getCategories: async () => {
    const response = await apiClient.get('/api/dishes/categories');
    return response.data || [];
  },

  // 获取菜品评价
  getDishReviews: async (dishId: number, page: number = 1, perPage: number = 10): Promise<PaginatedResponse<Review>> => {
    try {
      const response = await apiClient.get<PaginatedResponse<Review>>(`/api/dishes/${dishId}/reviews?page=${page}&per_page=${perPage}`);
      return response.data || { items: [], pagination: { page: 1, per_page: 10, total: 0, pages: 0 } };
    } catch (error) {
      console.error('获取菜品评价失败:', error);
      return { items: [], pagination: { page: 1, per_page: 10, total: 0, pages: 0 } };
    }
  },

  // 检查用户是否已评价过菜品
  checkUserReview: async (dishId: number, userId: number) => {
    try {
      const response = await apiClient.get(`/api/dishes/${dishId}/reviews/check?user_id=${userId}`);
      return response.data;
    } catch (error) {
      console.error('检查用户评价失败:', error);
      return { success: false, has_reviewed: false, data: null };
    }
  },

  // 获取用户对某道菜的所有评价
  getUserReviewsForDish: async (dishId: number, userId: number) => {
    try {
      const response = await apiClient.get(`/api/dishes/${dishId}/reviews/user/${userId}`);
      return response.data;
    } catch (error) {
      console.error('获取用户多次评价失败:', error);
      return { success: false, count: 0, data: [] };
    }
  },

  // 添加菜品评价
  addReview: async (dishId: number, data: {
    user_id: number;
    rating: number;
    content: string;
    title?: string;
  }) => {
    const response = await apiClient.post(`/api/dishes/${dishId}/reviews`, data);
    return response;
  },

  // 更新菜品评价
  updateReview: async (reviewId: number, data: {
    user_id: number;
    rating: number;
    content: string;
    title?: string;
  }) => {
    const response = await apiClient.put(`/api/reviews/${reviewId}`, data);
    return response.data;
  },

  // 删除菜品评价
  deleteReview: async (reviewId: number, userId: number) => {
    const response = await apiClient.delete(`/api/reviews/${reviewId}?user_id=${userId}`);
    return response.data;
  },

  // 获取同店推荐菜品
  getSameRestaurantDishes: async (dishId: number, limit: number = 6) => {
    try {
      const response = await apiClient.get(`/api/dishes/${dishId}/same-restaurant?limit=${limit}`);
      return response;
    } catch (error) {
      console.error('获取同店推荐失败:', error);
      return { success: false, data: [], total: 0 };
    }
  },
};

// 用户相关API
export const userApi = {
  // 用户注册
  register: async (data: {
    username: string;
    email: string;
    password: string;
    phone?: string;
  }) => {
    const response = await apiClient.post('/api/auth/register', data);
    if (response.success && (response as any).user && (response as any).token) {
      localStorage.setItem('auth_token', (response as any).token);
    }
    return response;
  },

  // 用户登录
  login: async (data: {
    username: string;
    password: string;
  }) => {
    const response = await apiClient.post('/api/users/login', data);
    if (response.success && response.data && (response.data as any).token) {
      apiClient.setToken((response.data as any).token);
    }
    return response.data;
  },

  // 用户登出
  logout: () => {
    apiClient.clearToken();
  },

  // 获取用户资料
  getUserProfile: async (userId: number): Promise<User | null> => {
    const response = await apiClient.get<User>(`/api/users/profile/${userId}`);
    return response.data || null;
  },

  // 更新用户资料
  updateUserProfile: async (userId: number, data: Partial<User>) => {
    const response = await apiClient.put(`/api/users/profile/${userId}`, data);
    return response.data;
  },

  // 获取用户收藏
  getUserFavorites: async (userId: number, page: number = 1, perPage: number = 20): Promise<PaginatedResponse<Dish>> => {
    const response = await apiClient.get<PaginatedResponse<Dish>>(`/api/users/favorites/${userId}?page=${page}&per_page=${perPage}`);
    return response.data || { items: [], pagination: { page: 1, per_page: 20, total: 0, pages: 0 } };
  },

  // 添加收藏
  addFavorite: async (userId: number, dishId: number) => {
    try {
      const response = await apiClient.post(`/api/users/${userId}/favorites`, { dish_id: dishId });
      return response.data;
    } catch (error) {
      console.error('添加收藏API调用失败:', error);
      throw error;
    }
  },

  // 取消收藏
  removeFavorite: async (userId: number, dishId: number) => {
    try {
      const response = await apiClient.delete(`/api/users/${userId}/favorites/${dishId}`);
      return response.data;
    } catch (error) {
      console.error('取消收藏API调用失败:', error);
      throw error;
    }
  },

  // 检查是否已收藏
  checkFavorite: async (userId: number, dishId: number): Promise<boolean> => {
    try {
      const response = await fetch(`http://localhost:5000/api/users/${userId}/favorites/${dishId}/check`);
      const data = await response.json();
      if (data.success) {
        return data.is_favorited || false;
      }
      return false;
    } catch (error) {
      console.error('检查收藏状态失败:', error);
      return false;
    }
  },

  // 获取用户订单
  getUserOrders: async (userId: number, page: number = 1, perPage: number = 20, status?: string) => {
    const params = new URLSearchParams({
      page: page.toString(),
      per_page: perPage.toString(),
    });
    if (status) params.append('status', status);

    const response = await apiClient.get(`/api/users/orders/${userId}?${params.toString()}`);
    return response.data || { items: [], pagination: { page: 1, per_page: 20, total: 0, pages: 0 } };
  },
};

// 导出API客户端
export { apiClient };

// 错误处理工具
export const handleApiError = (error: any): string => {
  if (error.message) {
    return error.message;
  }
  if (typeof error === 'string') {
    return error;
  }
  return '发生未知错误，请稍后重试';
};

// 数据缓存工具
class DataCache {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();

  set(key: string, data: any, ttl: number = 5 * 60 * 1000) { // 默认5分钟缓存
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    });
  }

  get(key: string): any | null {
    const item = this.cache.get(key);
    if (!item) return null;

    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }

    return item.data;
  }

  clear() {
    this.cache.clear();
  }

  delete(key: string) {
    this.cache.delete(key);
  }
}

export const dataCache = new DataCache();

// 餐厅相关API
export const restaurantApi = {
  // 获取餐厅详情
  getRestaurantDetail: async (restaurantName: string) => {
    try {
      const response = await apiClient.get(`/api/restaurants/${encodeURIComponent(restaurantName)}`);
      return response.data;
    } catch (error) {
      console.error('获取餐厅详情失败:', error);
      return null;
    }
  },

  // 获取餐厅列表
  getRestaurants: async (params: {
    page?: number;
    per_page?: number;
    cuisine_type?: string;
  } = {}) => {
    try {
      const searchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });

      const response = await apiClient.get(`/api/restaurants?${searchParams.toString()}`);
      return response.data || { items: [], pagination: { page: 1, per_page: 20, total: 0, pages: 0 } };
    } catch (error) {
      console.error('获取餐厅列表失败:', error);
      return { items: [], pagination: { page: 1, per_page: 20, total: 0, pages: 0 } };
    }
  }
};