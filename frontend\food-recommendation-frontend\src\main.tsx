import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'
import { StagewiseToolbar } from '@stagewise/toolbar-react'
import './globals.css'

// 渲染主应用
ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)

// 单独初始化工具栏
const toolbarConfig = {
  plugins: [], // 在这里添加您的自定义插件
};

document.addEventListener('DOMContentLoaded', () => {
  const toolbarRoot = document.createElement('div');
  toolbarRoot.id = 'stagewise-toolbar-root'; // 确保唯一的ID
  document.body.appendChild(toolbarRoot);

  ReactDOM.createRoot(toolbarRoot).render(
    <React.StrictMode>
      <StagewiseToolbar config={toolbarConfig} />
    </React.StrictMode>
  );
});