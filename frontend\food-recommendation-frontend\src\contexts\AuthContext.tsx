import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface User {
  user_id: number;
  username: string;
  email: string;
  phone?: string;
  avatar_url?: string;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  login: (username: string, password: string) => Promise<boolean>;
  register: (userData: { username: string; email: string; password: string; phone?: string }) => Promise<boolean>;
  logout: () => void;
  loading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  const isAuthenticated = user !== null;

  useEffect(() => {
    // 检查本地存储中的用户信息
    const checkAuth = async () => {
      try {
        const token = localStorage.getItem('auth_token');
        const userData = localStorage.getItem('user_data');
        
        if (token && userData) {
          const parsedUser = JSON.parse(userData);
          setUser(parsedUser);
        }
      } catch (error) {
        console.error('检查认证状态失败:', error);
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user_data');
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  const login = async (username: string, password: string): Promise<boolean> => {
    setLoading(true);
    try {
      // 基本验证
      if (!username || !password) {
        throw new Error('用户名和密码不能为空');
      }

        const response = await fetch('http://localhost:5000/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password }),
      });

      const data = await response.json();

      if (response.ok) {
        // 验证返回的数据
        if (data.success && data.user && data.token) {
          const userData = {
            user_id: data.user.user_id || data.user.id,
            username: data.user.username,
            email: data.user.email,
            phone: data.user.phone,
            avatar_url: data.user.avatar_url
          };
          
          setUser(userData);
          localStorage.setItem('auth_token', data.token);
          localStorage.setItem('user_data', JSON.stringify(userData));
          return true;
        } else {
          throw new Error(data.message || '登录失败');
        }
      } else {
        throw new Error(data.message || '用户名或密码错误');
      }
    } catch (error) {
      console.error('登录失败:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const register = async (userData: { username: string; email: string; password: string; phone?: string }): Promise<boolean> => {
    setLoading(true);
    try {
      // 基本验证
      if (!userData.username || !userData.email || !userData.password) {
        throw new Error('用户名、邮箱和密码不能为空');
      }

        const response = await fetch('http://localhost:5000/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });

      const data = await response.json();

      if (response.ok) {
        // 验证返回的数据
        if (data.success && data.user && data.token) {
          const userInfo = {
            user_id: data.user.user_id || data.user.id,
            username: data.user.username,
            email: data.user.email,
            phone: data.user.phone,
            avatar_url: data.user.avatar_url
          };
          
          setUser(userInfo);
          localStorage.setItem('auth_token', data.token);
          localStorage.setItem('user_data', JSON.stringify(userInfo));
          return true;
        } else {
          throw new Error(data.message || '注册失败');
        }
      } else {
        throw new Error(data.message || '用户名或邮箱可能已被使用');
      }
    } catch (error) {
      console.error('注册失败:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user_data');
  };

  const value: AuthContextType = {
    user,
    isAuthenticated,
    login,
    register,
    logout,
    loading
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// ProtectedRoute 组件
interface ProtectedRouteProps {
  children: ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isAuthenticated, loading } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    // 如果未认证，仍然显示内容，但可以在组件内部处理登录状态
    return <>{children}</>;
  }

  return <>{children}</>;
};
