# 数据处理使用指南

## 概述

美食推荐系统的数据处理模块负责将爬取的原始数据进行清洗、验证、预处理，并导入到数据库中。该模块确保数据的质量和一致性，为后续的推荐算法提供高质量的数据基础。

## 核心功能

- 🧹 **数据清洗**: 去除无效数据，标准化格式
- ✅ **数据验证**: 检查数据完整性和有效性
- 🔄 **数据预处理**: 为机器学习准备特征数据
- 📊 **质量监控**: 实时监控数据质量指标
- 📈 **统计分析**: 生成数据统计报告

## 模块结构

```
backend/utils/
├── data_processor.py      # 数据清洗和预处理核心模块
├── data_quality.py       # 数据质量检查和监控
└── database.py           # 数据库操作工具

backend/
├── process_data.py       # 数据处理主程序
└── init_db.py           # 数据库初始化脚本
```

## 快速开始

### 1. 环境准备

```bash
# 确保已安装依赖
pip install pandas numpy scikit-learn jieba

# 创建必要目录
mkdir -p logs data/processed data/quality_reports
```

### 2. 数据处理流程

```bash
# 1. 初始化数据库
python init_db.py

# 2. 处理爬取的数据
python process_data.py --input data/scraped/all_platforms_data_xxx.json --report

# 3. 检查数据质量
python -c "
from utils.data_quality import DataQualityMonitor
import json

# 读取数据
with open('data/scraped/all_platforms_data_xxx.json', 'r') as f:
    data = json.load(f)

# 质量检查
monitor = DataQualityMonitor()
report = monitor.monitor_data_quality(data)
print(f'数据质量分数: {report[\"overall_score\"]}')
"
```

## 数据清洗功能

### DataCleaner 类

负责清洗原始爬取数据，主要功能包括：

#### 文本清洗
- 去除HTML标签和特殊字符
- 标准化空白字符
- 保留中文、英文、数字和常用标点

#### 数值提取
- 从文本中提取价格数值
- 从文本中提取评分数值
- 验证数值范围的合理性

#### 分类标准化
- 菜系类型标准化（粤菜、桂菜、琼菜等）
- 地区名称标准化（广东、广西、海南）
- 价格区间分类（0-20、20-50、50-100、100+）

#### 使用示例

```python
from utils.data_processor import DataCleaner

# 创建清洗器
cleaner = DataCleaner()

# 清洗数据
raw_data = [
    {
        'name': '  白切鸡  ',
        'price': '¥45元',
        'rating': '4.5分',
        'cuisine_type': '粤',
        'region': '广州'
    }
]

cleaned_data = cleaner.clean_dish_data(raw_data)
print(cleaned_data[0])
# 输出: {'name': '白切鸡', 'price': 45.0, 'rating': 4.5, 'cuisine_type': '粤菜', 'region': '广东', ...}
```

## 数据预处理功能

### DataPreprocessor 类

为机器学习算法准备数据，主要功能包括：

#### 特征工程
- 数值特征标准化
- 分类特征编码
- 文本特征向量化（TF-IDF）

#### 数据转换
- 创建用户-菜品交互矩阵
- 生成推荐算法所需的特征矩阵

#### 使用示例

```python
from utils.data_processor import DataPreprocessor
import pandas as pd

# 创建预处理器
preprocessor = DataPreprocessor()

# 准备数据
df = pd.DataFrame(cleaned_data)

# 预处理
processed_data = preprocessor.preprocess_for_ml(df)

# 获取处理后的特征
numerical_features = processed_data['numerical_features']
tfidf_features = processed_data['tfidf_features']
```

## 数据质量监控

### DataQualityChecker 类

检查数据质量的五个维度：

#### 1. 完整性 (Completeness)
- 必需字段：name, platform
- 重要字段：price, cuisine_type, region
- 可选字段：description, image_url, rating

#### 2. 有效性 (Validity)
- 价格范围：0-1000元
- 评分范围：0-5分
- 辣度等级：0-5级
- 名称长度：2-100字符

#### 3. 一致性 (Consistency)
- 菜系类型标准化
- 地区名称标准化
- 平台名称标准化

#### 4. 唯一性 (Uniqueness)
- 检查重复记录
- 菜品名称唯一性
- 组合字段唯一性

#### 5. 新鲜度 (Freshness)
- 数据爬取时间
- 数据更新频率

### 质量分数计算

```
总体质量分数 = 完整性(30%) + 有效性(25%) + 一致性(20%) + 唯一性(15%) + 新鲜度(10%)
```

### 使用示例

```python
from utils.data_quality import DataQualityMonitor

# 创建监控器
monitor = DataQualityMonitor()

# 执行质量检查
quality_report = monitor.monitor_data_quality(data)

# 查看结果
print(f"总体质量分数: {quality_report['overall_score']}")
print(f"告警数量: {len(quality_report['alerts'])}")

# 分析质量趋势
trend_analysis = monitor.compare_quality_trends(days=7)
print(f"质量趋势: {trend_analysis['trend_summary']}")
```

## 数据导入功能

### DataImporter 类

将清洗后的数据导入数据库：

#### 批量导入
- 支持大数据量的批量处理
- 自动处理重复数据
- 事务安全保证

#### 数据更新
- 智能更新现有记录
- 保留历史数据
- 时间戳管理

#### 使用示例

```bash
# 导入数据到数据库
python process_data.py \
  --input data/scraped/meituan_guangzhou_dishes_xxx.json \
  --batch-size 50 \
  --report \
  --log-level INFO
```

## 命令行工具

### process_data.py 参数

| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|------|
| `--input` | 输入数据文件路径 | 必需 | `--input data/scraped/data.json` |
| `--batch-size` | 批处理大小 | 100 | `--batch-size 50` |
| `--report` | 生成数据报告 | False | `--report` |
| `--log-level` | 日志级别 | INFO | `--log-level DEBUG` |

### 输出文件

#### 处理日志
- `logs/data_process_YYYYMMDD.log` - 详细处理日志

#### 数据报告
- `data/processed/data_report_YYYYMMDD_HHMMSS.json` - 数据统计报告

#### 质量报告
- `data/quality_reports/quality_report_YYYYMMDD_HHMMSS.json` - 质量检查报告

## 数据统计报告

### 报告内容

```json
{
  "generated_at": "2024-01-01T12:00:00",
  "total_dishes": 1000,
  "basic_stats": {
    "avg_price": 45.5,
    "avg_rating": 4.2,
    "total_reviews": 50000,
    "total_orders": 100000
  },
  "cuisine_stats": {
    "粤菜": {"name": 300, "price": 52.3, "rating": 4.3},
    "桂菜": {"name": 250, "price": 28.5, "rating": 4.1},
    "琼菜": {"name": 200, "price": 38.2, "rating": 4.2}
  },
  "region_stats": {
    "广东": {"name": 400, "price": 48.5, "rating": 4.3},
    "广西": {"name": 350, "price": 32.1, "rating": 4.1},
    "海南": {"name": 250, "price": 42.8, "rating": 4.2}
  },
  "quality_metrics": {
    "completeness": {
      "has_price": 95.5,
      "has_rating": 88.2,
      "has_cuisine_type": 92.1
    }
  }
}
```

## 最佳实践

### 1. 数据处理流程

```bash
# 完整的数据处理流程
# 1. 爬取数据
python scrape_data.py --platform all --limit 100

# 2. 质量检查
python -c "
from utils.data_quality import DataQualityMonitor
import json
with open('data/scraped/latest_data.json', 'r') as f:
    data = json.load(f)
monitor = DataQualityMonitor()
report = monitor.monitor_data_quality(data)
"

# 3. 数据处理和导入
python process_data.py --input data/scraped/latest_data.json --report

# 4. 验证导入结果
python -c "
from models import Dish
from app import create_app, db
app = create_app()
with app.app_context():
    count = Dish.query.count()
    print(f'数据库中共有 {count} 道菜品')
"
```

### 2. 质量监控

```python
# 设置质量监控定时任务
import schedule
import time
from utils.data_quality import DataQualityMonitor

def quality_check():
    # 读取最新数据
    with open('data/scraped/latest_data.json', 'r') as f:
        data = json.load(f)
    
    # 执行质量检查
    monitor = DataQualityMonitor()
    report = monitor.monitor_data_quality(data)
    
    # 如果质量分数过低，发送告警
    if report['overall_score'] < 80:
        print(f"⚠️ 数据质量告警: {report['overall_score']}")

# 每小时执行一次质量检查
schedule.every().hour.do(quality_check)

while True:
    schedule.run_pending()
    time.sleep(60)
```

### 3. 数据清洗规则定制

```python
# 自定义清洗规则
class CustomDataCleaner(DataCleaner):
    def _standardize_cuisine_type(self, cuisine_type: str) -> str:
        # 添加自定义菜系映射
        custom_mapping = {
            '港式茶餐厅': '粤菜',
            '新疆菜': '西北菜',
            '东北菜': '东北菜'
        }
        
        # 先使用父类方法
        result = super()._standardize_cuisine_type(cuisine_type)
        
        # 再应用自定义映射
        for key, value in custom_mapping.items():
            if key in cuisine_type:
                return value
        
        return result
```

## 故障排除

### 常见问题

1. **内存不足**
   ```bash
   # 减少批处理大小
   python process_data.py --input data.json --batch-size 20
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库配置
   python -c "
   from utils.database import db_manager
   from app import create_app
   app = create_app()
   with app.app_context():
       print('连接状态:', db_manager.check_connection())
   "
   ```

3. **数据格式错误**
   ```bash
   # 启用详细日志
   python process_data.py --input data.json --log-level DEBUG
   ```

### 性能优化

1. **大数据量处理**
   - 增加批处理大小
   - 使用多进程处理
   - 分批次处理数据

2. **内存优化**
   - 及时释放不用的数据
   - 使用生成器处理大文件
   - 分块读取数据

3. **数据库优化**
   - 使用批量插入
   - 创建适当的索引
   - 优化查询语句

## 监控和告警

### 质量指标监控

- 完整性 < 90% → 警告
- 有效性 < 85% → 警告  
- 一致性 < 80% → 警告
- 总体质量 < 75% → 错误

### 处理性能监控

- 处理速度 < 100条/分钟 → 警告
- 内存使用 > 80% → 警告
- 错误率 > 5% → 错误

---

如有问题，请查看详细日志或联系开发团队。