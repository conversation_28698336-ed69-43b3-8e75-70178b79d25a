import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider } from '@/components/theme-provider';
import { Toaster } from '@/components/ui/toaster';

// 导入页面组件
import HomePage from '@/pages/HomePage';
import SearchPage from '@/pages/SearchPage';
import DishDetailPage from '@/pages/DishDetailPage';
import RestaurantDetailPage from '@/pages/RestaurantDetailPage';
import UserProfilePage from '@/pages/UserProfilePage';

// 导入认证上下文
import { AuthProvider, ProtectedRoute } from '@/contexts/AuthContext';

function App() {
  return (
    <ThemeProvider defaultTheme="light" storageKey="food-recommendation-theme">
      <AuthProvider>
        <Router>
          <div className="App">
            <Routes>
              {/* 主页 */}
              <Route path="/" element={<HomePage />} />
              
              {/* 搜索页面 */}
              <Route path="/search" element={<SearchPage />} />
              
              {/* 菜品详情页面 */}
              <Route path="/dish/:dishId" element={<DishDetailPage />} />
              
              {/* 餐厅详情页面 */}
              <Route path="/restaurant/:restaurantName" element={<RestaurantDetailPage />} />
              
              {/* 用户个人中心 - 需要登录 */}
              <Route 
                path="/profile" 
                element={
                  <ProtectedRoute>
                    <UserProfilePage />
                  </ProtectedRoute>
                } 
              />
              
              {/* 重定向到首页 */}
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
            
            {/* 全局提示组件 */}
            <Toaster />
          </div>
        </Router>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;
