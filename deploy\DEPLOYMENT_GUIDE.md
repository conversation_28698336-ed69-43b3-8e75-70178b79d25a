# 美食推荐系统部署指南

## 📋 目录
- [系统要求](#系统要求)
- [快速部署](#快速部署)
- [详细部署步骤](#详细部署步骤)
- [配置说明](#配置说明)
- [服务管理](#服务管理)
- [监控和维护](#监控和维护)
- [故障排除](#故障排除)
- [性能优化](#性能优化)

## 🔧 系统要求

### 硬件要求
- **CPU**: 2核心以上
- **内存**: 4GB以上（推荐8GB）
- **存储**: 20GB以上可用空间
- **网络**: 稳定的互联网连接

### 软件要求
- **操作系统**: Linux/Windows/macOS
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **Git**: 2.0+

### 端口要求
- `3306`: MySQL数据库
- `6379`: Redis缓存
- `5000`: 后端API服务
- `80`: 前端Web服务
- `8080`: Nginx代理服务

## 🚀 快速部署

### 1. 克隆项目
```bash
git clone <repository-url>
cd food-recommendation-system
```

### 2. 使用Docker Compose部署
```bash
cd deploy
docker-compose up -d
```

### 3. 访问系统
- **前端界面**: http://localhost:80
- **API文档**: http://localhost:5000/api/docs
- **系统监控**: http://localhost:8080

## 📝 详细部署步骤

### 步骤1: 环境准备

#### 安装Docker
```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# CentOS/RHEL
sudo yum install -y docker
sudo systemctl start docker
sudo systemctl enable docker
```

#### 安装Docker Compose
```bash
sudo curl -L "https://github.com/docker/compose/releases/download/v2.12.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 步骤2: 项目配置

#### 创建环境配置文件
```bash
cd deploy
cp .env.example .env
```

#### 编辑配置文件
```bash
# 数据库配置
MYSQL_ROOT_PASSWORD=123456789
MYSQL_DATABASE=food_recommendation
MYSQL_USER=food_user
MYSQL_PASSWORD=123456789

# 应用配置
API_SECRET_KEY=your_api_secret_key
JWT_SECRET_KEY=your_jwt_secret_key

# 外部服务配置
SMTP_SERVER=smtp.gmail.com
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_email_password
```

### 步骤3: 构建和启动服务

#### 构建Docker镜像
```bash
docker-compose build
```

#### 启动所有服务
```bash
docker-compose up -d
```

#### 检查服务状态
```bash
docker-compose ps
```

### 步骤4: 数据库初始化

#### 等待MySQL启动
```bash
docker-compose logs mysql
```

#### 初始化数据库
```bash
docker-compose exec mysql mysql -u root -p food_recommendation < ../database/init_db.sql
```

### 步骤5: 验证部署

#### 健康检查
```bash
# 检查后端API
curl http://localhost:5000/health

# 检查前端服务
curl http://localhost:80

# 检查Nginx代理
curl http://localhost:8080
```

## ⚙️ 配置说明

### Docker Compose配置

#### 服务组件
- **mysql**: MySQL 8.0数据库
- **redis**: Redis 7缓存服务
- **backend**: Python Flask API服务
- **frontend**: React前端应用
- **nginx**: Nginx反向代理

#### 网络配置
- 所有服务运行在`food_network`网络中
- 服务间通过服务名进行通信

#### 数据持久化
- MySQL数据: `mysql_data`卷
- Redis数据: `redis_data`卷
- 应用数据: 主机目录挂载

### 环境变量配置

#### 数据库配置
```env
MYSQL_ROOT_PASSWORD=root123456
MYSQL_DATABASE=food_recommendation
MYSQL_USER=food_user
MYSQL_PASSWORD=food_password
```

#### 应用配置
```env
FLASK_ENV=production
API_SECRET_KEY=your-secret-key
JWT_SECRET_KEY=your-jwt-secret
LOG_LEVEL=INFO
```

#### 外部服务配置
```env
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
```

## 🔄 服务管理

### 启动服务
```bash
# 启动所有服务
docker-compose up -d

# 启动特定服务
docker-compose up -d mysql redis
```

### 停止服务
```bash
# 停止所有服务
docker-compose down

# 停止特定服务
docker-compose stop backend
```

### 重启服务
```bash
# 重启所有服务
docker-compose restart

# 重启特定服务
docker-compose restart backend
```

### 查看日志
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f backend
```

### 进入容器
```bash
# 进入后端容器
docker-compose exec backend bash

# 进入数据库容器
docker-compose exec mysql mysql -u root -p
```

## 📊 监控和维护

### 系统监控

#### 服务状态监控
```bash
# 查看容器状态
docker-compose ps

# 查看资源使用
docker stats

# 查看系统资源
htop
```

#### 应用监控
```bash
# API健康检查
curl http://localhost:5000/health

# 数据库连接检查
docker-compose exec mysql mysqladmin ping -u root -p
```

### 日志管理

#### 日志轮转配置
```yaml
# docker-compose.yml中添加
logging:
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"
```

#### 日志收集
```bash
# 收集所有日志
docker-compose logs --since 24h > system.log

# 按服务收集日志
docker-compose logs backend > backend.log
```

### 数据备份

#### 数据库备份
```bash
# 创建备份
docker-compose exec mysql mysqldump -u root -p food_recommendation > backup_$(date +%Y%m%d).sql

# 恢复备份
docker-compose exec mysql mysql -u root -p food_recommendation < backup_20241225.sql
```

#### 文件备份
```bash
# 备份应用数据
docker cp $(docker-compose ps -q backend):/app/data ./backup/data_$(date +%Y%m%d)

# 备份配置文件
tar -czf config_backup_$(date +%Y%m%d).tar.gz .env docker-compose.yml
```

## 🔧 故障排除

### 常见问题

#### 1. 服务启动失败
```bash
# 检查日志
docker-compose logs [service_name]

# 检查端口占用
netstat -tulpn | grep [port]

# 重新构建镜像
docker-compose build --no-cache [service_name]
```

#### 2. 数据库连接失败
```bash
# 检查MySQL状态
docker-compose exec mysql mysqladmin ping -u root -p

# 检查网络连接
docker-compose exec backend ping mysql

# 重置数据库密码
docker-compose exec mysql mysql -u root -p -e "ALTER USER 'root'@'%' IDENTIFIED BY 'new_password';"
```

#### 3. 前端页面无法访问
```bash
# 检查Nginx配置
docker-compose exec nginx nginx -t

# 重新加载Nginx配置
docker-compose exec nginx nginx -s reload

# 检查前端构建
docker-compose logs frontend
```

#### 4. API响应慢
```bash
# 检查后端日志
docker-compose logs backend

# 检查数据库性能
docker-compose exec mysql mysqladmin processlist -u root -p

# 检查系统资源
docker stats
```

### 性能调优

#### 数据库优化
```sql
-- 添加索引
CREATE INDEX idx_dish_cuisine ON dishes(cuisine_type);
CREATE INDEX idx_review_dish ON reviews(dish_id);

-- 优化查询
EXPLAIN SELECT * FROM dishes WHERE cuisine_type = '粤菜';
```

#### 缓存优化
```bash
# Redis内存使用
docker-compose exec redis redis-cli info memory

# 清理缓存
docker-compose exec redis redis-cli flushall
```

#### 应用优化
```python
# 启用数据库连接池
SQLALCHEMY_POOL_SIZE = 10
SQLALCHEMY_POOL_RECYCLE = 3600

# 启用查询缓存
CACHE_TYPE = "redis"
CACHE_REDIS_URL = "redis://redis:6379/0"
```

## 🚀 性能优化

### 系统级优化

#### 1. 资源限制
```yaml
# docker-compose.yml
services:
  backend:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G
```

#### 2. 网络优化
```yaml
# 启用HTTP/2
nginx:
  volumes:
    - ./nginx-http2.conf:/etc/nginx/nginx.conf
```

#### 3. 存储优化
```yaml
# 使用SSD存储
volumes:
  mysql_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /ssd/mysql_data
```

### 应用级优化

#### 1. 数据库优化
- 添加适当的索引
- 优化查询语句
- 启用查询缓存
- 配置连接池

#### 2. 缓存策略
- Redis缓存热点数据
- 浏览器缓存静态资源
- CDN加速图片资源

#### 3. 代码优化
- 异步处理耗时操作
- 分页查询大数据集
- 压缩API响应数据

## 📈 扩展部署

### 水平扩展

#### 1. 负载均衡
```yaml
# 多个后端实例
backend:
  deploy:
    replicas: 3
```

#### 2. 数据库集群
```yaml
# MySQL主从复制
mysql-master:
  image: mysql:8.0
mysql-slave:
  image: mysql:8.0
```

#### 3. 缓存集群
```yaml
# Redis集群
redis-cluster:
  image: redis:7-alpine
  command: redis-server --cluster-enabled yes
```

### 云部署

#### 1. Docker Swarm
```bash
# 初始化Swarm
docker swarm init

# 部署Stack
docker stack deploy -c docker-compose.yml food-system
```

#### 2. Kubernetes
```yaml
# k8s部署文件
apiVersion: apps/v1
kind: Deployment
metadata:
  name: food-recommendation-backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: backend
  template:
    metadata:
      labels:
        app: backend
    spec:
      containers:
      - name: backend
        image: food-recommendation-backend:latest
        ports:
        - containerPort: 5000
```

## 🔒 安全配置

### 1. SSL/TLS配置
```nginx
server {
    listen 443 ssl http2;
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
}
```

### 2. 防火墙配置
```bash
# 只开放必要端口
ufw allow 80/tcp
ufw allow 443/tcp
ufw deny 3306/tcp
```

### 3. 访问控制
```nginx
# IP白名单
location /admin {
    allow ***********/24;
    deny all;
}
```

## 📞 技术支持

如果在部署过程中遇到问题，请：

1. 查看相关日志文件
2. 检查系统资源使用情况
3. 参考故障排除章节
4. 联系技术支持团队

---

**部署完成后，请访问 http://localhost:8080 查看系统运行状态！** 🎉