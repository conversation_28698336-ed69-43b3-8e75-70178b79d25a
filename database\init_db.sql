-- 美食推荐系统数据库初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS food_recommendation_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS food_recommendation CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用开发数据库
USE food_recommendation_dev;

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(80) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(120) NOT NULL UNIQUE COMMENT '邮箱',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    phone VARCHAR(20) COMMENT '手机号',
    avatar VARCHAR(255) COMMENT '头像URL',
    preferred_cuisine VARCHAR(100) COMMENT '偏好菜系',
    price_range VARCHAR(20) COMMENT '价格偏好范围',
    taste_preference TEXT COMMENT '口味偏好JSON',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    last_login DATETIME COMMENT '最后登录时间',
    INDEX idx_username (username),
    INDEX idx_email (email)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 创建菜品表
CREATE TABLE IF NOT EXISTS dishes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '菜品名称',
    description TEXT COMMENT '菜品描述',
    cuisine_type VARCHAR(50) COMMENT '菜系类型',
    region VARCHAR(50) COMMENT '地区',
    price DECIMAL(10,2) COMMENT '价格',
    price_range VARCHAR(20) COMMENT '价格区间',
    image_url VARCHAR(255) COMMENT '主图片URL',
    images TEXT COMMENT '多张图片JSON数组',
    calories INT COMMENT '卡路里',
    ingredients TEXT COMMENT '主要食材',
    cooking_method VARCHAR(50) COMMENT '烹饪方法',
    taste_tags TEXT COMMENT '口味标签JSON',
    spicy_level INT COMMENT '辣度等级(0-5)',
    rating FLOAT DEFAULT 0.0 COMMENT '平均评分',
    review_count INT DEFAULT 0 COMMENT '评价数量',
    order_count INT DEFAULT 0 COMMENT '订购次数',
    source_platform VARCHAR(50) COMMENT '数据来源平台',
    source_url VARCHAR(255) COMMENT '原始链接',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否有效',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_name (name),
    INDEX idx_cuisine_type (cuisine_type),
    INDEX idx_region (region),
    INDEX idx_rating (rating),
    INDEX idx_price (price)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='菜品表';

-- 创建评价表
CREATE TABLE IF NOT EXISTS reviews (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT '用户ID',
    dish_id INT NOT NULL COMMENT '菜品ID',
    rating INT NOT NULL COMMENT '评分(1-5)',
    title VARCHAR(100) COMMENT '评价标题',
    content TEXT COMMENT '评价内容',
    taste_rating INT COMMENT '口味评分(1-5)',
    price_rating INT COMMENT '性价比评分(1-5)',
    service_rating INT COMMENT '服务评分(1-5)',
    tags TEXT COMMENT '评价标签JSON',
    is_recommended BOOLEAN DEFAULT TRUE COMMENT '是否推荐',
    is_verified BOOLEAN DEFAULT FALSE COMMENT '是否已验证',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (dish_id) REFERENCES dishes(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_dish_id (dish_id),
    INDEX idx_rating (rating)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评价表';

-- 创建订单表
CREATE TABLE IF NOT EXISTS orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT '用户ID',
    order_no VARCHAR(50) NOT NULL UNIQUE COMMENT '订单号',
    total_amount DECIMAL(10,2) COMMENT '订单总金额',
    status VARCHAR(20) DEFAULT 'pending' COMMENT '订单状态',
    order_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '下单时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_order_no (order_no),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单表';

-- 创建订单项表
CREATE TABLE IF NOT EXISTS order_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL COMMENT '订单ID',
    dish_id INT NOT NULL COMMENT '菜品ID',
    quantity INT DEFAULT 1 COMMENT '数量',
    price DECIMAL(10,2) COMMENT '单价',
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (dish_id) REFERENCES dishes(id) ON DELETE CASCADE,
    INDEX idx_order_id (order_id),
    INDEX idx_dish_id (dish_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单项表';

-- 插入测试数据
INSERT INTO users (username, email, password_hash, preferred_cuisine, price_range) VALUES
('admin', '<EMAIL>', 'pbkdf2:sha256:260000$test$hash', '粤菜', '50-100'),
('testuser', '<EMAIL>', 'pbkdf2:sha256:260000$test$hash', '川菜', '30-80');

INSERT INTO dishes (name, description, cuisine_type, region, price, image_url, ingredients, cooking_method, rating) VALUES
('白切鸡', '经典粤菜，鸡肉鲜嫩，蘸料丰富', '粤菜', '广东', 45.00, '/images/baiqieji.jpg', '土鸡,生抽,香葱', '白切', 4.5),
('桂林米粉', '广西特色小吃，汤鲜味美', '桂菜', '广西', 15.00, '/images/guilin_mifen.jpg', '米粉,猪骨汤,卤肉', '煮制', 4.2),
('海南鸡饭', '海南经典美食，香滑可口', '琼菜', '海南', 35.00, '/images/hainan_jifan.jpg', '白切鸡,香米,姜蓉', '蒸煮', 4.3);