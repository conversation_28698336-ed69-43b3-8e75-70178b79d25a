#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
美食数据爬取系统启动脚本
"""

import os
import sys
import subprocess
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('crawler_run.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def install_dependencies():
    """安装必要的依赖包"""
    dependencies = [
        'beautifulsoup4',
        'fake-useragent', 
        'pymysql',
        'aiohttp',
        'lxml',
        'requests',
        'flask',
        'flask-cors'
    ]
    
    logger.info("检查并安装依赖包...")
    for package in dependencies:
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install', package, '--quiet'
            ])
            logger.info(f"✓ {package} 安装完成")
        except subprocess.CalledProcessError as e:
            logger.error(f"✗ {package} 安装失败: {e}")

def setup_database():
    """设置数据库"""
    try:
        logger.info("初始化数据库结构...")
        import pymysql
        
        # 连接数据库
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='food_recommendation',
            charset='utf8mb4'
        )
        
        # 执行SQL文件
        sql_file = os.path.join(os.path.dirname(__file__), 'crawlers', 'create_food_tables.sql')
        if os.path.exists(sql_file):
            with open(sql_file, 'r', encoding='utf-8') as f:
                sql_content = f.read()
            
            cursor = connection.cursor()
            statements = sql_content.split(';')
            
            for statement in statements:
                statement = statement.strip()
                if statement:
                    try:
                        cursor.execute(statement)
                    except Exception as e:
                        if "already exists" not in str(e).lower():
                            logger.warning(f"SQL执行警告: {e}")
            
            cursor.close()
            connection.close()
            logger.info("✓ 数据库初始化完成")
        else:
            logger.error("✗ 找不到SQL文件")
            
    except Exception as e:
        logger.error(f"✗ 数据库初始化失败: {e}")

def run_basic_crawler():
    """运行基础爬虫（下厨房）"""
    try:
        logger.info("运行基础爬虫（下厨房）...")
        
        # 切换到爬虫目录
        crawler_dir = os.path.join(os.path.dirname(__file__), 'crawlers')
        os.chdir(crawler_dir)
        
        # 运行基础爬虫
        result = subprocess.run([
            sys.executable, 'food_crawler.py'
        ], capture_output=True, text=True, timeout=3600)  # 1小时超时
        
        if result.returncode == 0:
            logger.info("✓ 基础爬虫运行完成")
            logger.info(result.stdout)
        else:
            logger.error("✗ 基础爬虫运行失败")
            logger.error(result.stderr)
            
    except subprocess.TimeoutExpired:
        logger.warning("⚠ 爬虫运行超时，继续下一步")
    except Exception as e:
        logger.error(f"✗ 基础爬虫运行异常: {e}")

def run_multi_platform_crawler():
    """运行多平台爬虫"""
    try:
        logger.info("运行多平台爬虫...")
        
        # 切换到爬虫目录
        crawler_dir = os.path.join(os.path.dirname(__file__), 'crawlers')
        os.chdir(crawler_dir)
        
        # 运行多平台爬虫
        result = subprocess.run([
            sys.executable, 'multi_platform_crawler.py'
        ], capture_output=True, text=True, timeout=7200)  # 2小时超时
        
        if result.returncode == 0:
            logger.info("✓ 多平台爬虫运行完成")
            logger.info(result.stdout)
        else:
            logger.error("✗ 多平台爬虫运行失败") 
            logger.error(result.stderr)
            
    except subprocess.TimeoutExpired:
        logger.warning("⚠ 多平台爬虫运行超时，继续下一步")
    except Exception as e:
        logger.error(f"✗ 多平台爬虫运行异常: {e}")

def check_data_quality():
    """检查数据质量"""
    try:
        logger.info("检查数据质量...")
        import pymysql
        
        connection = pymysql.connect(
            host='localhost',
            user='root', 
            password='123456',
            database='food_recommendation',
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # 检查菜品数量
        cursor.execute("SELECT COUNT(*) FROM dishes_extended")
        dish_count = cursor.fetchone()[0]
        
        # 检查餐厅数量
        cursor.execute("SELECT COUNT(*) FROM restaurants")
        restaurant_count = cursor.fetchone()[0]
        
        # 检查地区覆盖
        cursor.execute("""
            SELECT rg.province, COUNT(d.dish_id) as count 
            FROM dishes_extended d 
            LEFT JOIN regions rg ON d.region_id = rg.region_id 
            WHERE rg.province IN ('广东', '广西', '海南')
            GROUP BY rg.province
        """)
        region_coverage = cursor.fetchall()
        
        # 检查平台分布
        cursor.execute("""
            SELECT source_platform, COUNT(*) as count 
            FROM dishes_extended 
            GROUP BY source_platform
        """)
        platform_distribution = cursor.fetchall()
        
        cursor.close()
        connection.close()
        
        logger.info("=" * 50)
        logger.info("数据质量报告:")
        logger.info(f"菜品总数: {dish_count}")
        logger.info(f"餐厅总数: {restaurant_count}")
        logger.info("地区覆盖:")
        for province, count in region_coverage:
            logger.info(f"  {province}: {count} 道菜品")
        logger.info("平台分布:")
        for platform, count in platform_distribution:
            logger.info(f"  {platform}: {count} 道菜品")
        logger.info("=" * 50)
        
        return dish_count > 0
        
    except Exception as e:
        logger.error(f"✗ 数据质量检查失败: {e}")
        return False

def start_enhanced_api():
    """启动增强版API服务"""
    try:
        logger.info("启动增强版API服务...")
        
        # 回到后端目录
        backend_dir = os.path.dirname(__file__)
        os.chdir(backend_dir)
        
        # 启动API服务（非阻塞）
        api_process = subprocess.Popen([
            sys.executable, 'enhanced_api.py'
        ])
        
        logger.info(f"✓ API服务已启动 (PID: {api_process.pid})")
        logger.info("API地址: http://localhost:5000")
        logger.info("健康检查: http://localhost:5000/api/health")
        
        return api_process
        
    except Exception as e:
        logger.error(f"✗ API服务启动失败: {e}")
        return None

def main():
    """主函数"""
    start_time = datetime.now()
    
    logger.info("=" * 60)
    logger.info("美食数据爬取系统启动")
    logger.info(f"开始时间: {start_time}")
    logger.info("=" * 60)
    
    try:
        # 1. 安装依赖
        install_dependencies()
        
        # 2. 初始化数据库
        setup_database()
        
        # 3. 运行基础爬虫
        run_basic_crawler()
        
        # 4. 运行多平台爬虫
        run_multi_platform_crawler()
        
        # 5. 检查数据质量
        data_ok = check_data_quality()
        
        if data_ok:
            # 6. 启动增强版API
            api_process = start_enhanced_api()
            
            if api_process:
                logger.info("=" * 60)
                logger.info("系统启动完成！")
                logger.info("前端可以通过以下API获取数据:")
                logger.info("- 搜索菜品: GET /api/dishes/search")
                logger.info("- 菜品详情: GET /api/dishes/{id}")
                logger.info("- 菜系分类: GET /api/categories")
                logger.info("- 地区信息: GET /api/regions")
                logger.info("- 统计信息: GET /api/statistics")
                logger.info("- 随机推荐: GET /api/dishes/random")
                logger.info("=" * 60)
                
                # 保持运行
                try:
                    api_process.wait()
                except KeyboardInterrupt:
                    logger.info("用户中断，正在关闭...")
                    api_process.terminate()
            else:
                logger.error("API服务启动失败")
        else:
            logger.error("数据质量检查未通过，请检查爬虫运行情况")
            
    except Exception as e:
        logger.error(f"系统启动失败: {e}")
    
    finally:
        end_time = datetime.now()
        duration = end_time - start_time
        logger.info(f"总耗时: {duration}")
        logger.info("系统关闭")

if __name__ == "__main__":
    main()